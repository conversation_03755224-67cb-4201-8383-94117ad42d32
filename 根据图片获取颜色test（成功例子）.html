<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Image Color Palette Extraction Test</title>
        <style>
            body {
                font-family: sans-serif;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .container {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 40px;
                margin-top: 20px;
            }
            .test-case {
                border: 1px solid #ccc;
                border-radius: 8px;
                padding: 15px;
                text-align: center;
            }
            .palette {
                display: flex;
                justify-content: center;
                margin-top: 10px;
                padding: 0;
                list-style-type: none;
            }
            .color-swatch {
                width: 80px;
                height: 80px;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                align-items: center;
                color: white;
                text-shadow: 1px 1px 2px black;
                font-size: 12px;
                padding-bottom: 5px;
            }
            img {
                max-width: 300px;
                height: auto;
                border-radius: 8px;
            }
        </style>
    </head>
    <body>
        <h1>图像颜色提取逆向工程</h1>
        <p>目标颜色值: <strong>#EFBC6C, #170807, #B21D1C, #F8F3F0</strong></p>

        <!-- 
      重要提示: 
      为了让Canvas API读取图片数据，需要添加 crossorigin="anonymous" 属性。
      同时，图片服务器也必须支持跨域请求 (CORS)。这里使用的Imgur链接是支持的。
    -->
        <!-- src="https://i.imgur.com/gY9k4b5.png" -->
        <img
            id="sourceImage"
            src="https://i.postimg.cc/k5Hy2ywr/walller-1.jpg"
            crossorigin="anonymous"
            alt="Crayon Shin-chan"
        />

        <div class="container">
            <!-- 测试案例 1: ColorThief.js -->
            <div class="test-case">
                <h2>ColorThief.js</h2>
                <p>基于中位切分算法，注重面积占比。</p>
                <ul id="colorthief-palette" class="palette"></ul>
            </div>

            <!-- 测试案例 2: Vibrant.js -->
            <div class="test-case">
                <h2>Vibrant.js</h2>
                <p>提取“活力”色，注重饱和度和视觉冲击力。</p>
                <ul id="vibrant-palette" class="palette"></ul>
            </div>

            <!-- 测试案例 3: image-q -->
            <div class="test-case">
                <h2>image-q (K-Means)</h2>
                <p>基于K-Means聚类算法，理论上最接近Canva。</p>
                <ul id="imageq-palette" class="palette"></ul>
            </div>
        </div>

        <!-- 引入JS库 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/color-thief/2.3.2/color-thief.umd.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/vibrant.js/1.0.0/Vibrant.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/image-q@2.1.2/dist/image-q.min.js"></script>

        <!-- 我们的主逻辑脚本 -->
        <script>
            // 确保图片完全加载后再执行脚本
            window.onload = function () {
                const img = document.getElementById('sourceImage')

                // 如果图片还未加载完成，则监听其load事件
                if (img.complete) {
                    runColorExtraction()
                } else {
                    img.addEventListener('load', runColorExtraction)
                }
            }

            function runColorExtraction() {
                const img = document.getElementById('sourceImage')

                // --- 辅助函数：将 [r, g, b] 转换为 #RRGGBB ---
                function rgbToHex(rgb) {
                    return (
                        '#' +
                        rgb
                            .map(x => {
                                const hex = x.toString(16)
                                return hex.length === 1 ? '0' + hex : hex
                            })
                            .join('')
                            .toUpperCase()
                    )
                }

                // --- 辅助函数：在页面上创建颜色样本 ---
                function createSwatch(paletteContainer, color) {
                    const swatch = document.createElement('li')
                    swatch.className = 'color-swatch'
                    swatch.style.backgroundColor = color
                    swatch.textContent = color
                    document.getElementById(paletteContainer).appendChild(swatch)
                }

                // --- 1. 使用 ColorThief.js ---
                try {
                    const colorThief = new ColorThief()
                    const thiefPalette = colorThief.getPalette(img, 4)
                    thiefPalette.forEach(rgb => {
                        createSwatch('colorthief-palette', rgbToHex(rgb))
                    })
                } catch (e) {
                    console.error('ColorThief Error:', e)
                    document.getElementById('colorthief-palette').textContent = '提取失败'
                }

                // --- 2. 使用 Vibrant.js ---
                try {
                    Vibrant.from(img).getPalette((err, palette) => {
                        if (err) {
                            console.error('Vibrant.js Error:', err)
                            document.getElementById('vibrant-palette').textContent = '提取失败'
                            return
                        }
                        // Vibrant返回的是一个对象，我们手动挑选4个
                        const vibrantColors = [
                            palette.Vibrant,
                            palette.Muted,
                            palette.DarkVibrant,
                            palette.LightVibrant,
                        ].filter(Boolean) // 过滤掉null的颜色

                        vibrantColors.forEach(swatch => {
                            createSwatch('vibrant-palette', rgbToHex(swatch.rgb))
                        })
                    })
                } catch (e) {
                    console.error('Vibrant.js Setup Error:', e)
                    document.getElementById('vibrant-palette').textContent = '提取失败'
                }

                // --- 3. 使用 image-q ---
                // image-q需要先将图片绘制到canvas上
                try {
                    const canvas = document.createElement('canvas')
                    const context = canvas.getContext('2d')
                    canvas.width = img.width
                    canvas.height = img.height
                    context.drawImage(img, 0, 0, img.width, img.height)

                    // 从Canvas获取图像数据
                    const pointContainer = imageq.utils.PointContainer.fromHTMLCanvasElement(canvas)

                    // 使用K-Means算法进行颜色量化
                    const kmeans = new imageq.quantize.KMeans()

                    // 执行量化并获取调色板
                    const resultPalette = kmeans.quantize(pointContainer, 4)

                    resultPalette.palette.forEach(colorPoint => {
                        createSwatch(
                            'imageq-palette',
                            rgbToHex([colorPoint.r, colorPoint.g, colorPoint.b]),
                        )
                    })
                } catch (e) {
                    console.error('image-q Error:', e)
                    document.getElementById('imageq-palette').textContent = '提取失败'
                }
            }
        </script>
    </body>
</html>
