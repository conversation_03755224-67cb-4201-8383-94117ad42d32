# Git 提交规范规则

## 常用提交类型规范（基于 Conventional Commits）

### 提交格式
```
<类型>[可选 范围]: <简短描述>

[可选 正文]

[可选 脚注]
```

### 主要类型
- **feat**: 新功能 (feature)
- **fix**: 修复 bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响代码运行的变动）
- **refactor**: 重构（既不是新增功能，也不是修复 bug 的代码变动）
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动
- **ci**: CI 相关配置
- **build**: 构建系统或外部依赖的变动
- **revert**: 回滚之前的提交

### 示例
```
feat: 添加用户登录功能

fix: 修复登录页面跳转错误

docs: 更新 API 文档说明

style: 格式化代码缩进

refactor: 重构用户服务逻辑

chore: 更新依赖包版本
```

### 范围说明
可选的范围说明组件/模块：
- api: API 接口
- ui: 用户界面
- auth: 认证授权
- db: 数据库相关
- config: 配置文件
- deps: 依赖管理

### 示例（带范围）
```
feat(auth): 添加 JWT 令牌验证
fix(ui): 修复导航栏样式问题
docs(api): 更新接口文档
```