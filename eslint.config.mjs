/**
 * ESLint配置文件
 * 本文件配置项目的代码规范和自动修复功能
 */

import { FlatCompat } from '@eslint/eslintrc'
import { dirname } from 'path'
import { fileURLToPath } from 'url'

// 获取当前文件的路径
const __filename = fileURLToPath(import.meta.url)
// 获取当前文件所在的目录
const __dirname = dirname(__filename)

// 创建兼容性实例，用于支持传统的ESLint配置
const compat = new FlatCompat({
    baseDirectory: __dirname,
})

// ESLint配置数组
const eslintConfig = [
    // 忽略文件配置（替代.eslintignore）
    {
        ignores: [
            // 构建输出目录
            '.next/**',
            'out/**',
            'build/**',
            'dist/**',

            // 依赖目录
            'node_modules/**',

            // 缓存目录
            '.cache/**',
            '.cursor/**',

            // 各种配置文件
            'next.config.ts',
            'next-env.d.ts',
            '*.config.js',
            '*.config.mjs',
            'postcss.config.mjs',

            // 其他文件
            '*.log',
            '.DS_Store',
        ],
    },
    // 扩展配置继承自以下预设
    ...compat.extends(
        // Next.js核心性能优化规则
        'next/core-web-vitals',
        // Next.js的TypeScript规则
        'next/typescript',
        // React推荐规则
        'plugin:react/recommended',
        // React Hooks推荐规则
        'plugin:react-hooks/recommended',
        // Prettier配置 - 必须放在最后以覆盖之前的格式化规则
        'prettier',
    ),
    {
        // 自定义规则配置
        rules: {
            // 基本格式规则
            // 强制使用4个空格缩进
            indent: ['error', 4],
            // 强制使用单引号
            quotes: ['error', 'single'],
            // 禁止使用分号
            semi: ['error', 'never'],
            // 多行时强制使用尾随逗号
            'comma-dangle': ['error', 'always-multiline'],
            // 限制console的使用，但允许warn和error
            'no-console': ['warn', { allow: ['warn', 'error'] }],

            // ===== 禁用未使用变量自动清除相关规则 =====
            // 完全禁用未使用变量检查 - 不移除未使用的变量或代码
            'no-unused-vars': 'off',
            // 禁用TypeScript未使用变量检查
            '@typescript-eslint/no-unused-vars': 'off',
            // 禁用未使用的表达式检查
            'no-unused-expressions': 'off',
            // 禁用未使用的标签检查
            'no-unused-labels': 'off',
            // 禁用重复的导入检查（可能导致自动清理导入）
            'no-duplicate-imports': 'off',
            // 禁用未使用的导入检查
            'unused-imports/no-unused-imports': 'off',
            // 如果有这个插件，也要禁用
            'import/no-unused-modules': 'off',

            // React规则
            // 确保JSX中的React被正确使用
            'react/jsx-uses-react': 'error',
            // 防止在JSX中使用的变量被标记为未使用
            'react/jsx-uses-vars': 'error',
            // 关闭prop-types检查，我们使用TypeScript
            'react/prop-types': 'off',
            // 不需要在文件顶部引入React (React 17+)
            'react/react-in-jsx-scope': 'off',

            // TypeScript规则
            // 不要求函数显式返回类型
            '@typescript-eslint/explicit-function-return-type': 'off',
            // 不要求导出函数和类的显式返回和参数类型
            '@typescript-eslint/explicit-module-boundary-types': 'off',
            // 警告使用any类型
            '@typescript-eslint/no-explicit-any': 'warn',
        },
        // 其他设置
        settings: {
            // React设置
            react: {
                // 自动检测React版本
                version: 'detect',
            },
        },
    },
]

export default eslintConfig
