/**
 * 测试格式化效果的文件
 * 该文件包含了一些故意的格式错误
 */

// 未格式化的变量声明（没有分号，使用双引号）
const test = 'hello world'

// 未格式化的函数（缩进混乱，使用双引号和分号）
function testFunction() {
    console.log('Testing formatting')
    if (test === 'hello world') {
        return true
    } else {
        return false
    }
}

// 未格式化的对象（不符合代码风格）
const obj = { name: 'test', value: 123, enabled: true }

// 未格式化的数组（没有尾随逗号）
const arr = [1, 2, 3]

// 导出以供使用
export { arr, obj, test, testFunction }
