'use client'

import React, { useRef } from 'react'
import { useCustomImageStore } from '../hooks/useCustomImageStore'

interface PublicFrameCustomImageModalProps {
    onImageSelect: (file: File) => void
    onClose: () => void
    isOpen: boolean
}

/**
 * 公共框架自定义图片模态框
 * 提供独立的图片上传功能，支持点击上传和拖拽上传
 * 使用独立的Zustand存储管理状态
 */
export default function PublicFrameCustomImageModal({
    onImageSelect,
    onClose,
    isOpen,
}: PublicFrameCustomImageModalProps) {
    const fileInputRef = useRef<HTMLInputElement>(null)
    
    // 从自定义存储中获取状态和操作
    const { image, isDragging, error, uploadImage, clearImage, setDragging } = useCustomImageStore()

    // 如果模态框未打开，返回null
    if (!isOpen) return null

    /**
     * 处理文件选择（点击上传）
     * 从文件输入框获取文件并上传到存储
     */
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>): void => {
        const file = event.target.files?.[0]
        if (file) {
            uploadImage(file)
            // 成功上传后通知父组件
            onImageSelect(file)
        }
    }

    /**
     * 处理拖拽事件
     * 阻止默认行为并提供视觉反馈
     */
    const handleDragOver = (event: React.DragEvent): void => {
        event.preventDefault()
        setDragging(true)
    }

    const handleDragEnter = (event: React.DragEvent): void => {
        event.preventDefault()
        setDragging(true)
    }

    const handleDragLeave = (event: React.DragEvent): void => {
        event.preventDefault()
        setDragging(false)
    }

    /**
     * 处理文件放置（拖拽上传）
     * 从拖拽事件中获取文件并上传到存储
     */
    const handleDrop = (event: React.DragEvent): void => {
        event.preventDefault()
        setDragging(false)

        const files = event.dataTransfer.files
        const file = files?.[0]
        
        if (file) {
            uploadImage(file)
            // 成功上传后通知父组件
            onImageSelect(file)
        }
    }

    /**
     * 处理移除图片
     * 清除存储中的图片并重置文件输入框
     */
    const handleRemoveImage = (): void => {
        clearImage()
        if (fileInputRef.current) {
            fileInputRef.current.value = ''
        }
    }

    /**
     * 触发文件选择对话框
     */
    const triggerFileInput = (): void => {
        fileInputRef.current?.click()
    }

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={(e) => {
                if (e.target === e.currentTarget) onClose()
            }}
        >
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4"
                >
                    <h2 className="text-xl font-semibold text-gray-800"
                    >
                        上传自定义图片
                    </h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* 上传区域 */}
                <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                        isDragging
                            ? 'border-blue-400 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                    } ${image ? 'border-green-400 bg-green-50' : ''}`}
                    onDragOver={handleDragOver}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={triggerFileInput}
                >
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        onChange={handleFileSelect}
                        className="hidden"
                    />

                    {image ? (
                        /* 已上传图片预览 */
                        <div className="space-y-4"
                        >
                            <img
                                src={image.previewUrl}
                                alt="预览"
                                className="max-w-full h-48 object-contain mx-auto rounded"
                            />
                            <div className="text-sm text-gray-600"
                            >
                                <p>{image.file.name}</p>
                                <p>{(image.file.size / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                            <button
                                onClick={(e) => {
                                    e.stopPropagation()
                                    handleRemoveImage()
                                }}
                                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm"
                            >
                                移除图片
                            </button>
                        </div>
                    ) : (
                        /* 未上传状态 */
                        <div className="space-y-4"
                        >
                            <svg className="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div className="text-gray-600"
                            >
                                <p className="font-medium"
                                >
                                    拖拽图片到此处或点击上传
                                </p>
                                <p className="text-sm text-gray-500 mt-2"
                                >
                                    支持 JPG、PNG 格式，最大 5MB
                                </p>
                            </div>
                        </div>
                    )}
                </div>

                {/* 错误信息 */}
                {error && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md"
                    >
                        <p className="text-sm text-red-600"
                        >{error}</p>
                    </div>
                )}

                {/* 操作按钮 */}
                <div className="flex justify-end space-x-3 mt-6"
                >
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        取消
                    </button>
                    <button
                        onClick={onClose}
                        disabled={!image}
                        className={`px-4 py-2 rounded transition-colors ${
                            image
                                ? 'bg-blue-500 text-white hover:bg-blue-600'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                    >
                        确认
                    </button>
                </div>
            </div>
        </div>
    )
}