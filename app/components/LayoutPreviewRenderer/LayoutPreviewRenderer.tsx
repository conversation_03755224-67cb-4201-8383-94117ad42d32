import React from 'react'
import DisplayContainer from '../DisplayContainer/DisplayContainer'
import { LayoutConfig, LayoutType } from '../DisplayContainer/DisplayConfig'

/**
 * @interface LayoutPreviewRendererProps
 * @description LayoutPreviewRenderer 组件的属性接口
 * @property {LayoutType} type - 当前渲染的布局类型 (single, dual, triple)
 * @property {LayoutConfig[]} configs - 该类型下的所有布局配置数组
 * @property {{type: LayoutType, id: number}} activeLayout - 全局当前激活的布局信息，用于高亮显示
 * @property {(layout: {type: LayoutType, id: number}) => void} setActiveLayout - 设置激活布局的回调函数
 * @property {number} width - 预览图的宽度
 * @property {number} height - 预览图的高度
 */
interface LayoutPreviewRendererProps {
    type: LayoutType
    configs: LayoutConfig[]
    activeLayout: { type: LayoutType; id: number }
    setActiveLayout: (layout: { type: LayoutType; id: number }) => void
    width: number
    height: number
}

/**
 * @component LayoutPreviewRenderer
 * @description 一个专门用于渲染布局预览选项的组件
 * 它接收一个布局配置数组，并为数组中的每一项渲染一个预览图
 * @param {LayoutPreviewRendererProps} props - 组件属性
 * @returns {JSX.Element} - 返回一个包含所有布局预览选项的React片段
 */
export const LayoutPreviewRenderer: React.FC<LayoutPreviewRendererProps> = ({
    type,
    configs,
    activeLayout,
    setActiveLayout,
    width,
    height,
}) => {
    return (
        <>
            {configs.map(config => {
                const layoutId = `${type}-${config.id}`
                let itemClassName = 'layout-item'
                if (activeLayout.type === type && activeLayout.id === config.id) {
                    itemClassName += ' active'
                }

                return (
                    <div
                        key={layoutId}
                        className={itemClassName}
                        style={{ width: width, height: height }}
                        onClick={() => setActiveLayout({ type: type, id: config.id })}
                    >
                        <div
                            className='frame layout-frame'
                            style={{ width: width, height: height }}
                        >
                            <div className='frame-content'>
                                <div
                                    style={{
                                        position: 'relative',
                                        overflow: 'hidden',
                                        width: width,
                                        height: height,
                                        opacity: 1,
                                    }}
                                >
                                    <div
                                        style={{
                                            width: width,
                                            height: height,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            overflow: 'hidden',
                                        }}
                                    >
                                        {/* 
                                          调用核心渲染引擎 DisplayContainer 来生成预览图
                                          关键点：在这里，我们明确地传入了 canvasWidth 和 canvasHeight
                                          这会触发 DisplayContainer 的"固定尺寸模式"，使其渲染出指定尺寸的缩略图，
                                          而不是尝试使用全局的全屏尺寸。这是组件灵活性的体现
                                        */}
                                        <DisplayContainer
                                            layoutConfig={config}
                                            canvasWidth={width}
                                            canvasHeight={height}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )
            })}
        </>
    )
}
