'use client'
import {
    EXPORT_QUALITY_PRESETS,
    ExportQualityKey,
    ImageExportFormat,
    ModalKey,
} from '@/app/features/viewDimensions/utils/重构/状态管理'
import {
    useExportSettings,
    useIsMobile,
    useModal,
    useViewDimensions,
} from '@/app/hooks/useAppState'

/**
 * 导出视图弹窗组件
 */
export const ExportPopver = () => {
    const { currentExportSettings, setExportFormat, setExportQuality } = useExportSettings()
    const viewDimensions = useViewDimensions()
    const isModalOpen = useModal(ModalKey.ExportPopover)
    const isMobile = useIsMobile()

    // 如果弹窗不存在，则返回 null
    if (!isModalOpen) {
        return null
    }

    // 弹窗内容
    const PopView = () => {
        return (
            <>
                <h5>Export settings</h5>
                {/* 图片格式 */}
                <div className='panel-control image-format '>
                    <span className='label gray-text'>Image format</span>
                    <div className='controls'>
                        <div className='panel-control-grid col-1'>
                            <div className='switch'>
                                {Object.values(ImageExportFormat).map(item => {
                                    const isActiveFormat = currentExportSettings.format === item
                                    return (
                                        <>
                                            <button
                                                onClick={() => {
                                                    setExportFormat(item)
                                                }}
                                                className={`switch-button  label-only ${
                                                    isActiveFormat ? 'is-active' : ''
                                                }`}
                                            >
                                                <p className='text-capitalize'>{item}</p>
                                                <div
                                                    className='active-indicator'
                                                    style={{
                                                        opacity: isActiveFormat ? 1 : 0,
                                                    }}
                                                />
                                            </button>
                                        </>
                                    )
                                })}
                            </div>
                        </div>
                    </div>
                </div>
                {/* 图片质量 */}
                <div className='panel-control image-quality '>
                    <span className='label gray-text'>Image quality</span>
                    <div className='controls'>
                        <div className='panel-control-grid col-1'>
                            <div className='switch'>
                                {Object.values(ExportQualityKey).map(item => {
                                    const isActiveQuality =
                                        currentExportSettings.quality.scaleFactor ===
                                        EXPORT_QUALITY_PRESETS[item].scaleFactor
                                    return (
                                        <>
                                            <button
                                                onClick={() => {
                                                    setExportQuality(EXPORT_QUALITY_PRESETS[item])
                                                }}
                                                className={`switch-button ${
                                                    isActiveQuality ? 'is-active' : ''
                                                }`}
                                            >
                                                <div className='visual'>
                                                    <div className='icon-wrapper'>
                                                        {/* 他使用svg，但是这里我使用的是文字 */}
                                                        {item === ExportQualityKey.Low && (
                                                            <svg
                                                                xmlns='http://www.w3.org/2000/svg'
                                                                viewBox='0 0 24 24'
                                                            >
                                                                <g fill='currentColor'>
                                                                    <path d='M7.706 19V7.153h-.049L4 9.676V7.619L7.686 5H9.83v14zM12 12.052 13.055 11l2.947 2.951L18.942 11l1.055 1.052-2.948 2.952 2.948 2.951L18.942 19l-2.94-2.944L13.055 19 12 17.955l2.947-2.951z' />
                                                                </g>
                                                            </svg>
                                                        )}
                                                        {item === ExportQualityKey.Medium && (
                                                            <svg
                                                                xmlns='http://www.w3.org/2000/svg'
                                                                viewBox='0 0 24 24'
                                                            >
                                                                <g fill='currentColor'>
                                                                    <path d='M2.114 19v-1.47l4.848-4.877c1.832-1.832 2.243-2.557 2.243-3.626v-.019C9.195 7.7 8.203 6.765 6.809 6.765c-1.612 0-2.767 1.088-2.786 2.481v.058H2v-.058C2 6.784 4.089 5 6.781 5c2.643 0 4.533 1.66 4.533 3.884v.019c0 1.574-.735 2.691-3.207 5.134l-3.13 3.073v.086h6.556V19zM14 12.052 15.055 11l2.947 2.951L20.942 11l1.055 1.052-2.948 2.952 2.948 2.951L20.942 19l-2.94-2.944L15.055 19 14 17.955l2.947-2.951z' />
                                                                </g>
                                                            </svg>
                                                        )}

                                                        {item === ExportQualityKey.High && (
                                                            <svg
                                                                xmlns='http://www.w3.org/2000/svg'
                                                                viewBox='0 0 24 24'
                                                            >
                                                                <g fill='currentColor'>
                                                                    <path d='M6.976 19c-2.892 0-4.779-1.578-4.967-3.784L2 15.093h2.018l.01.104c.131 1.202 1.267 2.047 2.948 2.047 1.653 0 2.77-.921 2.77-2.235v-.019c0-1.53-1.108-2.385-2.92-2.385H5.183v-1.634h1.586c1.55 0 2.601-.91 2.601-2.169v-.018c0-1.306-.882-2.066-2.413-2.066-1.502 0-2.554.789-2.685 2.047l-.01.084H2.291l.009-.094C2.507 6.492 4.309 5 6.957 5c2.695 0 4.46 1.427 4.46 3.511v.019c0 1.681-1.201 2.808-2.854 3.155v.047c1.991.178 3.324 1.38 3.324 3.277v.019c0 2.319-2.038 3.972-4.911 3.972M14 12.052 15.055 11l2.947 2.951L20.942 11l1.055 1.052-2.948 2.952 2.948 2.951L20.942 19l-2.94-2.944L15.055 19 14 17.955l2.947-2.951z'></path>
                                                                </g>
                                                            </svg>
                                                        )}
                                                        {/* 这个是直接显示文字的 */}
                                                        {/* {
                                                                            EXPORT_QUALITY_PRESETS[item]
                                                                                .scaleMultiplierLabel
                                                                        } */}
                                                    </div>
                                                </div>
                                                <p className='text-capitalize'>
                                                    {
                                                        EXPORT_QUALITY_PRESETS[item]
                                                            .resolutionTierName
                                                    }
                                                </p>
                                                <div
                                                    className='active-indicator'
                                                    style={{
                                                        opacity: isActiveQuality ? 1 : 0,
                                                    }}
                                                />
                                            </button>
                                        </>
                                    )
                                })}
                            </div>
                        </div>
                    </div>
                </div>
                {/* 输出尺寸 */}
                <div className='mockup-details'>
                    <div className='row'>
                        <span>Output Resolution</span>
                        <p>
                            {viewDimensions.useWidth * currentExportSettings.quality.scaleFactor}
                            {' x '}
                            {viewDimensions.useHeight * currentExportSettings.quality.scaleFactor}
                        </p>
                    </div>
                </div>
            </>
        )
    }

    return isMobile ? (
        <div
            className='drop-menu'
            style={{
                top: 'calc(100% + 10px)',
                bottom: 'unset',
                right: 'unset',
                left: 'unset',
                width: 240,
                filter: 'blur(0px)',
                opacity: 1,
                transform: 'none',
            }}
        >
            {PopView()}
        </div>
    ) : (
        <div
            className='popover export-settings-drop'
            style={{
                top: 60,
                right: 8,
                bottom: 'unset',
                left: 'unset',
                width: 244,
                height: 'auto',
                margin: 0,
                filter: 'blur(0px)',
                opacity: 1,
                transform: 'none',
            }}
        >
            <div className='v-stack'>
                <div className='scroll'>
                    <div className='v-stack-content' style={{ gap: 12, padding: 12 }}>
                        {/* 桌面端导出设置弹窗，但是现在暂时不用这个，而是用手机端导出设置弹窗 */}
                        {PopView()}
                    </div>
                </div>
            </div>
        </div>
    )
    // 如果弹窗存在，则渲染弹窗内容
}
