/**
 * @file app/components/DisplayContainer/DisplayConfig.ts
 * @description 该文件作为布局配置的单一数据源。
 * 它定义了所有共享的类型接口（DeviceConfig, LayoutConfig）、枚举（LayoutType），
 * 以及所有可用的设备布局配置（单设备、双设备、三设备）。
 * 其他组件（如 CanvasContainer 和 MobileMockup_Layout）将从此文件导入配置，
 * 以确保数据的一致性和可维护性。
 */

/**
 * @interface DeviceConfig
 * @description 定义单个设备在布局中的详细配置。
 * 这是最基础的配置单元。
 * @property {string} transform - 设备的 CSS transform 样式。
 * @property {string} [filter] - 作用于设备的可选 CSS filter 样式。
 * @property {number | 'unset'} [zIndex] - 设备的可选 z-index 值。
 * @property {string} [imageSource] - 设备显示屏中要展示的图片URL（可选）。
 */
export interface DeviceConfig {
    transform: string
    filter?: string
    zIndex?: number | 'unset'
    imageSource?: string
}

/**
 * @interface LayoutConfig
 * @description 定义一个完整的设备布局配置。
 * 它包含一个或多个 DeviceConfig。
 * @property {number} id - 布局的唯一标识符。
 * @property {string} name - 布局的名称。
 * @property {string} [componentTransform] - 应用于整个布局容器的 CSS transform 样式。
 * @property {string} aspectRatio - 布局的宽高比。
 * @property {DeviceConfig[]} devices - 布局中包含的设备配置数组。
 */
export interface LayoutConfig {
    id: number
    name: string
    componentTransform?: string
    aspectRatio: string
    devices: DeviceConfig[]
}

/**
 * @enum LayoutType
 * @description 定义布局的类型，用于区分单设备、双设备、三设备布局组。
 */
export enum LayoutType {
    Single = 'single',
    Dual = 'dual',
    Triple = 'triple',
}

/**
 * @const singleDeviceLayoutConfigs
 * @description 单设备布局配置数组。
 */
export const singleDeviceLayoutConfigs: LayoutConfig[] = [
    {
        id: 1,
        name: '默认视图',
        componentTransform: 'translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
                zIndex: 1,
            },
        ],
    },
    {
        id: 2,
        name: '倾斜视图',
        componentTransform: 'translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(-8deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 3,
        name: '顶部放大',
        componentTransform:
            'translate(0%, -40%) scale(1.8) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 4,
        name: '顶部进一步放大',
        componentTransform:
            'translate(0%, -66%) scale(2.5) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 5,
        name: '底部放大',
        componentTransform:
            'translate(0%, 40%) scale(1.8) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 6,
        name: '底部进一步放大',
        componentTransform:
            'translate(0%, 66%) scale(2.5) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
]

/**
 * @const dualDeviceLayoutConfigs
 * @description 用于渲染双设备预览的配置数组。
 */
export const dualDeviceLayoutConfigs: LayoutConfig[] = [
    {
        id: 1,
        name: 'Double Default',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-30%, 0%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(30%, 0%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 2,
        name: 'Double Offset',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-30%, -5%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(30%, 5%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 3,
        name: 'Double Offset 2',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-18%, -4%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(18%, 4%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 4,
        name: 'Double Rotated',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-25%, 0%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(-10deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(25%, 0%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(10deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 5,
        name: 'Double Rotated Offset',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-21%, -6%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(-10deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(21%, 6%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(10deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 6,
        name: 'Double Mixed',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-17.5%, -2.5%) scale(0.3) rotateX(0deg) rotateY(0deg) rotateZ(-10deg) skewX(0deg) skewY(0deg)',
                filter: 'blur(0.7em)',
            },
            {
                transform:
                    'perspective(200em) translate(19.8%, 0.2%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(7deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 7,
        name: 'Double Rotated 2',
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-20%, -5%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(-16deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(20%, 5%) scale(0.4) rotateX(0deg) rotateY(0deg) rotateZ(-16deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
]

/**
 * @const tripleDeviceLayoutConfigs
 * @description 用于渲染三设备预览的配置数组。
 */
export const tripleDeviceLayoutConfigs: LayoutConfig[] = [
    {
        id: 1,
        name: 'Three Default',
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-38%, 0%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(38%, 0%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 2,
        name: 'Three Offset',
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-38%, 3%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(38%, -3%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 3,
        name: 'Three Center Front',
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-26%, 1%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(-8deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(0.297) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
                zIndex: 1,
            },
            {
                transform:
                    'perspective(200em) translate(26%, 1%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(8deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 4,
        name: 'Three Mixed',
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-26%, 1.5%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(-12deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(0.297) rotateX(0deg) rotateY(0deg) rotateZ(-4deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(29%, 0%) scale(0.324) rotateX(0deg) rotateY(0deg) rotateZ(4deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 5,
        name: 'Three Mixed 2',
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-24%, 5%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(4deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(-4deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(22%, -6%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(-12deg) skewX(0deg) skewY(0deg)',
            },
        ],
    },
    {
        id: 6,
        name: 'Three Mixed Blur',
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform:
                    'perspective(200em) translate(-18%, 1%) scale(0.2) rotateX(0deg) rotateY(0deg) rotateZ(-10deg) skewX(0deg) skewY(0deg)',
                filter: 'blur(1em)',
            },
            {
                transform:
                    'perspective(200em) translate(0%, 0%) scale(0.27) rotateX(0deg) rotateY(0deg) rotateZ(6deg) skewX(0deg) skewY(0deg)',
            },
            {
                transform:
                    'perspective(200em) translate(25%, 7%) scale(0.3) rotateX(0deg) rotateY(0deg) rotateZ(-6deg) skewX(0deg) skewY(0deg)',
                filter: 'blur(0.6em)',
            },
        ],
    },
]
