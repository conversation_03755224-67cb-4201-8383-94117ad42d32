'use client'

import { animated, useSpring } from '@react-spring/web'
import { useState } from 'react'
import './VersionChecker.scss'

interface VersionCheckerProps {
    showVersionChecker: boolean
}

const VersionChecker = ({ showVersionChecker }: VersionCheckerProps) => {
    // 重新加载应用的函数
    const handleReloadApp = () => {
        console.log('reload app')
    }

    // 使用统一的动画配置，确保所有动画一致流畅
    const animConfig = {
        normal: {
            mass: 1.6, // 质量：影响动画的惯性
            tension: 190, // 张力：影响动画的速度和弹性
            friction: 22, // 摩擦力：影响动画的阻尼
            clamp: false, // 是否限制动画范围
        },
        hover: {
            mass: 1.6, // 悬停状态的质量
            tension: 180, // 悬停状态的张力，略低于普通状态，使动画更柔和
            friction: 18, // 悬停状态的摩擦力，略低于普通状态，使动画更流畅
            clamp: false, // 不限制动画范围
        },
    }

    const getSpringToggleArgent = {
        default: {
            width: '200px', // 初始宽度
            height: '40px', // 初始高度
            borderRadius: 28, // 圆角半径
            transform: 'perspective(1000px) rotateX(0deg) scale(1)', // 3D变换：透视、旋转和缩放
            transformOrigin: '50% 50% 0px', // 变换原点：中心点
        },
        hover: {
            width: '280px', // 初始宽度
            height: '120px', // 初始高度
            borderRadius: 28, // 圆角半径
            transform: 'perspective(1000px) rotateX(0deg) scale(1)', // 3D变换：透视、旋转和缩放
            transformOrigin: '50% 50% 0px', // 变换原点：中心点
        },
    }

    // 外部容器的弹簧动画状态和控制API
    const [styleSpring, api] = useSpring(() => ({
        from: getSpringToggleArgent.default,
        config: animConfig.normal, // 使用普通状态的动画配置
    }))

    // 内部模态框的弹簧动画状态和控制API
    const [modalSpring, modalApi] = useSpring(() => ({
        from: {
            opacity: 1, // 初始透明度
            filter: 'blur(0px)', // 初始模糊效果
            transform: 'perspective(1000px) rotateX(0deg) scale(1)', // 3D变换
            transformOrigin: '50% 50% 0px', // 变换原点
        },
        config: animConfig.normal, // 使用普通状态的动画配置
    }))

    // 跟踪鼠标悬停状态
    const [isHovered, setIsHovered] = useState(false)

    // 鼠标进入处理函数
    const handleMouseEnter = () => {
        setIsHovered(true) // 设置悬停状态为true

        // 开始过渡前轻微重置状态，避免硬切换
        modalApi.set({
            opacity: 0.8, // 稍微降低透明度而不是完全透明
            filter: 'blur(7px)', // 模糊过度效果，越大的话，模糊效果越明显
            transform: 'perspective(1000px) rotateX(-3deg) scale(0.96)', // 轻微变换：微微向后倾斜并缩小
        })

        // 同时启动外部容器的动画
        api.start({
            to: getSpringToggleArgent.hover,
            config: animConfig.hover, // 使用悬停状态的动画配置
        })

        // 延迟几毫秒启动内部动画，创造更流畅的视觉效果
        setTimeout(() => {
            modalApi.start({
                to: {
                    opacity: 1, // 恢复完全不透明
                    filter: 'blur(0px)', // 移除模糊效果
                    transform: 'perspective(1000px) rotateX(0deg) scale(1)', // 恢复正常变换
                    transformOrigin: '50% 50% 0px', // 保持变换原点不变
                },
                config: animConfig.hover, // 使用悬停状态的动画配置
            })
        }, 50) // 50毫秒的延迟，创造错落有致的动画效果
    }

    // 鼠标离开处理函数
    const handleMouseLeave = () => {
        setIsHovered(false) // 设置悬停状态为false

        // 启动外部容器的收缩动画
        api.start({
            to: getSpringToggleArgent.default,
            config: animConfig.normal, // 使用普通状态的动画配置
        })

        // 保持内部模态框不透明，与外部容器同步收缩
        modalApi.start({
            to: {
                opacity: 1, // 保持完全不透明
                filter: 'blur(0px)', // 保持无模糊
                transform: 'perspective(1000px) rotateX(0deg) scale(1)', // 恢复正常变换
                transformOrigin: '50% 50% 0px', // 保持变换原点不变
            },
            config: animConfig.normal, // 使用普通状态的动画配置
        })
    }

    return (
        <div className='version-checker' style={{ opacity: showVersionChecker ? '1' : '0' }}>
            {/* @ts-expect-error - 忽略TypeScript对animated.div的类型检查 */}
            <animated.div
                onMouseEnter={handleMouseEnter} // 鼠标进入事件
                onMouseLeave={handleMouseLeave} // 鼠标离开事件
                style={{ ...styleSpring }} // 应用弹簧动画样式
                className='version-checker__content' // BEM命名的CSS类
            >
                {/* @ts-expect-error - 忽略TypeScript对animated.div的类型检查 */}
                <animated.div
                    id='modal-view-id' // 模态框ID，可用于外部引用
                    className='version-checker__modal version-checker__modal--mini' // BEM命名的CSS类
                    style={{ ...modalSpring }} // 应用弹簧动画样式
                >
                    <div className='version-checker__modal-content'>
                        {' '}
                        {/* 弹性布局容器，纵向排列 */}
                        <div className='version-checker__title version-checker__title--bold'>
                            {
                                // 根据悬停状态显示不同的文本
                                isHovered ? 'New Shots Version Available' : 'Update Available'
                            }
                            {
                                // 仅在非悬停状态显示下箭头图标
                                isHovered ? (
                                    ''
                                ) : (
                                    <svg
                                        className='version-checker__icon'
                                        xmlns='http://www.w3.org/2000/svg'
                                        viewBox='0 0 24 24'
                                    >
                                        <path
                                            fill='currentColor'
                                            d='M12.058 2c-.631 0-1.087.442-1.087 1.073v13.393l.134 3.303.684-.241-4.069-4.527-1.921-1.867a1.08 1.08 0 0 0-.752-.295c-.605 0-1.047.47-1.047 1.06 0 .282.106.538.334.794l6.919 6.931c.228.241.51.376.805.376s.578-.135.806-.376l6.918-6.931c.228-.256.335-.512.335-.794 0-.59-.443-1.06-1.047-1.06-.282 0-.564.107-.752.295l-1.921 1.867-4.084 4.527.699.241.134-3.303V3.073c0-.631-.456-1.073-1.088-1.073'
                                        ></path>
                                    </svg>
                                )
                            }
                        </div>
                        <div className='version-checker__description'>
                            {
                                // 根据悬停状态显示不同的文本
                                isHovered ? 'Your changes will be Reset' : ''
                            }
                        </div>
                    </div>
                    {/* 重新加载按钮，仅在悬停时可见 */}
                    <div
                        onClick={handleReloadApp}
                        style={{
                            visibility: isHovered ? 'visible' : 'hidden', // 控制可见性
                            opacity: isHovered ? 1 : 0, // 控制透明度
                            transition: 'opacity 0.2s ease', // 平滑过渡效果
                        }}
                        className='version-checker__reload-btn'
                    >
                        Reload App
                    </div>
                </animated.div>
            </animated.div>
        </div>
    )
}

export default VersionChecker
