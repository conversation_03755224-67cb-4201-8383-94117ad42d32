.version-checker {
    position: fixed; // 固定定位
    z-index: 99; // 最高层级显示
    top: 6px; // 顶部间距
    left: 240px; // 左侧间距
    right: 240px; // 右侧间距

    display: flex; // 弹性布局
    justify-content: center; // 水平居中
    margin: 0 auto; // 居中

    &:before { // 用于遮挡背面的appbar的内容
        content: ""; // 伪元素内容
        background: rgba(var(--background),1); // 背景色
        z-index: -1; // 负层级，位于主元素后面
        height: 60px; // 高度
        position: absolute; // 绝对定位
        inset: 0; // 全屏 (top, right, bottom, left 都为 0)
    }
    // 内容容器
    .version-checker__content {
        // 【 以下是 style 直接嵌入的，可能是动画库】
        width: auto; // 自动宽度
        border-radius: 28px; // 圆角
        transform: none; // 不进行变换
        transform-origin: 50% 50% 0px; // 具体解释: 50% 50% 0px 表示元素的中心点在父元素的中心点，0px 表示元素在 z 轴上的位置
        
        
        background: linear-gradient(#00000026,#00000026),linear-gradient(140deg,#ff6432 12.8%,#ff0065 43.52%,#7b2eff 84.34%); // 渐变背景
        overflow: hidden; // 溢出隐藏
    }
    // 小弹窗
    .version-checker__modal {
        // 【 以下是 style 直接嵌入的，可能是动画库】
        // opacity: 1; // 不透明
        // filter: blur(0px); // 无模糊
        // transform: none; // 无变换
        // transform-origin: 50% 50% 0px; // 变换原点

        gap: 8px; // 元素间距
        padding: 10px 16px; // 内边距
    }
    // 小弹窗样式
    .version-checker__modal--mini { // BEM 命名规范 - 修饰符
        // outline: solid 1px rgba(var(--primary),.06); // 轮廓线
        // outline-offset: -1px; // 轮廓偏移
        border-radius: inherit; // 继承父元素的圆角
        color: #fff; // 文字颜色
        align-items: center; // 垂直居中
        display: flex; // 弹性布局
        flex-direction: column; // 垂直布局
    }
    .version-checker__modal-content {
        display: grid;
        place-items: center; // 相当于flex的 align-items: center; justify-content: center;
        gap: 4px;
    }
    // 标题
    .version-checker__title {
        display: flex;
        flex-direction: row;

        letter-spacing: -.3px; // 字间距
        font: 16px/22px Inter,sans-serif; // 字体设置
    }
    // 标题加粗
    .version-checker__title--bold { // BEM 命名规范 - 修饰符
        font-weight: 500; // 字体粗细
    }
    .version-checker__description {
        letter-spacing: -.3px; // 字间距
        font:
        12.5px / 20px Inter,
        sans-serif;
    }

    // 图标
    .version-checker__icon {
        width: 18px; // 宽度
        height: 18px; // 高度
    }
    // 刷新按钮
    .version-checker__reload-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: rgba(var(--panel-active),1);
        border-radius: 16px;
        font:
            500 17px / 24px Inter,
            sans-serif; // 字体
        color: rgba(var(--primary),1);
        cursor: pointer;
        width: 80%;
        height: 38px;
    }
}