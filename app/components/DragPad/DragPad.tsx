import React, { useState, useRef, useCallback, useEffect } from 'react'
import {
    DRAG_PAD_CONFIG,
    getDeviceSizeConfig,
    getDeviceDragSpeed,
    getButtonSize,
    type DragSpeedConfig,
} from './dragPadConfig'

/**
 * 拖拽状态枚举
 */
enum DragStatus {
    IDLE = 'idle',
    DRAGGING = 'dragging',
}

/**
 * 位置坐标接口
 */
interface Position {
    x: number
    y: number
}

/**
 * DragPad 组件属性接口
 */
interface DragPadProps {
    /** 是否为移动端 */
    isMobile: boolean
    /** 🔴 必传参数：初始位置，由调用方明确指定 */
    initialPosition: Position
    /** 位置变化回调函数 */
    onPositionChange?: (position: Position) => void
    /** 拖拽开始回调函数 */
    onDragStart?: () => void
    /** 拖拽结束回调函数 */
    onDragEnd?: () => void
    /** 🔧 自定义拖拽移动速度配置，不传则使用配置文件中的默认值 */
    dragSpeed?: DragSpeedConfig
}

/**
 * 拖拽控制组件
 *
 * 📋 功能需求实现总结
 *
 * 🟢 简单版本 (基础功能)
 *   ✅ 点击跳转：点击网格中任意按钮，圆圈立即跳转到该位置正上方覆盖
 *   ✅ 直接拖拽：按住圆圈可以自由拖拽移动 (PC端鼠标 + 📱移动端触摸)
 *   ✅ 边界限制：拖拽范围限制在容器内，减去圆圈的size
 *   ✅ 光标状态：拖拽时光标变化（grab → grabbing）
 *   ✅ 保持原有HTML/CSS结构不变
 *
 * 🔵 复杂版本 (高级功能)
 *   ✅ 响应式设计：PC端(128×120px)和移动端(152×144px)自适应尺寸
 *   ✅ 跨平台兼容：PC端鼠标事件 + 📱移动端触摸事件双重支持
 *   ✅ 事件回调系统：onPositionChange、onDragStart、onDragEnd 完整回调链
 *   ✅ 状态管理：DragStatus 枚举管理拖拽状态 (IDLE/DRAGGING)
 *   ✅ 精确计算：网格按钮中心位置自动计算，边界约束算法
 *   ✅ 全局事件：鼠标/触摸移动监听，支持拖拽到容器外释放
 *   ✅ 内存管理：事件监听器自动添加/移除，防止内存泄漏
 *   ✅ TypeScript：完整类型定义，接口约束，枚举管理
 *   ✅ 性能优化：useCallback 避免函数重创建，useRef DOM引用
 *
 * @param props 组件属性
 * @returns JSX元素
 */
export const DragPad: React.FC<DragPadProps> = ({
    isMobile,
    initialPosition,
    onPositionChange,
    onDragStart,
    onDragEnd,
    dragSpeed,
}) => {
    // 🔧 配置系统：从配置文件获取设备对应的尺寸
    const sizeConfig = getDeviceSizeConfig(isMobile)
    const WRAPPER_SIZE = sizeConfig.wrapperSize
    const CONTAINER_SIZE = sizeConfig.containerSize
    const HANDLE_SIZE = sizeConfig.handleSize
    const GRID_SIZE = DRAG_PAD_CONFIG.grid.gridSize
    const BUTTON_SIZE = getButtonSize(isMobile)

    // 🔧 配置系统：从配置文件获取移动速度，支持自定义覆盖
    const DRAG_SPEED = isMobile
        ? (dragSpeed?.mobile ?? DRAG_PAD_CONFIG.speed.mobile)
        : (dragSpeed?.pc ?? DRAG_PAD_CONFIG.speed.pc)

    // 🔵 复杂版本：状态管理系统
    const [dragStatus, setDragStatus] = useState<DragStatus>(DragStatus.IDLE) // 拖拽状态枚举管理
    const [handlePosition, setHandlePosition] = useState<Position>(initialPosition) // 手柄当前位置
    const [dragStartPosition, setDragStartPosition] = useState<Position>({ x: 0, y: 0 }) // 拖拽起始点
    const [lastMousePosition, setLastMousePosition] = useState<Position>({ x: 0, y: 0 }) // 鼠标最后位置

    // 🔵 复杂版本：DOM引用优化 - 避免重复查询DOM
    const dragPadRef = useRef<HTMLDivElement>(null) // 拖拽容器引用
    const dragHandleRef = useRef<HTMLDivElement>(null) // 拖拽手柄引用

    /**
     * 🟢 简单版本：点击跳转功能 - 计算网格按钮的中心位置
     * 🔵 复杂版本：精确位置算法 - 自动计算按钮中心并调整手柄偏移
     * @param row 行索引 (0-4)
     * @param col 列索引 (0-4)
     * @returns 按钮中心坐标，已减去手柄尺寸偏移
     */
    const calculateButtonCenterPosition = useCallback(
        (row: number, col: number): Position => {
            // 计算按钮中心点，减去手柄尺寸的一半实现居中覆盖
            const x = col * BUTTON_SIZE + BUTTON_SIZE / 2 - HANDLE_SIZE / 2
            const y = row * BUTTON_SIZE + BUTTON_SIZE / 2 - HANDLE_SIZE / 2
            return { x, y }
        },
        [BUTTON_SIZE, HANDLE_SIZE],
    )

    /**
     * 🟢 简单版本：边界限制功能 - 限制位置在容器边界内
     * 🔵 复杂版本：智能边界算法 - 精确计算可拖拽区域，防止手柄超出边界
     * @param position 原始位置
     * @returns 修正后的安全位置
     */
    const constrainPosition = useCallback(
        (position: Position): Position => {
            // 计算最大可移动范围：容器尺寸减去手柄尺寸
            const maxX = CONTAINER_SIZE - HANDLE_SIZE
            const maxY = CONTAINER_SIZE - HANDLE_SIZE

            // 使用Math.max和Math.min确保位置在[0, max]范围内
            return {
                x: Math.max(0, Math.min(maxX, position.x)),
                y: Math.max(0, Math.min(maxY, position.y)),
            }
        },
        [CONTAINER_SIZE, HANDLE_SIZE],
    )

    /**
     * 🟢 简单版本：点击跳转核心逻辑 - 处理网格按钮点击事件
     * 🔵 复杂版本：事件回调系统 - 点击后触发位置变化回调链
     * @param row 按钮行索引 (0-4)
     * @param col 按钮列索引 (0-4)
     */
    const handleGridButtonClick = useCallback(
        (row: number, col: number) => {
            // 🟢 简单版本：计算目标位置并应用边界限制
            const newPosition = calculateButtonCenterPosition(row, col)
            const constrainedPosition = constrainPosition(newPosition)
            setHandlePosition(constrainedPosition)

            // 🔵 复杂版本：触发外部位置变化回调，支持父组件监听
            if (onPositionChange) {
                onPositionChange(constrainedPosition)
            }
        },
        [calculateButtonCenterPosition, constrainPosition, onPositionChange],
    )

    /**
     * 🟢 简单版本：拖拽开始功能 - 处理鼠标按下事件
     * 🔵 复杂版本：状态管理与事件处理 - 初始化拖拽状态，记录起始位置
     * @param event React鼠标事件对象
     */
    const handleDragStart = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            // 🔵 复杂版本：DOM引用检查，确保容器存在
            if (!dragPadRef.current) {
                return
            }

            // 🟢 简单版本：获取鼠标相对于容器的位置
            const rect = dragPadRef.current.getBoundingClientRect()
            const startX = event.clientX - rect.left
            const startY = event.clientY - rect.top

            // 🔵 复杂版本：状态管理 - 设置拖拽状态和记录位置
            setDragStatus(DragStatus.DRAGGING)
            setDragStartPosition({ x: startX, y: startY })
            setLastMousePosition({ x: event.clientX, y: event.clientY })

            // 🔵 复杂版本：事件回调系统 - 通知外部拖拽开始
            if (onDragStart) {
                onDragStart()
            }

            // 🟢 简单版本：阻止默认行为，避免文本选择和页面滚动
            event.preventDefault()
            event.stopPropagation()
        },
        [onDragStart],
    )

    /**
     * 📱 移动端专用：处理触摸开始事件
     * 🔵 复杂版本：触摸事件适配 - 支持移动端拖拽功能
     * @param event React触摸事件对象
     */
    const handleTouchStart = useCallback(
        (event: React.TouchEvent<HTMLDivElement>) => {
            // 🔵 复杂版本：DOM引用检查
            if (!dragPadRef.current) {
                return
            }

            // 📱 移动端：获取第一个触摸点位置
            const touch = event.touches[0]
            if (!touch) return

            const rect = dragPadRef.current.getBoundingClientRect()
            const startX = touch.clientX - rect.left
            const startY = touch.clientY - rect.top

            // 🔵 复杂版本：状态管理 - 设置拖拽状态
            setDragStatus(DragStatus.DRAGGING)
            setDragStartPosition({ x: startX, y: startY })
            setLastMousePosition({ x: touch.clientX, y: touch.clientY })

            // 🔵 复杂版本：触发拖拽开始回调
            if (onDragStart) {
                onDragStart()
            }

            // 📱 移动端：阻止默认触摸行为，避免页面滚动
            event.preventDefault()
            event.stopPropagation()
        },
        [onDragStart],
    )

    /**
     * 🟢 简单版本：拖拽移动核心 - 处理鼠标移动事件
     * 🔵 复杂版本：全局事件监听 - 支持拖拽到容器外继续移动
     * @param event 原生鼠标事件对象
     */
    const handleDragMove = useCallback(
        (event: MouseEvent) => {
            // 🔵 复杂版本：状态检查，仅在拖拽状态下响应
            if (dragStatus !== DragStatus.DRAGGING) {
                return
            }

            // 🔵 复杂版本：DOM引用检查
            if (!dragPadRef.current) {
                return
            }

            // 🟢 简单版本：计算鼠标相对位置，减去手柄中心偏移
            const rect = dragPadRef.current.getBoundingClientRect()
            const baseX = event.clientX - rect.left - HANDLE_SIZE / 2
            const baseY = event.clientY - rect.top - HANDLE_SIZE / 2

            // 🔵 复杂版本：应用移动速度倍数，PC端更快响应
            const currentX = handlePosition.x + (baseX - handlePosition.x) * DRAG_SPEED
            const currentY = handlePosition.y + (baseY - handlePosition.y) * DRAG_SPEED

            // 🟢 简单版本：应用边界限制并更新位置
            const newPosition = constrainPosition({ x: currentX, y: currentY })
            setHandlePosition(newPosition)
            setLastMousePosition({ x: event.clientX, y: event.clientY })

            // 🔵 复杂版本：实时位置回调，支持外部组件同步状态
            if (onPositionChange) {
                onPositionChange(newPosition)
            }
        },
        [dragStatus, constrainPosition, HANDLE_SIZE, DRAG_SPEED, handlePosition, onPositionChange],
    )

    /**
     * 📱 移动端专用：处理触摸移动事件
     * 🔵 复杂版本：触摸拖拽核心 - 支持移动端滑动拖拽
     * @param event 原生触摸事件对象
     */
    const handleTouchMove = useCallback(
        (event: TouchEvent) => {
            // 🔵 复杂版本：状态检查
            if (dragStatus !== DragStatus.DRAGGING) {
                return
            }

            // 🔵 复杂版本：DOM引用检查
            if (!dragPadRef.current) {
                return
            }

            // 📱 移动端：获取第一个触摸点
            const touch = event.touches[0]
            if (!touch) return

            // 📱 移动端：计算触摸相对位置
            const rect = dragPadRef.current.getBoundingClientRect()
            const baseX = touch.clientX - rect.left - HANDLE_SIZE / 2
            const baseY = touch.clientY - rect.top - HANDLE_SIZE / 2

            // 🔵 复杂版本：应用移动速度倍数（移动端保持1.0倍精确度）
            const currentX = handlePosition.x + (baseX - handlePosition.x) * DRAG_SPEED
            const currentY = handlePosition.y + (baseY - handlePosition.y) * DRAG_SPEED

            // 🟢 简单版本：应用边界限制并更新位置
            const newPosition = constrainPosition({ x: currentX, y: currentY })
            setHandlePosition(newPosition)
            setLastMousePosition({ x: touch.clientX, y: touch.clientY })

            // 🔵 复杂版本：实时位置回调
            if (onPositionChange) {
                onPositionChange(newPosition)
            }
        },
        [dragStatus, constrainPosition, HANDLE_SIZE, DRAG_SPEED, handlePosition, onPositionChange],
    )

    /**
     * 🟢 简单版本：拖拽结束功能 - 重置拖拽状态
     * 🔵 复杂版本：状态清理与回调 - 完整的拖拽生命周期管理
     */
    const handleDragEndEvent = useCallback(() => {
        // 🔵 复杂版本：状态检查，避免重复处理
        if (dragStatus !== DragStatus.DRAGGING) {
            return
        }

        // 🟢 简单版本：重置拖拽状态到空闲状态
        setDragStatus(DragStatus.IDLE)
        setDragStartPosition({ x: 0, y: 0 })
        setLastMousePosition({ x: 0, y: 0 })

        // 🔵 复杂版本：拖拽结束回调，通知外部组件
        if (onDragEnd) {
            onDragEnd()
        }
    }, [dragStatus, onDragEnd])

    /**
     * 📱 移动端专用：处理触摸结束事件
     * 🔵 复杂版本：触摸拖拽结束 - 统一的状态清理
     * @param event React触摸事件对象
     */
    const handleTouchEnd = useCallback(
        (event: React.TouchEvent<HTMLDivElement>) => {
            // 直接调用统一的结束处理函数
            handleDragEndEvent()

            // 📱 移动端：阻止默认触摸行为
            event.preventDefault()
            event.stopPropagation()
        },
        [handleDragEndEvent],
    )

    /**
     * 🔵 复杂版本：内存管理 - 添加全局事件监听器
     * 支持拖拽到容器外继续操作，PC端和移动端双重支持
     */
    const addGlobalEventListeners = useCallback(() => {
        // PC端鼠标事件监听
        document.addEventListener('mousemove', handleDragMove) // 全局鼠标移动
        document.addEventListener('mouseup', handleDragEndEvent) // 鼠标释放
        document.addEventListener('mouseleave', handleDragEndEvent) // 鼠标离开页面

        // 📱 移动端触摸事件监听
        document.addEventListener('touchmove', handleTouchMove, { passive: false }) // 全局触摸移动
        document.addEventListener('touchend', handleDragEndEvent) // 触摸结束
        document.addEventListener('touchcancel', handleDragEndEvent) // 触摸取消
    }, [handleDragMove, handleDragEndEvent, handleTouchMove])

    /**
     * 🔵 复杂版本：内存管理 - 移除全局事件监听器
     * PC端和移动端事件完整清理，防止内存泄漏
     */
    const removeGlobalEventListeners = useCallback(() => {
        // PC端鼠标事件清理
        document.removeEventListener('mousemove', handleDragMove)
        document.removeEventListener('mouseup', handleDragEndEvent)
        document.removeEventListener('mouseleave', handleDragEndEvent)

        // 📱 移动端触摸事件清理
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('touchend', handleDragEndEvent)
        document.removeEventListener('touchcancel', handleDragEndEvent)
    }, [handleDragMove, handleDragEndEvent, handleTouchMove])

    // 🔵 复杂版本：生命周期管理 - 拖拽状态变化时自动管理事件监听器
    useEffect(() => {
        if (dragStatus === DragStatus.DRAGGING) {
            // 拖拽开始：添加全局事件监听
            addGlobalEventListeners()
        } else {
            // 拖拽结束：移除全局事件监听
            removeGlobalEventListeners()
        }

        // 组件卸载时确保清理所有事件监听器
        return () => {
            removeGlobalEventListeners()
        }
    }, [dragStatus, addGlobalEventListeners, removeGlobalEventListeners])

    // 🟢 简单版本：光标状态变化 - grab ↔ grabbing
    const handleCursor = dragStatus === DragStatus.DRAGGING ? 'grabbing' : 'grab'

    return (
        <div
            className='drag-pad-wrapper '
            style={{
                padding: 4,
                width: WRAPPER_SIZE,
                maxWidth: WRAPPER_SIZE,
                height: WRAPPER_SIZE,
                maxHeight: WRAPPER_SIZE,
            }}
        >
            {/* 🟢 简单版本：拖拽主容器 - 保持原有HTML结构 */}
            <div
                ref={dragPadRef}
                className='drag-pad'
                style={{
                    position: 'relative',
                    width: CONTAINER_SIZE,
                    maxWidth: CONTAINER_SIZE,
                    height: CONTAINER_SIZE,
                    maxHeight: CONTAINER_SIZE,
                    boxSizing: 'border-box',
                }}
            >
                {/* 🟢 简单版本：拖拽手柄 - 支持鼠标拖拽和点击 */}
                {/* 📱 移动端：触摸拖拽支持 - onTouchStart/onTouchEnd */}
                {/* 🔵 复杂版本：动态位置更新，光标状态管理 */}
                <div
                    ref={dragHandleRef}
                    className='drag-handle '
                    tabIndex={0}
                    onMouseDown={handleDragStart}
                    onTouchStart={handleTouchStart}
                    onTouchEnd={handleTouchEnd}
                    style={{
                        transform: `translateX(${handlePosition.x}px) translateY(${handlePosition.y}px)`,
                        willChange: 'transform',
                        width: HANDLE_SIZE,
                        height: HANDLE_SIZE,
                        cursor: handleCursor,
                        touchAction: 'none',
                        display: 'grid',
                        placeItems: 'center',
                    }}
                />
                {/* 🟢 简单版本：5x5网格系统 - 25个可点击按钮 */}
                <div className='drag-grid-tile flex-1' style={{ display: 'flex' }}>
                    <div className='preset-col'>
                        <div className='preset-button' onClick={() => handleGridButtonClick(0, 0)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(1, 0)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(2, 0)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(3, 0)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(4, 0)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                    </div>
                    <div className='preset-col'>
                        <div className='preset-button' onClick={() => handleGridButtonClick(0, 1)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(1, 1)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(2, 1)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(3, 1)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(4, 1)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                    </div>
                    <div className='preset-col'>
                        <div className='preset-button' onClick={() => handleGridButtonClick(0, 2)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(1, 2)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(2, 2)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(3, 2)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(4, 2)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                    </div>
                    <div className='preset-col'>
                        <div className='preset-button' onClick={() => handleGridButtonClick(0, 3)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(1, 3)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(2, 3)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(3, 3)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(4, 3)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                    </div>
                    <div className='preset-col'>
                        <div className='preset-button' onClick={() => handleGridButtonClick(0, 4)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(1, 4)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(2, 4)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(3, 4)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                        <div className='preset-button' onClick={() => handleGridButtonClick(4, 4)}>
                            <div className='tile' style={{ borderRadius: 12 }} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

/*
 * 🎯 功能实现总结:
 *
 * 📱 简单版本 (基础交互):
 *   - 点击网格按钮：圆圈立即跳转到按钮正上方
 *   - 拖拽移动：按住圆圈自由移动 (PC鼠标 + 移动端触摸)
 *   - 边界约束：移动范围限制在容器内
 *   - 光标反馈：grab/grabbing状态切换
 *
 * 🖥️ 复杂版本 (企业级功能):
 *   - 响应式适配：PC(128px)/移动端(152px)自动切换
 *   - 跨平台兼容：鼠标事件 + 触摸事件双重支持
 *   - 事件回调链：onPositionChange/onDragStart/onDragEnd
 *   - 状态管理：枚举管理拖拽状态，useCallback性能优化
 *   - 全局事件：支持拖拽到容器外继续操作
 *   - 内存安全：事件监听器自动清理，防止内存泄漏
 *   - 类型安全：完整TypeScript类型定义和接口约束
 *
 * 💡 技术亮点:
 *   - 精确位置计算：自动居中对齐，像素级精度
 *   - 事件优化：useCallback避免重渲染
 *   - DOM性能：useRef避免重复查询
 *   - 边界算法：Math.max/min确保合法位置
 *   - 生命周期：useEffect管理事件监听器
 */
