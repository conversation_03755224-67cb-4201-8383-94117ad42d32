/**
 * DragPad 组件配置文件
 * 统一管理拖拽组件的各种配置项
 */

/**
 * 移动速度配置接口
 */
export interface DragSpeedConfig {
    /** PC端移动速度倍数，默认为 1.5 */
    pc: number
    /** 移动端移动速度倍数，默认为 1.0 */
    mobile: number
}

/**
 * 设备尺寸配置接口
 */
export interface DeviceSizeConfig {
    /** 外层包装器尺寸 */
    wrapperSize: number
    /** 拖拽区域尺寸 */
    containerSize: number
    /** 拖拽手柄尺寸 */
    handleSize: number
}

/**
 * 网格系统配置接口
 */
export interface GridConfig {
    /** 网格大小 (如: 5 表示 5x5 网格) */
    gridSize: number
}

/**
 * DragPad 完整配置接口
 */
export interface DragPadConfig {
    /** 移动速度配置 */
    speed: DragSpeedConfig
    /** PC端尺寸配置 */
    pcSize: DeviceSizeConfig
    /** 移动端尺寸配置 */
    mobileSize: DeviceSizeConfig
    /** 网格系统配置 */
    grid: GridConfig
}

/**
 * 🔧 DragPad 默认配置
 *
 * 配置说明：
 * - PC端：更大尺寸，更快响应速度
 * - 移动端：适中尺寸，精确控制
 * - 网格：5x5标准布局
 */
export const DRAG_PAD_CONFIG: DragPadConfig = {
    // 🚀 移动速度配置
    speed: {
        pc: 0.15, // PC端1.5倍速度，提升响应性
        mobile: 1.0, // 移动端1.0倍速度，保持精确性
    },

    // 🖥️ PC端尺寸配置
    pcSize: {
        wrapperSize: 128, // 外层包装器尺寸
        containerSize: 120, // 拖拽区域尺寸
        handleSize: 24, // 拖拽手柄尺寸
    },

    // 📱 移动端尺寸配置
    mobileSize: {
        wrapperSize: 152, // 外层包装器尺寸
        containerSize: 144, // 拖拽区域尺寸
        handleSize: 28.8, // 拖拽手柄尺寸
    },

    // 📐 网格系统配置
    grid: {
        gridSize: 5, // 5x5网格系统
    },
}

/**
 * 🔧 获取设备对应的尺寸配置
 * @param isMobile 是否为移动端
 * @returns 对应设备的尺寸配置
 */
export const getDeviceSizeConfig = (isMobile: boolean): DeviceSizeConfig => {
    return isMobile ? DRAG_PAD_CONFIG.mobileSize : DRAG_PAD_CONFIG.pcSize
}

/**
 * 🔧 获取设备对应的移动速度
 * @param isMobile 是否为移动端
 * @returns 对应设备的移动速度倍数
 */
export const getDeviceDragSpeed = (isMobile: boolean): number => {
    return isMobile ? DRAG_PAD_CONFIG.speed.mobile : DRAG_PAD_CONFIG.speed.pc
}

/**
 * 🔧 计算按钮尺寸
 * @param isMobile 是否为移动端
 * @returns 每个网格按钮的精确尺寸
 */
export const getButtonSize = (isMobile: boolean): number => {
    const sizeConfig = getDeviceSizeConfig(isMobile)
    return sizeConfig.containerSize / DRAG_PAD_CONFIG.grid.gridSize
}
