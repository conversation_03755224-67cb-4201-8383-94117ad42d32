# DragPad 组件配置文档

## 📋 配置系统概述

DragPad 组件采用统一的配置系统管理所有参数，包括尺寸、速度、网格等配置项。

## 🔧 配置文件结构

```typescript
// dragPadConfig.ts
export const DRAG_PAD_CONFIG = {
    // 🚀 移动速度配置
    speed: {
        pc: 1.5, // PC端1.5倍速度，提升响应性
        mobile: 1.0, // 移动端1.0倍速度，保持精确性
    },

    // 🖥️ PC端尺寸配置
    pcSize: {
        wrapperSize: 128, // 外层包装器尺寸
        containerSize: 120, // 拖拽区域尺寸
        handleSize: 24, // 拖拽手柄尺寸
    },

    // 📱 移动端尺寸配置
    mobileSize: {
        wrapperSize: 152, // 外层包装器尺寸
        containerSize: 144, // 拖拽区域尺寸
        handleSize: 28.8, // 拖拽手柄尺寸
    },

    // 📐 网格系统配置
    grid: {
        gridSize: 5, // 5x5网格系统
    },
}
```

## 🚀 使用方式

### 基础使用（推荐）

```typescript
// 🔴 注意：initialPosition 是必传参数，由调用方明确指定
<DragPad
    isMobile={isMobile}
    initialPosition={{ x: 96, y: 24 }}  // 必须明确传入初始位置
    onPositionChange={handlePositionChange}
/>
```

### 自定义速度配置

```typescript
// 如需自定义速度，可传递 dragSpeed 参数
<DragPad
    isMobile={isMobile}
    initialPosition={{ x: 120, y: 60 }}  // 必须明确传入初始位置
    onPositionChange={handlePositionChange}
    dragSpeed={{
        pc: 2.0,      // 自定义PC端2倍速度
        mobile: 0.8,  // 自定义移动端0.8倍速度
    }}
/>
```

## 🔧 修改全局配置

如需修改默认配置，直接编辑 `dragPadConfig.ts` 文件：

```typescript
// 示例：修改PC端为2倍速度
export const DRAG_PAD_CONFIG = {
    speed: {
        pc: 2.0, // ✏️ 修改PC端速度
        mobile: 1.0,
    },
    // ... 其他配置
}
```

## 📊 配置项说明

### 速度配置 (speed)

- **pc**: PC端移动速度倍数，建议值：1.0-2.0
- **mobile**: 移动端移动速度倍数，建议值：0.8-1.2

### 尺寸配置 (pcSize / mobileSize)

- **wrapperSize**: 外层包装器尺寸，包含padding
- **containerSize**: 实际拖拽区域尺寸
- **handleSize**: 拖拽手柄圆圈尺寸

### 网格配置 (grid)

- **gridSize**: 网格大小，默认5表示5x5=25个按钮

### 🔴 初始位置参数 (initialPosition)

- **必传参数**: 由调用方明确指定，组件不提供默认值
- **x**: 初始X坐标 (建议范围: 0 - containerSize-handleSize)
- **y**: 初始Y坐标 (建议范围: 0 - containerSize-handleSize)
- **原因**: 避免隐式依赖，确保每个使用场景都明确指定位置

## ⚡ 工具函数

配置文件提供了便利的工具函数：

```typescript
import { getDeviceSizeConfig, getDeviceDragSpeed, getButtonSize } from './dragPadConfig'

// 获取设备对应的尺寸配置
const sizeConfig = getDeviceSizeConfig(isMobile)

// 获取设备对应的移动速度
const dragSpeed = getDeviceDragSpeed(isMobile)

// 计算按钮尺寸
const buttonSize = getButtonSize(isMobile)
```

## 🔴 重要设计变更

### 为什么 initialPosition 是必传参数？

1. **明确性**: 每个使用场景都必须明确指定初始位置，避免隐式依赖
2. **可读性**: 代码中明确看到初始位置，便于理解和维护
3. **灵活性**: 不同使用场景可能需要不同的初始位置
4. **错误预防**: 如果忘记传入会立即报TypeScript错误，避免运行时问题

```typescript
// ❌ 错误：缺少必传参数
<DragPad isMobile={isMobile} />

// ✅ 正确：明确指定初始位置
<DragPad
    isMobile={isMobile}
    initialPosition={{ x: 96, y: 24 }}
/>
```

## 🎯 最佳实践

1. **必传参数**: 始终明确传入 `initialPosition`，不依赖默认值
2. **性能优化**: PC端可适当提高速度，移动端保持精确
3. **响应式设计**: 配置已针对不同设备优化，无需额外处理
4. **统一管理**: 所有配置修改都在 `dragPadConfig.ts` 中进行
5. **位置计算**: 建议使用工具函数计算合适的初始位置
