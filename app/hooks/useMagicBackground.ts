import { useBackgroundStore, BackgroundTypeEnum } from './useBackgroundStore'
import { useMagicBackgroundStore, MagicBackgroundType } from './useMagicBackgroundStore'

/**
 * @description 魔法背景统一Hook
 * 自动协调useBackgroundStore和useMagicBackgroundStore
 * 简化魔法背景的使用
 */
export const useMagicBackground = () => {
    const { setBackgroundType } = useBackgroundStore()
    const { selectedBackground, setSelectedBackground } = useMagicBackgroundStore()

    /**
     * @description 设置魔法背景并自动切换背景类型
     * @param type - 魔法背景类型
     * @param value - 背景值
     */
    const setMagicBackground = (type: MagicBackgroundType, value: any) => {
        setBackgroundType(BackgroundTypeEnum.MAGIC)
        setSelectedBackground({ type, value })
    }

    return {
        selectedBackground,
        setMagicBackground,
    }
}
