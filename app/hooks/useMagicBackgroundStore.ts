import { create } from 'zustand'

/**
 * @description 背景类型枚举
 */
export enum MagicBackgroundType {
    SOLID = 'solid',
    GRADIENT = 'gradient',
    MESH = 'mesh',
    IMAGE = 'image',
}

/**
 * @description 纯色背景值接口
 * @interface MagicSolidBackgroundValue
 * @property {string} background - CSS background 样式字符串
 */
export interface MagicSolidBackgroundValue {
    background: string
}

/**
 * @description 渐变背景值接口
 * @interface MagicGradientBackgroundValue
 * @property {string} background - CSS background 样式字符串
 */
export interface MagicGradientBackgroundValue {
    background: string
}

/**
 * @description 网格背景值接口
 * @interface MagicMeshBackgroundValue
 * @property {string} backgroundColor - 背景颜色
 * @property {string} backgroundImage - CSS background-image 样式字符串
 */
export interface MagicMeshBackgroundValue {
    backgroundColor: string
    backgroundImage: string
}

/**
 * @description 图片背景值接口
 * @interface MagicImageBackgroundValue
 * @property {string} background - 外层容器背景颜色
 * @property {string} imageUrl - 图片URL
 */
export interface MagicImageBackgroundValue {
    background: string
    imageUrl: string
}

/**
 * @description 背景状态接口
 * @interface MagicBackgroundState
 * @property {MagicBackgroundType} type - 背景类型
 * @property {MagicSolidBackgroundValue | MagicGradientBackgroundValue | MagicMeshBackgroundValue | MagicImageBackgroundValue} value - 背景渲染所需的数据
 */
export interface MagicBackgroundState {
    type: MagicBackgroundType
    value:
        | MagicSolidBackgroundValue
        | MagicGradientBackgroundValue
        | MagicMeshBackgroundValue
        | MagicImageBackgroundValue
}

/**
 * @description 背景存储状态接口
 * @interface MagicBackgroundStore
 * @property {MagicBackgroundState | null} selectedBackground - 当前选中的背景
 * @property {(background: MagicBackgroundState) => void} setSelectedBackground - 设置背景状态
 */
interface MagicBackgroundStore {
    selectedBackground: MagicBackgroundState | null
    setSelectedBackground: (background: MagicBackgroundState) => void
}

/**
 * @description 使用 Zustand 创建的全局背景状态存储
 * @example
 * const { selectedBackground, setSelectedBackground } = useMagicBackgroundStore()
 *
 * // 设置纯色背景
 * setSelectedBackground({
 *   type: MagicBackgroundType.SOLID,
 *   value: { background: 'rgb(255, 100, 50)' }
 * })
 *
 * // 设置渐变背景
 * setSelectedBackground({
 *   type: MagicBackgroundType.GRADIENT,
 *   value: { background: 'linear-gradient(140deg, rgb(255, 100, 50) 0%, rgb(255, 0, 101) 100%)' }
 * })
 *
 * // 设置网格背景
 * setSelectedBackground({
 *   type: MagicBackgroundType.MESH,
 *   value: {
 *     backgroundColor: 'rgb(254, 254, 254)',
 *     backgroundImage: 'radial-gradient(at 40% 20%, rgb(145, 107, 93) 0px, transparent 50%)'
 *   }
 * })
 *
 * // 设置图片背景
 * setSelectedBackground({
 *   type: MagicBackgroundType.IMAGE,
 *   value: {
 *     background: 'rgb(235, 189, 93)',
 *     imageUrl: '/__壁纸测试/walller__5.jpg'
 *   }
 * })
 */
export const useMagicBackgroundStore = create<MagicBackgroundStore>(set => ({
    selectedBackground: {
        type: MagicBackgroundType.GRADIENT,
        value: {
            background:
                'linear-gradient(140deg, rgb(255, 100, 50) 12.8%, rgb(255, 0, 101) 43.52%, rgb(123, 46, 255) 84.34%)',
        },
    },
    setSelectedBackground: (background: MagicBackgroundState) => set({ selectedBackground: background }),
}))
