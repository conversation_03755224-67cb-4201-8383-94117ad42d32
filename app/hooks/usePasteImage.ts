/**
 * @fileoverview 剪贴板图片粘贴处理 Hook (V2 - Zustand Refactor)
 * @description 连接视图与 `usePasteUploadStore`，处理全局粘贴事件的绑定。
 *
 * 核心职责：
 * - 初始化并配置 `usePasteUploadStore`
 * - 监听全局 `paste` 事件，并将其委托给 Store 处理
 * - 从 Store 中订阅状态，并返回给调用组件
 * - 保持轻量，不包含任何业务逻辑
 *
 * <AUTHOR> Team (Refactored by AI)
 * @version 2.0.0
 * @since 2025.01
 */
import { useEffect } from 'react'
import { usePasteUploadStore, PasteImageOptions, PasteStatus } from './pasteUploadStore'

/**
 * @interface UsePasteImageReturn
 * @description Hook 返回给组件的响应式状态和数据。
 */
interface UsePasteImageReturn {
    /** @property {PasteStatus} status - 当前的整体状态。 */
    status: PasteStatus
    /** @property {boolean} isProcessing - 一个便捷的布尔值，表示是否正在处理或上传中。 */
    isProcessing: boolean
    /** @property {number} queueSize - 当前在上传队列中的文件数量。 */
    queueSize: number
    /** @property {string | null} error - 当前的错误信息。 */
    error: string | null
}

/**
 * @function usePasteImage
 * @description 一个用于处理剪贴板图片粘贴的React Hook。
 * 它作为视图层和 `usePasteUploadStore` 之间的桥梁。
 *
 * @param {PasteImageOptions} options - 配置选项，用于定制粘贴和上传行为。
 * @returns {UsePasteImageReturn} Hook 返回一个对象，包含组件渲染所需的状态。
 */
export const usePasteImage = (options: PasteImageOptions = {}): UsePasteImageReturn => {
    const { enabled = true } = options

    // 从 Zustand Store 中获取所需的操作 (actions)。
    const { initialize, handlePaste } = usePasteUploadStore()

    // 使用 useEffect 在组件挂载或配置更新时，初始化或更新 Store 中的配置。
    // 使用 JSON.stringify(options) 作为依赖项，可以避免因父组件重渲染导致 options 对象引用变化而引起的、不必要的重复初始化。
    // 这是一种性能优化技巧，确保只有在配置内容实际发生改变时才执行。
    useEffect(() => {
        initialize(options)
    }, [initialize, JSON.stringify(options)])

    // 使用 useEffect 来设置和清除全局的 'paste' 事件监听器。
    useEffect(() => {
        // 如果 Hook 被禁用，则不执行任何操作。
        if (!enabled) {
            return
        }

        /**
         * @function eventHandler
         * @description 处理 'paste' 事件的回调函数。
         * @param {ClipboardEvent} event - 浏览器粘贴事件对象。
         */
        const eventHandler = async (event: ClipboardEvent) => {
            // 检查活动元素，如果用户正在输入框、文本域或任何可编辑元素中操作，则忽略粘贴事件。
            // 这样可以避免覆盖常规的文本粘贴功能。
            const activeElement = document.activeElement as HTMLElement
            if (
                activeElement &&
                (activeElement.tagName === 'INPUT' ||
                    activeElement.tagName === 'TEXTAREA' ||
                    activeElement.isContentEditable)
            ) {
                return
            }

            if (event.clipboardData) {
                // 阻止浏览器的默认粘贴行为，例如在页面上直接显示图片。
                event.preventDefault()
                // 将粘贴事件的数据委托给 Zustand Store 中的 `handlePaste` action 进行处理。
                await handlePaste(event.clipboardData)
            }
        }

        // 绑定事件监听器
        document.addEventListener('paste', eventHandler)
        console.log('📋 粘贴事件监听器已绑定 (v2.0)')

        // 返回一个清理函数，该函数将在组件卸载或 `enabled` 状态改变时执行。
        return () => {
            document.removeEventListener('paste', eventHandler)
            console.log('📋 粘贴事件监听器已解除 (v2.0)')
        }
    }, [enabled, handlePaste]) // 依赖项数组确保只在 `enabled` 或 `handlePaste` action 变化时才重新绑定。

    // 从 Store 中订阅组件渲染所需的状态。
    // Zustand 会自动处理组件的重渲染，只有当这些特定状态改变时，组件才会更新。
    const status = usePasteUploadStore(state => state.status)
    const queueSize = usePasteUploadStore(state => state.queue.length)
    const error = usePasteUploadStore(state => state.error)

    // 计算派生状态，方便UI层使用。
    const isProcessing = status === PasteStatus.PROCESSING || status === PasteStatus.UPLOADING

    // 将订阅的状态返回给调用该 Hook 的组件。
    return {
        status,
        isProcessing,
        queueSize,
        error,
    }
}

// 重新导出 PasteStatus 枚举，方便其他文件直接从该模块导入，而无需关心 Store 的内部结构。
export { PasteStatus }
