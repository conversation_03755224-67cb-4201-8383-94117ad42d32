import type { Metadata } from 'next'
// import './css/globals.scss'
import './css/defaultScss/defaultMain.scss'
import { Toaster } from 'sonner'

export const metadata: Metadata = {
    title: '加加加=油💪🏻💪🏻💪🏻',
    description: '',
}

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang='zh-CN'>
            <body>
                {children}
                <Toaster position='top-right' richColors closeButton duration={4000} />
            </body>
        </html>
    )
}
