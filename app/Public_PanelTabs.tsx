import { useIsMobile } from '@/app/hooks/useAppState'

export const Public_PanelTabs = ({
    activeTab,
    updateActiveTab,
    isMobile,
}: {
    activeTab: string
    updateActiveTab: (tab: string) => void
    isMobile: boolean
}) => {
    /**
     * 处理标签点击事件
     *
     * @param {string} tab - 被点击的标签名称
     */
    const handleTabClick = (tab: string) => {
        if (activeTab !== tab) {
            updateActiveTab(tab)
        }
    }
    return (
        <div className='panel-tabs'>
            <div className='tabs'>
                <button
                    type='button'
                    className={
                        'button default-button undefined-button undefined-button undefined-blur true-round undefined-active ' +
                        (activeTab === 'mockup' ? 'is-active' : '')
                    }
                    style={{ flexDirection: 'row' }}
                    onClick={() => handleTabClick('mockup')}
                >
                    <span>Mockup</span>
                </button>
                <button
                    type='button'
                    className={
                        'button default-button undefined-button undefined-button undefined-blur true-round undefined-active ' +
                        (activeTab === 'frame' ? 'is-active' : '')
                    }
                    style={{ flexDirection: 'row' }}
                    onClick={() => handleTabClick('frame')}
                >
                    <span>Frame</span>
                </button>
            </div>
        </div>
    )
}
