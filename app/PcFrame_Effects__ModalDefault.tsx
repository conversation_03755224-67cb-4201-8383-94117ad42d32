import { useIsMobile } from './hooks/useAppState'
import { useForm } from 'react-hook-form'
import { PublicSliderComponent } from './components/Public_SliderComponet'

export const PcFrame_Effects__ModalDefault = () => {
    // 初始化react-hook-form
    const { setValue, watch } = useForm({
        defaultValues: {
            noise: 10,
            blur: 10,
        },
    })

    // 监听当前值
    const currentValue = watch('noise')
    const currentValueBlur = watch('blur')

    return (
        <div
            className='popover undefined'
            style={{
                top: '282.594px',
                right: 'unset',
                bottom: 'unset',
                left: 240,
                width: 236,
                height: 'auto',
                margin: '-40px 0px 0px',
                filter: 'blur(0px)',
                opacity: 1,
                transform: 'none',
            }}
        >
            <div className='v-stack'>
                <div className='scroll'>
                    <div className='v-stack-content' style={{ gap: 12, padding: 10 }}>
                        <div className='scene-popover-header'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/image/background-effects-header.png'
                            />
                            <span>Background effects</span>
                        </div>

                        <PublicSliderComponent
                            config='noise'
                            value={currentValue}
                            setValue={value => setValue('noise', value)}
                        />

                        <PublicSliderComponent
                            config='blur'
                            value={currentValueBlur}
                            setValue={value => setValue('blur', value)}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
