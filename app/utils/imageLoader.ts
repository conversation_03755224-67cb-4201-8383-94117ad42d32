/**
 * 图片加载工具模块
 * 
 * 功能：处理各种格式的图片加载，包括File对象、URL字符串、HTMLImageElement
 * 特点：支持跨域、缓存避免、超时处理、错误恢复
 */

/**
 * 图片加载超时时间（毫秒）
 * 例如：10000ms = 10秒，超过此时间未加载完成则触发超时错误
 */
const IMAGE_LOAD_TIMEOUT = 10000

/**
 * 为URL添加时间戳参数以避免缓存
 * 
 * @example
 * ```typescript
 * const originalUrl = "https://example.com/image.jpg"
 * const urlWithTimestamp = addTimestampToUrl(originalUrl)
 * // 结果: "https://example.com/image.jpg?t=1625097600000"
 * 
 * const cachedUrl = "https://example.com/image.jpg?version=1"
 * const newUrl = addTimestampToUrl(cachedUrl)
 * // 结果: "https://example.com/image.jpg?version=1&t=1625097600000"
 * ```
 */
const addTimestampToUrl = (url: string): string => {
    const timestamp = Date.now()
    return url.includes('?') 
        ? `${url}&t=${timestamp}` 
        : `${url}?t=${timestamp}`
}

/**
 * 从File对象加载图片
 * 
 * @example
 * ```typescript
 * const file = new File([blob], "example.jpg", { type: "image/jpeg" })
 * const image = await loadImageFromFile(file)
 * console.log(`图片尺寸: ${image.naturalWidth}x${image.naturalHeight}`)
 * ```
 */
const loadImageFromFile = (file: File): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
        const img = new Image()
        const reader = new FileReader()
        
        // 处理文件读取成功
        reader.onload = (e) => {
            img.src = e.target?.result as string
        }
        
        // 处理文件读取失败
        reader.onerror = () => reject(new Error('文件读取失败'))
        
        // 处理图片加载成功
        img.onload = () => resolve(img)
        
        // 处理图片加载失败
        img.onerror = () => reject(new Error(`图片加载失败: ${file.name}`))
        
        // 开始读取文件
        reader.readAsDataURL(file)
    })
}

/**
 * 从URL字符串加载图片
 * 
 * @example
 * ```typescript
 * const url = "https://picsum.photos/800/600"
 * const image = await loadImageFromUrl(url)
 * console.log(`加载成功: ${image.naturalWidth}x${image.naturalHeight}`)
 * ```
 */
const loadImageFromUrl = (url: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
        const img = new Image()
        
        // 设置跨域属性，但不设置blob URL的跨域属性
        if (!url.startsWith('blob:')) {
            img.crossOrigin = 'anonymous'
        }
        
        // 设置带时间戳的URL避免缓存，但不处理blob URL
        img.src = url.startsWith('blob:') ? url : addTimestampToUrl(url)
        
        // 设置超时处理
        const timeoutId = setTimeout(() => {
            reject(new Error('图片加载超时'))
        }, IMAGE_LOAD_TIMEOUT)
        
        // 成功加载时清除超时并返回图片
        img.onload = () => {
            clearTimeout(timeoutId)
            resolve(img)
        }
        
        // 加载失败时清除超时并返回错误
        img.onerror = () => {
            clearTimeout(timeoutId)
            reject(new Error(`图片加载失败: ${url}`))
        }
    })
}

/**
 * 从HTMLImageElement加载图片
 * 如果图片已经加载完成则直接返回，否则等待加载
 * 
 * @example
 * ```typescript
 * const existingImg = document.createElement('img')
 * existingImg.src = "example.jpg"
 * const image = await loadImageFromElement(existingImg)
 * ```
 */
const loadImageFromElement = (element: HTMLImageElement): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
        // 如果图片已经完全加载，直接返回
        if (element.complete && element.naturalWidth > 0) {
            resolve(element)
            return
        }
        
        // 等待图片加载完成
        element.onload = () => resolve(element)
        element.onerror = () => reject(new Error('图片加载失败'))
    })
}

/**
 * 统一的图片加载入口函数
 * 支持多种输入格式：File、URL字符串、HTMLImageElement
 * 
 * @param source - 图片源，可以是File、URL字符串或HTMLImageElement
 * @returns Promise<HTMLImageElement> - 加载完成的图片元素
 * 
 * @example
 * ```typescript
 * // 从文件加载
 * const file = new File([blob], "test.jpg")
 * const img1 = await loadImage(file)
 * 
 * // 从URL加载
 * const img2 = await loadImage("https://example.com/image.jpg")
 * 
 * // 从已有元素加载
 * const existingImg = document.querySelector('img')
 * const img3 = await loadImage(existingImg)
 * ```
 */
export const loadImage = async (
    source: File | HTMLImageElement | string,
): Promise<HTMLImageElement> => {
    try {
        if (source instanceof File) {
            return await loadImageFromFile(source)
        } else if (typeof source === 'string') {
            return await loadImageFromUrl(source)
        } else if (source instanceof HTMLImageElement) {
            return await loadImageFromElement(source)
        } else {
            throw new Error('不支持的图片类型')
        }
    } catch (error) {
        // 统一错误处理，提供更友好的错误信息
        const message = error instanceof Error ? error.message : '未知错误'
        throw new Error(`图片加载失败: ${message}`)
    }
}