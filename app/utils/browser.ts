import { useEffect } from 'react'

/**
 * 浏览器环境检测和安全访问工具
 * 解决 Next.js SSR 中的 window is not defined 问题
 */

/**
 * 检查是否在客户端环境
 * @returns {boolean} 是否在客户端
 */
export const isClient = (): boolean => {
    return typeof window !== 'undefined'
}

/**
 * 检查是否在服务器端环境
 * @returns {boolean} 是否在服务器端
 */
export const isServer = (): boolean => {
    return typeof window === 'undefined'
}

/**
 * 安全获取 window 对象
 * @returns {Window | undefined} window 对象或 undefined
 */
export const getWindow = (): Window | undefined => {
    return isClient() ? window : undefined
}

/**
 * 安全获取 document 对象
 * @returns {Document | undefined} document 对象或 undefined
 */
export const getDocument = (): Document | undefined => {
    return isClient() ? document : undefined
}

/**
 * 安全获取 navigator 对象
 * @returns {Navigator | undefined} navigator 对象或 undefined
 */
export const getNavigator = (): Navigator | undefined => {
    return isClient() ? navigator : undefined
}

/**
 * 安全获取 localStorage
 * @returns {Storage | undefined} localStorage 或 undefined
 */
export const getLocalStorage = (): Storage | undefined => {
    return isClient() ? localStorage : undefined
}

/**
 * 安全获取 sessionStorage
 * @returns {Storage | undefined} sessionStorage 或 undefined
 */
export const getSessionStorage = (): Storage | undefined => {
    return isClient() ? sessionStorage : undefined
}

/**
 * 安全执行只在客户端运行的代码
 * @param {Function} callback - 要执行的回调函数
 * @param {any} fallback - 服务器端的回退值
 * @returns {any} 客户端执行结果或回退值
 */
export const clientOnly = <T>(callback: () => T, fallback?: T): T | undefined => {
    if (isClient()) {
        try {
            return callback()
        } catch (error) {
            console.error('客户端代码执行错误:', error)
            return fallback
        }
    }
    return fallback
}

/**
 * 安全添加事件监听器
 * @param {string} event - 事件名称
 * @param {EventListener} handler - 事件处理函数
 * @param {boolean | AddEventListenerOptions} options - 事件选项
 * @returns {Function | undefined} 清理函数，用于移除事件监听器
 */
export const safeAddEventListener = (
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions,
): (() => void) | undefined => {
    return clientOnly(() => {
        window.addEventListener(event, handler, options)

        // 返回清理函数
        return () => {
            window.removeEventListener(event, handler, options)
        }
    })
}

/**
 * 安全获取元素尺寸
 * @param {string} selector - CSS 选择器
 * @returns {DOMRect | undefined} 元素尺寸信息
 */
export const safeGetElementRect = (selector: string): DOMRect | undefined => {
    return clientOnly(() => {
        const element = document.querySelector(selector)
        return element?.getBoundingClientRect()
    })
}

/**
 * 安全获取视口尺寸
 * @returns {{ width: number; height: number } | undefined} 视口尺寸
 */
export const safeGetViewportSize = (): { width: number; height: number } | undefined => {
    return clientOnly(() => ({
        width: window.innerWidth,
        height: window.innerHeight,
    }))
}

/**
 * 安全检查媒体查询
 * @param {string} query - 媒体查询字符串
 * @returns {boolean | undefined} 媒体查询结果
 */
export const safeMatchMedia = (query: string): boolean | undefined => {
    return clientOnly(() => {
        return window.matchMedia(query).matches
    })
}

/**
 * React Hook: 安全的 useEffect，只在客户端执行
 * @param {Function} effect - 副作用函数
 * @param {any[]} deps - 依赖数组
 */
export const useClientEffect = (effect: () => void | (() => void), deps?: React.DependencyList) => {
    useEffect(() => {
        if (isClient()) {
            return effect()
        }
    }, deps)
}

/**
 * React Hook: 安全的键盘事件监听
 * @param {Function} handler - 键盘事件处理函数
 * @param {any[]} deps - 依赖数组
 */
export const useKeyboardListener = (
    handler: (event: KeyboardEvent) => void,
    deps?: React.DependencyList,
) => {
    useEffect(() => {
        if (!isClient()) return

        const safeHandler = (e: KeyboardEvent) => {
            try {
                handler(e)
            } catch (error) {
                console.error('键盘事件处理错误:', error)
            }
        }

        window.addEventListener('keydown', safeHandler)

        return () => {
            window.removeEventListener('keydown', safeHandler)
        }
    }, deps)
}

/**
 * React Hook: 安全的窗口尺寸监听
 * @param {Function} handler - 尺寸变化处理函数
 * @param {any[]} deps - 依赖数组
 */
export const useWindowResize = (
    handler: (size: { width: number; height: number }) => void,
    deps?: React.DependencyList,
) => {
    useEffect(() => {
        if (!isClient()) return

        const safeHandler = () => {
            try {
                handler({
                    width: window.innerWidth,
                    height: window.innerHeight,
                })
            } catch (error) {
                console.error('窗口尺寸变化处理错误:', error)
            }
        }

        window.addEventListener('resize', safeHandler)

        return () => {
            window.removeEventListener('resize', safeHandler)
        }
    }, deps)
}
