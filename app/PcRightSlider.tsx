import { useForm } from 'react-hook-form'
import { useState, useCallback, useMemo, useRef, useEffect } from 'react'

import { PublicSliderComponent } from './components/Public_SliderComponet'
import DisplayContainer from './components/DisplayContainer/DisplayContainer'
import {
    LayoutConfig,
    singleDeviceLayoutConfigs,
    dualDeviceLayoutConfigs,
    tripleDeviceLayoutConfigs,
    LayoutType,
} from './components/DisplayContainer/DisplayConfig'
import { LayoutPreviewRenderer } from './components/LayoutPreviewRenderer/LayoutPreviewRenderer'
import { getSliderRange } from './components/Public_SliderComponet'

/**
 * @interface MousePosition
 * @description 鼠标位置信息接口
 * @property {number} x - 鼠标在容器内的 X 坐标
 * @property {number} y - 鼠标在容器内的 Y 坐标
 */
interface MousePosition {
    x: number
    y: number
}

/**
 * @interface PcRightSliderProps
 * @description PcRightSlider 组件的属性
 * @property {{type: LayoutType, id: number}} activeLayout - 当前需要渲染的布局信息
 * @property {(layout: {type: LayoutType, id: number}) => void} setActiveLayout - 更新布局的回调函数
 */
interface PcRightSliderProps {
    activeLayout: { type: LayoutType; id: number }
    setActiveLayout: (layout: { type: LayoutType; id: number }) => void
}

/**
 * @function calculateScaleFromZoom
 * @description 根据缩放值计算对应的 scale 值
 * 缩放值越大，scale 值越小，这是一个反比关系
 * 计算公式：scale = 100 / zoom
 * @param {number} zoom - 缩放值
 * @returns {number} 计算出的 scale 值
 */
const calculateScaleFromZoom = (zoom: number): number => {
    // 纯计算函数：scale = 100 / zoom
    // 这是一个反比关系，zoom 值越大，scale 值越小
    return 100 / zoom
}

/**
 * @function calculateTransformStyle
 * @description 根据鼠标位置计算 transform 样式字符串
 * @param {MousePosition} position - 鼠标位置对象
 * @param {boolean} isDragHandle - 是否为 drag-handle 元素，如果是则使用 translateX/translateY 格式
 * @returns {string} transform 样式字符串
 */
const calculateTransformStyle = (
    position: MousePosition,
    isDragHandle: boolean = false,
): string => {
    if (isDragHandle) {
        // drag-handle 使用 translateX 和 translateY 格式
        return `translateX(${position.x}px) translateY(${position.y}px)`
    } else {
        // cursor-indicator 使用 translate 格式
        return `translate(${position.x}px, ${position.y}px)`
    }
}

/**
 * @component PcRightSlider
 * @description PC端右侧滑块控制面板组件。
 * 它包含缩放控制、布局预览和相关的控制功能。
 * @param {PcRightSliderProps} props - 组件属性
 */
export const PcRightSlider = ({ activeLayout, setActiveLayout }: PcRightSliderProps) => {
    // 使用 react-hook-form 管理表单状态，主要用于缩放控制
    const { setValue, watch } = useForm({
        defaultValues: {
            // zoom: 200, // 默认缩放值为 200%，对应 scale 为 0.5 (100/200)
            zoom: 100,
        },
    })

    // 监听 zoom 字段的变化，实时获取当前缩放值
    const currentValue = watch('zoom')

    /**
     * @state selectedLayoutType
     * @description 当前选中的布局类型，用于控制显示哪个 LayoutPreviewRenderer
     * 可选值：LayoutType.Single（单设备）、LayoutType.Dual（双设备）、LayoutType.Triple（三设备）
     */
    const [selectedLayoutType, setSelectedLayoutType] = useState<LayoutType>(LayoutType.Single)

    /**
     * @state isMouseInPad
     * @description 鼠标是否在 position-pad-safearea 内
     * 用于控制 cursor-indicator 的显示/隐藏状态
     */
    const [isMouseInPad, setIsMouseInPad] = useState<boolean>(false)

    /**
     * @state mousePosition
     * @description 鼠标在容器内的当前位置
     * 用于实时更新 cursor-indicator 的位置，跟随鼠标移动
     */
    const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })

    /**
     * @state dragHandlePosition
     * @description drag-handle 的当前位置
     * 初始位置设置为容器中心偏左上的位置，这个位置是经过测试的最佳起始位置
     */
    const [dragHandlePosition, setDragHandlePosition] = useState<MousePosition>({
        x: 85.7143, // 初始 X 坐标：容器宽度的约 41% 位置
        y: 63.4286, // 初始 Y 坐标：容器高度的约 43% 位置
    })

    // --- 拖拽功能的核心状态 ---

    /**
     * @state isDragging
     * @description 标记当前是否正在进行拖拽操作。
     * - `true`: 表示用户已按下鼠标并开始拖拽。
     * - `false`: 表示拖拽未开始或已结束。
     * 这个状态是整个拖拽逻辑的开关，用于决定是否执行移动和结束的事件处理。
     */
    const [isDragging, setIsDragging] = useState(false)

    /**
     * @ref dragStartOffset
     * @description 使用 `useRef` 存储拖拽开始时，鼠标指针相对于拖拽手柄（.drag-handle）左上角的偏移量。
     * - **为什么用 `useRef` 而不是 `useState`?**
     *   1.  **无需触发重渲染**: 这个值的变化是拖拽过程中的内部状态，不需要引起组件的重新渲染。
     *   2.  **数据持久化**: `useRef` 可以在多次渲染之间保持其值不变，确保在整个拖拽过程中（从 mousedown 到 mouseup）偏移量数据是连续且正确的。
     * - **数据结构**: { x: number, y: number }
     */
    const dragStartOffset = useRef({ x: 0, y: 0 })

    /**
     * @ref wasDragged
     * @description 使用 `useRef` 标记当前操作是否为拖拽操作。
     * 用于区分真正的点击事件和拖拽结束后的点击事件，防止拖拽结束时触发点击逻辑。
     */
    const wasDragged = useRef(false)

    /**
     * @ref dragPadRef
     * @description `useRef` 用于获取对拖拽区域（.drag-pad）DOM 元素的直接引用。
     * - **目的**: 通过这个引用，我们可以调用 `getBoundingClientRect()` 方法来获取拖拽区域的精确尺寸和位置。
     *   这对于后续计算拖拽手柄的有效移动范围（边界限制）至关重要。
     * - **类型**: `React.RefObject<HTMLDivElement>`
     */
    const dragPadRef = useRef<HTMLDivElement>(null) // Ref for the drag-pad element

    /**
     * @function handleLayoutTypeChange
     * @description 处理布局类型切换，使用 useCallback 优化性能，避免频繁重渲染
     * @param {LayoutType} type - 要切换到的布局类型
     */
    const handleLayoutTypeChange = useCallback((type: LayoutType) => {
        setSelectedLayoutType(type)
    }, [])

    /**
     * @function handleMouseEnter
     * @description 处理鼠标移入 position-pad-safearea 事件
     * 当鼠标进入容器时，显示 cursor-indicator 元素
     */
    const handleMouseEnter = useCallback(() => {
        setIsMouseInPad(true) // 设置鼠标在容器内状态为 true，触发 cursor-indicator 显示
    }, [])

    /**
     * @function handleMouseLeave
     * @description 处理鼠标移出 position-pad-safearea 事件
     * 当鼠标离开容器时，隐藏 cursor-indicator 元素
     */
    const handleMouseLeave = useCallback(() => {
        setIsMouseInPad(false) // 设置鼠标在容器内状态为 false，触发 cursor-indicator 隐藏
    }, [])

    /**
     * @function handleMouseMove
     * @description 处理鼠标在 position-pad-safearea 内移动事件
     * 实时更新 cursor-indicator 的位置，让指示器跟随鼠标移动
     * @param {React.MouseEvent<HTMLDivElement>} event - 鼠标事件对象
     */
    const handleMouseMove = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            // 获取容器的边界矩形信息
            const rect = event.currentTarget.getBoundingClientRect()

            // 计算鼠标相对于容器的位置
            const x = event.clientX - rect.left // 鼠标在页面中的 X 坐标 - 容器左边界
            const y = event.clientY - rect.top // 鼠标在页面中的 Y 坐标 - 容器上边界

            // 确保鼠标位置在容器范围内，防止指示器超出边界
            const clampedX = Math.max(0, Math.min(x, rect.width)) // 限制 X 坐标在 0 到容器宽度之间
            const clampedY = Math.max(0, Math.min(y, rect.height)) // 限制 Y 坐标在 0 到容器高度之间

            // 始终更新鼠标位置状态，包括拖拽过程中
            setMousePosition({ x: clampedX, y: clampedY })
        },
        [], // 移除 isDragging 依赖，因为我们需要在拖拽时也更新位置
    )

    /**
     * @function handleMouseClick
     * @description 处理鼠标点击 position-pad-safearea 事件，更新 drag-handle 位置
     * 根据 cursor-indicator 和 drag-handle 的对齐逻辑，drag-handle 需要相对于 cursor-indicator 有一个固定的偏移量
     * 这个偏移量是通过实际测试得出的精确值，确保两个元素能够完美对齐
     * @param {React.MouseEvent<HTMLDivElement>} event - 鼠标事件对象
     */
    const handleMouseClick = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            // 如果刚完成拖拽操作，则忽略此次点击事件
            if (wasDragged.current) {
                wasDragged.current = false
                return
            }

            if (isDragging) return // 如果正在拖拽，则不执行点击移动

            // 获取容器的边界矩形信息
            const rect = event.currentTarget.getBoundingClientRect()

            // 计算点击位置相对于容器的坐标
            const x = event.clientX - rect.left // 点击位置在页面中的 X 坐标 - 容器左边界
            const y = event.clientY - rect.top // 点击位置在页面中的 Y 坐标 - 容器上边界

            // 确保点击位置在容器范围内，防止拖拽手柄超出边界
            const clampedX = Math.max(0, Math.min(x, rect.width)) // 限制 X 坐标在 0 到容器宽度之间
            const clampedY = Math.max(0, Math.min(y, rect.height)) // 限制 Y 坐标在 0 到容器高度之间

            // 根据对齐逻辑计算 drag-handle 的位置
            // 通过实际测试得出的精确偏移量：
            // 示例数据：
            //   cursor-indicator: translate(102.25px, 54.043px)
            //   drag-handle: translateX(92.9643px) translateY(48.4715px)
            // 计算差值：
            //   X轴偏移 = 92.9643 - 102.25 = -9.2857px
            //   Y轴偏移 = 48.4715 - 54.043 = -5.5715px
            // 因此 drag-handle 需要相对于点击位置向左上方偏移
            const dragHandleX = clampedX - 9.2857 // 硬编码：X轴偏移量，确保 drag-handle 与 cursor-indicator 对齐
            const dragHandleY = clampedY - 5.5715 // 硬编码：Y轴偏移量，确保 drag-handle 与 cursor-indicator 对齐

            // 更新拖拽手柄位置状态，触发 drag-handle 重新渲染
            setDragHandlePosition({ x: dragHandleX, y: dragHandleY })
        },
        [isDragging],
    )

    // --- 拖拽功能的事件处理器 ---

    /**
     * @function handleMouseDownOnDragHandle
     * @description 当用户在拖拽手柄（.drag-handle）上按下鼠标时触发此函数。
     * 这是拖拽操作的起点。
     * @param {React.MouseEvent<HTMLDivElement>} event - 鼠标事件对象。
     */
    const handleMouseDownOnDragHandle = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
        // 1. 阻止默认行为
        //    - 对于可拖拽的元素，浏览器可能会有默认的拖拽行为（例如，拖拽图片或链接）。
        //    - `event.preventDefault()` 可以防止这些默认行为的发生，确保我们的自定义拖拽逻辑能够顺利执行。
        event.preventDefault()

        // 2. 启动拖拽状态
        //    - 将 `isDragging` 状态设置为 `true`。这将激活 `useEffect` 中的全局事件监听器。
        setIsDragging(true)

        // 3. 计算并存储初始偏移量
        //    - `event.currentTarget.getBoundingClientRect()`: 获取拖拽手柄自身的尺寸和位置。
        //    - `event.clientX/Y`: 获取鼠标在视口中的精确坐标。
        //    - `dragStartOffset.current`: 计算鼠标点击点相对于拖拽手柄左上角的偏移量，并将其存储在 ref 中。
        //      这个偏移量在后续的 `mousemove` 事件中至关重要，因为它能确保拖拽手柄在移动时不会“跳”到鼠标指针的左上角，
        //      而是保持拖拽开始时的相对位置。
        const handleRect = event.currentTarget.getBoundingClientRect()
        dragStartOffset.current = {
            x: event.clientX - handleRect.left,
            y: event.clientY - handleRect.top,
        }
    }, [])

    /**
     * @effect useEffect (拖拽事件的全局监听)
     * @description 这个 `useEffect` 是实现拖拽功能的关键。
     * 它负责在拖拽开始时（`isDragging` 为 `true`）向整个文档（`document`）动态添加 `mousemove` 和 `mouseup` 事件监听器，
     * 并在拖拽结束时（`isDragging` 变为 `false` 或组件卸载）将它们移除。
     *
     * - **为什么在 `document` 上监听?**
     *   如果在组件的 `div` 上监听 `mousemove`，一旦鼠标移动过快，超出了 `div` 的范围，事件就会停止触发，导致拖拽中断。
     *   通过在 `document` 上进行全局监听，可以确保即使用户的鼠标移出浏览器窗口，拖拽行为依然能够平滑、连续地进行。
     *
     * - **依赖项**: `[isDragging]`
     *   这个 effect 仅在 `isDragging` 状态发生变化时才会重新执行，这符合我们的需求，并且避免了不必要的性能开销。
     */
    useEffect(() => {
        /**
         * @function handleMouseMoveGlobal
         * @description 全局的鼠标移动处理器。当 `isDragging` 为 `true` 时，此函数会持续更新拖拽手柄的位置。
         * @param {MouseEvent} event - 全局鼠标事件对象。
         */
        const handleMouseMoveGlobal = (event: MouseEvent) => {
            // 安全检查：如果 `isDragging` 为 false 或拖拽区域的 ref 不存在，则直接返回，不执行任何操作。
            if (!isDragging || !dragPadRef.current) return

            // 标记发生了拖拽操作，用于区分点击和拖拽结束
            wasDragged.current = true

            // 1. 获取拖拽区域的边界
            //    - `dragPadRef.current.getBoundingClientRect()`: 获取 `.drag-pad` 元素的位置和尺寸。
            const padRect = dragPadRef.current.getBoundingClientRect()

            // 2. 计算新的位置
            //    - `event.clientX/Y`: 当前鼠标在视口中的位置。
            //    - `padRect.left/top`: 拖拽区域的左上角在视口中的位置。
            //    - `dragStartOffset.current.x/y`: 拖拽开始时记录的鼠标与手柄的偏移量。
            //    - **公式**: 新位置 = (当前鼠标位置 - 拖拽区域左上角位置) - 初始偏移量
            let newX = event.clientX - padRect.left - dragStartOffset.current.x
            let newY = event.clientY - padRect.top - dragStartOffset.current.y

            // 3. 边界限制 (Constrain the position)
            //    - 为了防止拖拽手柄移出其容器（`.drag-pad`），我们需要将其位置限制在容器的边界内。
            const handleWidth = 28.5714 // 从内联样式中获取的拖拽手柄宽度
            const handleHeight = 21.1429 // 从内联样式中获取的拖拽手柄高度
            //    - `Math.max(0, ...)`: 确保手柄的左/上边缘不会超过容器的左/上边界。
            //    - `Math.min(..., padRect.width - handleWidth)`: 确保手柄的右/下边缘不会超过容器的右/下边界。
            newX = Math.max(0, Math.min(newX, padRect.width - handleWidth))
            newY = Math.max(0, Math.min(newY, padRect.height - handleHeight))

            // 4. 更新状态
            //    - 调用 `setDragHandlePosition`，传入经过边界限制后的新坐标，触发组件重渲染，从而移动拖拽手柄。
            setDragHandlePosition({ x: newX, y: newY })
        }

        /**
         * @function handleMouseUpGlobal
         * @description 全局的鼠标松开处理器。当用户松开鼠标时，此函数会停止拖拽操作。
         */
        const handleMouseUpGlobal = (event: MouseEvent) => {
            // 更新鼠标位置到释放时的位置，确保 cursor-indicator 立即显示在正确位置
            if (dragPadRef.current) {
                const rect = dragPadRef.current.getBoundingClientRect()
                const x = event.clientX - rect.left
                const y = event.clientY - rect.top

                // 确保鼠标位置在容器范围内
                const clampedX = Math.max(0, Math.min(x, rect.width))
                const clampedY = Math.max(0, Math.min(y, rect.height))

                setMousePosition({ x: clampedX, y: clampedY })
            }

            // 将 `isDragging` 状态设置回 `false`，这将导致 `useEffect` 的清理函数被调用，移除全局监听器。
            setIsDragging(false)

            // 注意：wasDragged.current 不会被重置，因为 click 事件会在 mouseup 之后触发
            // 它将在 handleMouseClick 中被检查并清除
        }

        // 当 `isDragging` 为 `true` 时，添加全局事件监听器。
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMoveGlobal)
            document.addEventListener('mouseup', handleMouseUpGlobal)
        }

        // **清理函数 (Cleanup Function)**
        //    - 这是 `useEffect` 的一个关键部分。它返回一个函数，该函数会在以下两种情况下被调用：
        //      1.  当组件卸载时。
        //      2.  在下一次 effect 执行之前（即 `isDragging` 状态改变时）。
        //    - **目的**: 移除之前添加的事件监听器，以防止内存泄漏和意外的 bug。
        //      如果不进行清理，即使用户已经停止拖拽，`handleMouseMoveGlobal` 仍然会继续被触发，造成不必要的性能损耗。
        return () => {
            document.removeEventListener('mousemove', handleMouseMoveGlobal)
            document.removeEventListener('mouseup', handleMouseUpGlobal)
        }
    }, [isDragging]) // 依赖项数组：确保此 effect 仅在 `isDragging` 变化时运行

    /**
     * @constant renderCurrentLayoutPreviews
     * @description 使用 useMemo 优化性能，只在 selectedLayoutType 变化时重新计算要渲染的组件
     * 根据当前选中的布局类型，渲染对应的布局预览组件
     */
    const renderCurrentLayoutPreviews = useMemo(() => {
        // 根据选中的布局类型渲染对应的预览组件
        if (selectedLayoutType === LayoutType.Single) {
            // 单设备布局预览：显示一个设备的布局配置
            return (
                <LayoutPreviewRenderer
                    type={LayoutType.Single}
                    configs={singleDeviceLayoutConfigs}
                    activeLayout={activeLayout}
                    setActiveLayout={setActiveLayout}
                    width={208} // 预览区域宽度：208px
                    height={156} // 预览区域高度：156px
                />
            )
        } else if (selectedLayoutType === LayoutType.Dual) {
            // 双设备布局预览：显示两个设备的布局配置
            return (
                <LayoutPreviewRenderer
                    type={LayoutType.Dual}
                    configs={dualDeviceLayoutConfigs}
                    activeLayout={activeLayout}
                    setActiveLayout={setActiveLayout}
                    width={208} // 预览区域宽度：208px
                    height={156} // 预览区域高度：156px
                />
            )
        } else {
            // 三设备布局预览：显示三个设备的布局配置
            return (
                <LayoutPreviewRenderer
                    type={LayoutType.Triple}
                    configs={tripleDeviceLayoutConfigs}
                    activeLayout={activeLayout}
                    setActiveLayout={setActiveLayout}
                    width={208} // 预览区域宽度：208px
                    height={156} // 预览区域高度：156px
                />
            )
        }
    }, [selectedLayoutType, activeLayout, setActiveLayout])

    /**
     * @function getActiveLayoutConfig
     * @description 根据传入的布局对象，从配置数组中查找并返回完整的布局配置。
     * 这个函数实现了防御性编程，确保即使配置不存在也能返回一个安全的默认值
     * @param {{type: LayoutType, id: number}} layout - 需要查找的布局对象。
     * @returns {LayoutConfig} 返回找到的布局配置。如果找不到，则返回一个默认配置。
     */
    const getActiveLayoutConfig = (layout: { type: LayoutType; id: number }): LayoutConfig => {
        let baseConfig: LayoutConfig | undefined

        // 1. 根据布局类型，在对应的配置数组中查找匹配的 ID。
        // 使用 find 方法查找具有相同 ID 的配置项
        if (layout.type === LayoutType.Single) {
            // 在单设备布局配置数组中查找
            baseConfig = singleDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Dual) {
            // 在双设备布局配置数组中查找
            baseConfig = dualDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Triple) {
            // 在三设备布局配置数组中查找
            baseConfig = tripleDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else {
            // 防御性编程：处理未知布局类型
            console.error(`未知的布局类型: ${layout.type}`)
        }

        // 2. 防御性编程：如果找不到配置，打印警告并返回一个安全的默认值。
        if (!baseConfig) {
            console.warn(
                `在 DisplayConfig.ts 中未找到布局 ${layout.type}-${layout.id} 的配置，将使用默认布局。`,
            )
            // 返回单设备布局的第一个作为默认值，确保组件不会崩溃
            return singleDeviceLayoutConfigs[0]
        }

        // 3. 深拷贝基础配置以避免意外修改原始配置数组，这对于共享数据至关重要。
        // 使用 JSON.parse(JSON.stringify()) 进行深拷贝，确保返回的配置是独立的副本
        return JSON.parse(JSON.stringify(baseConfig))
    }

    // 根据从 props 接收的 activeLayout 获取最终的布局配置
    // 这个配置将用于渲染 DisplayContainer 组件
    const activeLayoutConfig = getActiveLayoutConfig(activeLayout)

    // 调试信息 - 确认 PcRightSlider 成功获取了布局配置。
    // 在开发环境中用于验证布局配置是否正确传递
    console.log('✅ PcRightSlider布局配置成功获取:', {
        activeLayout, // 当前激活的布局信息
        activeLayoutConfig, // 获取到的完整布局配置
        hasLayoutConfig: true, // 标记是否成功获取配置
    })

    return (
        <div className='sidebar'>
            <div className='control-panel layout-panel'>
                <div
                    className='layouts-panel-controls-top'
                    style={{ top: 0, height: 'max-content' }}
                >
                    <div className='transform-controls-wrapper'>
                        <div className='position-controls' style={{ transform: 'none' }}>
                            <div className='layout-filters'>
                                <div className='switch' style={{ flex: 1 }}>
                                    {/* Single Layout Button */}
                                    <button
                                        className={`switch-button icon-only ${selectedLayoutType === LayoutType.Single ? 'is-active' : ''}`}
                                        onClick={() => handleLayoutTypeChange(LayoutType.Single)}
                                    >
                                        <div className='visual'>
                                            <div className='icon-wrapper'>
                                                <svg
                                                    xmlns='http://www.w3.org/2000/svg'
                                                    viewBox='0 0 24 24'
                                                >
                                                    <path
                                                        fill='currentColor'
                                                        d='M13.997 2A3.004 3.004 0 0 1 17 5.012v13.976A3 3 0 0 1 13.997 22h-3.995A3.005 3.005 0 0 1 7 18.988V5.012A3.01 3.01 0 0 1 10.002 2z'
                                                    />
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            className='active-indicator'
                                            style={{
                                                opacity:
                                                    selectedLayoutType === LayoutType.Single
                                                        ? 1
                                                        : 0,
                                            }}
                                        />
                                    </button>

                                    {/* Dual Layout Button */}
                                    <button
                                        className={`switch-button icon-only ${selectedLayoutType === LayoutType.Dual ? 'is-active' : ''}`}
                                        onClick={() => handleLayoutTypeChange(LayoutType.Dual)}
                                    >
                                        <div className='visual'>
                                            <div className='icon-wrapper'>
                                                <svg
                                                    xmlns='http://www.w3.org/2000/svg'
                                                    viewBox='0 0 24 24'
                                                >
                                                    <g fill='currentColor'>
                                                        <path d='M8.098 4A2.404 2.404 0 0 1 10.5 6.41v11.18A2.4 2.4 0 0 1 8.098 20H4.901A2.404 2.404 0 0 1 2.5 17.59V6.41A2.41 2.41 0 0 1 4.901 4zM19.098 4A2.404 2.404 0 0 1 21.5 6.41v11.18A2.4 2.4 0 0 1 19.098 20h-3.197a2.404 2.404 0 0 1-2.401-2.41V6.41A2.41 2.41 0 0 1 15.901 4z' />
                                                    </g>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            className='active-indicator'
                                            style={{
                                                opacity:
                                                    selectedLayoutType === LayoutType.Dual ? 1 : 0,
                                            }}
                                        />
                                    </button>

                                    {/* Triple Layout Button */}
                                    <button
                                        className={`switch-button icon-only ${selectedLayoutType === LayoutType.Triple ? 'is-active' : ''}`}
                                        onClick={() => handleLayoutTypeChange(LayoutType.Triple)}
                                    >
                                        <div className='visual'>
                                            <div className='icon-wrapper'>
                                                <svg
                                                    xmlns='http://www.w3.org/2000/svg'
                                                    viewBox='0 0 24 24'
                                                >
                                                    <g fill='currentColor'>
                                                        <path d='M5.548 6.5c1.083 0 1.952.879 1.952 1.958v9.084A1.95 1.95 0 0 1 5.548 19.5H2.951A1.953 1.953 0 0 1 1 17.542V8.458C1 7.379 1.875 6.5 2.951 6.5zM13.298 6.5c1.083 0 1.952.879 1.952 1.958v9.084a1.95 1.95 0 0 1-1.952 1.958h-2.597a1.953 1.953 0 0 1-1.951-1.958V8.458c0-1.079.875-1.958 1.951-1.958zM21.048 6.5C22.131 6.5 23 7.379 23 8.458v9.084a1.95 1.95 0 0 1-1.952 1.958h-2.597a1.953 1.953 0 0 1-1.951-1.958V8.458c0-1.079.875-1.958 1.951-1.958z' />
                                                    </g>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            className='active-indicator'
                                            style={{
                                                opacity:
                                                    selectedLayoutType === LayoutType.Triple
                                                        ? 1
                                                        : 0,
                                            }}
                                        />
                                    </button>
                                </div>
                            </div>
                            <div className='panel-control zoom-panel-control'>
                                <span className='label gray-text'>Zoom</span>
                                <div className='controls'>
                                    {/* 鼠标交互区域：处理鼠标移入、移出、移动和点击事件 */}
                                    <div
                                        className='position-pad-safearea'
                                        style={{ width: 208 }} // 硬编码：容器宽度 208px
                                        onMouseEnter={handleMouseEnter} // 鼠标移入时显示 cursor-indicator
                                        onMouseLeave={handleMouseLeave} // 鼠标移出时隐藏 cursor-indicator
                                        onMouseMove={handleMouseMove} // 鼠标移动时更新 cursor-indicator 位置
                                        onClick={handleMouseClick} // 鼠标点击时更新 drag-handle 位置
                                    >
                                        {/* 拖拽面板包装器：包含预览区域和拖拽区域 */}
                                        <div
                                            className='drag-pad-wrapper zoom-pad'
                                            style={{
                                                padding: 4, // 内边距：4px
                                                width: 208, // 硬编码：面板宽度 208px
                                                maxWidth: 208, // 最大宽度限制
                                                height: 156, // 硬编码：面板高度 156px
                                                maxHeight: 156, // 最大高度限制
                                            }}
                                        >
                                            <div
                                                className='pad-preview'
                                                style={{ pointerEvents: 'none' }}
                                            >
                                                <div
                                                    className='layout-item'
                                                    style={{ width: 208, height: 156 }}
                                                >
                                                    <div
                                                        className='frame layout-frame'
                                                        style={{ width: 208, height: 156 }}
                                                    >
                                                        <div className='frame-content'>
                                                            <div
                                                                style={{
                                                                    position: 'relative',
                                                                    overflow: 'hidden',
                                                                    width: 208,
                                                                    height: 156,
                                                                    opacity: 1,
                                                                }}
                                                            >
                                                                <div
                                                                    style={{
                                                                        width: 208,
                                                                        height: 156,
                                                                        display: 'flex',
                                                                        flexDirection: 'column',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        overflow: 'hidden',
                                                                    }}
                                                                >
                                                                    <DisplayContainer
                                                                        layoutConfig={
                                                                            activeLayoutConfig
                                                                        }
                                                                        canvasWidth={208}
                                                                        canvasHeight={156}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {/* 拖拽区域：包含 cursor-indicator 和 drag-handle */}
                                            <div
                                                ref={dragPadRef} // 绑定 ref，以便在 JS 中获取此元素的 DOM 节点
                                                className='drag-pad'
                                                style={{
                                                    position: 'relative', // 相对定位，作为子元素的定位参考
                                                    width: 200, // 硬编码：拖拽区域宽度 200px (208-4*2)
                                                    maxWidth: 200, // 最大宽度限制
                                                    height: 148, // 硬编码：拖拽区域高度 148px (156-4*2)
                                                    maxHeight: 148, // 最大高度限制
                                                    boxSizing: 'border-box', // 盒模型：包含内边距和边框
                                                }}
                                            >
                                                {/* 鼠标位置指示器：跟随鼠标移动的视觉指示器，只有鼠标在容器内时才显示 */}
                                                <div
                                                    className='cursor-indicator'
                                                    style={{
                                                        position: 'absolute', // 绝对定位，相对于 drag-pad 定位
                                                        transform:
                                                            calculateTransformStyle(mousePosition), // 使用 translate(x, y) 格式
                                                        pointerEvents: 'none', // 不响应鼠标事件，避免干扰
                                                        zIndex: 1, // 层级：确保在背景之上
                                                        display:
                                                            isMouseInPad && !isDragging
                                                                ? 'grid'
                                                                : 'none', // 根据鼠标状态和拖拽状态显示/隐藏
                                                        placeItems: 'center', // 网格布局：居中对齐
                                                    }}
                                                >
                                                    {/* 幽灵取景器：cursor-indicator 的视觉元素，显示缩放后的预览框 */}
                                                    <div
                                                        className='viewfinder-div ghost-viewfinder'
                                                        style={{
                                                            position: 'absolute', // 绝对定位
                                                            width: 208, // 硬编码：取景器宽度 208px
                                                            height: 156, // 硬编码：取景器高度 156px
                                                            scale: calculateScaleFromZoom(
                                                                currentValue,
                                                            ), // 根据缩放值计算 scale
                                                            opacity: 1, // 完全不透明
                                                        }}
                                                    />
                                                </div>
                                                {/* 拖拽手柄：用户点击时移动的目标元素，使用 translateX/translateY 格式 */}
                                                <div
                                                    className='drag-handle'
                                                    onMouseDown={handleMouseDownOnDragHandle} // 绑定 onMouseDown 事件，启动拖拽
                                                    style={{
                                                        transform: calculateTransformStyle(
                                                            dragHandlePosition,
                                                            true, // 标记为 drag-handle 元素，使用 translateX/translateY 格式
                                                        ),
                                                        willChange: 'transform', // 性能优化：告知浏览器此元素会频繁变换
                                                        width: '28.5714px', // 硬编码：手柄宽度
                                                        height: '21.1429px', // 硬编码：手柄高度
                                                        cursor: isDragging ? 'grabbing' : 'grab', // 根据拖拽状态切换鼠标手势
                                                        touchAction: 'none', // 禁用触摸操作，避免移动端干扰
                                                        display: 'grid', // 网格布局
                                                        placeItems: 'center', // 居中对齐
                                                    }}
                                                    tabIndex={0} // 键盘导航支持
                                                >
                                                    {/* 默认取景器：drag-handle 的视觉元素，显示缩放后的预览框 */}
                                                    <div
                                                        className='viewfinder-div default-viewfinder'
                                                        style={{
                                                            position: 'absolute', // 绝对定位
                                                            width: 208, // 硬编码：取景器宽度 208px
                                                            height: 156, // 硬编码：取景器高度 156px
                                                            scale: calculateScaleFromZoom(
                                                                currentValue,
                                                            ), // 根据缩放值计算 scale
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <PublicSliderComponent
                                        value={currentValue}
                                        setValue={value => setValue('zoom', value)}
                                        config='zoom'
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    style={{
                        position: 'relative',
                        width: '100%',
                        height: '100%',
                        marginTop: 156,
                    }}
                >
                    <div className='v-stack'>
                        <div className='scroll'>
                            <div
                                className='v-stack-content'
                                style={{ gap: 10, padding: '136px 10px 100px 10px' }}
                            >
                                <div className='panel-control undefined'>
                                    <span className='label gray-text'>layout presets</span>
                                    <div className='controls'>
                                        <div className='ad-container'>
                                            <button
                                                type='button'
                                                className='button icon-button small-button undefined-button true-blur true-round undefined-active close-button'
                                                style={{ flexDirection: 'row' }}
                                            >
                                                <svg
                                                    xmlns='http://www.w3.org/2000/svg'
                                                    fill='currentColor'
                                                    viewBox='0 0 24 24'
                                                >
                                                    <path d='M4.362 17.793c-.48.48-.49 1.332.01 1.831.51.5 1.361.49 1.832.02L12 13.846l5.788 5.788c.49.49 1.332.49 1.831-.01.5-.51.5-1.341.01-1.831l-5.788-5.788 5.788-5.798c.49-.49.5-1.332-.01-1.831-.499-.5-1.341-.5-1.83-.01L12 10.154 6.204 4.366c-.47-.48-1.332-.5-1.832.01-.5.5-.49 1.361-.01 1.831l5.788 5.798z' />
                                                </svg>
                                            </button>
                                            <div className='image-container'>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    src='/ad-content/ad-01.jpg'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    {/* 根据选中的布局类型渲染对应的 LayoutPreviewRenderer */}
                                    {renderCurrentLayoutPreviews}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
