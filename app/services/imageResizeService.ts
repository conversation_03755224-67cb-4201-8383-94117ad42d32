/**
 * 图片缩放服务模块
 * 
 * 功能：提供高质量的图片缩放服务，使用Pica库进行专业级图片处理
 * 特点：支持锐化、质量控制、精确尺寸计算、错误恢复
 */

import * as pica from 'pica'
import { getPicaInstance } from '../utils/picaConfig'

/**
 * 缩放配置选项接口
 * 定义图片缩放时的各种参数
 */
export interface ResizeOptions {
    /** 缩放因子，1.0表示原尺寸，2.0表示放大一倍 */
    scaleFactor: number
    
    /** 质量等级，0-3，3为最高质量 */
    quality?: number
    
    /** 锐化强度，默认80 */
    unsharpAmount?: number
    
    /** 锐化半径，默认0.6 */
    unsharpRadius?: number
    
    /** 锐化阈值，默认2 */
    unsharpThreshold?: number
}

/**
 * 创建源canvas用于图片处理
 * 
 * @param image - 原始图片元素
 * @returns HTMLCanvasElement - 包含原始图片的canvas
 * 
 * @example
 * ```typescript
 * const image = await loadImage("example.jpg")
 * const sourceCanvas = createSourceCanvas(image)
 * // sourceCanvas.width = image.naturalWidth (如800)
 * // sourceCanvas.height = image.naturalHeight (如600)
 * ```
 */
const createSourceCanvas = (image: HTMLImageElement): HTMLCanvasElement => {
    const canvas = document.createElement('canvas')
    
    // 设置canvas尺寸与原始图片一致
    canvas.width = image.naturalWidth
    canvas.height = image.naturalHeight
    
    // 获取2D渲染上下文
    const ctx = canvas.getContext('2d')
    if (!ctx) {
        throw new Error('无法创建canvas 2D渲染上下文')
    }
    
    // 将图片绘制到canvas上
    ctx.drawImage(image, 0, 0)
    
    return canvas
}

/**
 * 创建目标canvas用于缩放后的图片
 * 
 * @param originalWidth - 原始宽度
 * @param originalHeight - 原始高度  
 * @param scaleFactor - 缩放因子
 * @returns HTMLCanvasElement - 空的目标canvas
 * 
 * @example
 * ```typescript
 * const targetCanvas = createTargetCanvas(800, 600, 1.5)
 * // targetCanvas.width = 1200 (800 * 1.5)
 * // targetCanvas.height = 900 (600 * 1.5)
 * ```
 */
const createTargetCanvas = (
    originalWidth: number,
    originalHeight: number,
    scaleFactor: number,
): HTMLCanvasElement => {
    const canvas = document.createElement('canvas')
    
    // 计算缩放后的尺寸，使用Math.round确保整数像素
    canvas.width = Math.round(originalWidth * scaleFactor)
    canvas.height = Math.round(originalHeight * scaleFactor)
    
    return canvas
}

/**
 * 计算最终的质量等级
 * 确保返回0-3之间的整数等级
 * 
 * @param quality - 输入的质量参数
 * @returns 0 | 1 | 2 | 3 - 标准化的质量等级
 * 
 * @example
 * ```typescript
 * normalizeQuality(2.7) // 返回 3
 * normalizeQuality(-0.5) // 返回 0
 * normalizeQuality(1.2) // 返回 1
 * ```
 */
const normalizeQuality = (quality: number): 0 | 1 | 2 | 3 => {
    return Math.max(0, Math.min(3, Math.round(quality))) as 0 | 1 | 2 | 3
}

/**
 * 执行图片缩放的核心函数
 * 
 * @param image - 原始图片元素
 * @param options - 缩放配置选项
 * @returns Promise<string> - 缩放后的图片data URL
 * 
 * @example
 * ```typescript
 * const image = await loadImage("example.jpg") // 假设原图800x600
 * const resizedDataUrl = await resizeImage(image, {
 *   scaleFactor: 1.5,    // 放大到1200x900
 *   quality: 3,          // 最高质量
 *   unsharpAmount: 80    // 中等锐化
 * })
 * console.log(`缩放完成，数据长度: ${resizedDataUrl.length}`)
 * ```
 */
export const resizeImage = async (
    image: HTMLImageElement,
    options: ResizeOptions,
): Promise<string> => {
    const {
        scaleFactor,
        quality = 3,
        unsharpAmount = 80,
        unsharpRadius = 0.6,
        unsharpThreshold = 2,
    } = options
    
    // 参数验证
    if (scaleFactor <= 0) {
        throw new Error('缩放因子必须大于0')
    }
    
    if (!image.naturalWidth || !image.naturalHeight) {
        throw new Error('图片尺寸无效')
    }
    
    // 记录处理开始信息
    console.log('开始图片缩放处理:', {
        originalWidth: image.naturalWidth,
        originalHeight: image.naturalHeight,
        scaleFactor,
        newWidth: Math.round(image.naturalWidth * scaleFactor),
        newHeight: Math.round(image.naturalHeight * scaleFactor),
        quality: normalizeQuality(quality),
        unsharpAmount,
        unsharpRadius,
        unsharpThreshold,
    })
    
    try {
        // 获取Pica实例
        const picaInstance = getPicaInstance()
        
        // 创建源canvas（包含原始图片）
        const sourceCanvas = createSourceCanvas(image)
        
        // 创建目标canvas（指定缩放后尺寸）
        const targetCanvas = createTargetCanvas(
            image.naturalWidth,
            image.naturalHeight,
            scaleFactor,
        )
        
        // 使用Pica进行高质量缩放
        console.log('正在执行Pica高质量缩放...')
        const resizedCanvas = await picaInstance.resize(sourceCanvas, targetCanvas, {
            quality: normalizeQuality(quality),
            unsharpAmount,
            unsharpRadius,
            unsharpThreshold,
        })
        
        // 将结果canvas转换为data URL
        console.log('缩放完成，正在转换为Data URL...')
        const dataUrl = resizedCanvas.toDataURL('image/png', 1.0)
        
        // 记录处理结果
        console.log(`图片处理完成: ${dataUrl.substring(0, 50)}...`)
        
        return dataUrl
        
    } catch (error) {
        // 提供详细的错误信息
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        console.error('图片缩放失败:', errorMessage)
        throw new Error(`缩放处理失败: ${errorMessage}`)
    }
}

/**
 * 快速验证图片是否可以缩放
 * 
 * @param image - 要验证的图片
 * @param scaleFactor - 缩放因子
 * @returns boolean - 是否可以进行缩放
 * 
 * @example
 * ```typescript
 * const image = new Image()
 * image.src = "test.jpg"
 * 
 * if (canResizeImage(image, 2.0)) {
 *   console.log("可以进行2倍缩放")
 * } else {
 *   console.log("无法进行缩放")
 * }
 * ```
 */
export const canResizeImage = (
    image: HTMLImageElement,
    scaleFactor: number,
): boolean => {
    return Boolean(
        image && 
        image.naturalWidth > 0 && 
        image.naturalHeight > 0 && 
        scaleFactor > 0,
    )
}

/**
 * 计算缩放后的图片尺寸
 * 
 * @param image - 原始图片
 * @param scaleFactor - 缩放因子
 * @returns 缩放后的尺寸对象
 * 
 * @example
 * ```typescript
 * const image = { naturalWidth: 1920, naturalHeight: 1080 }
 * const newSize = calculateNewSize(image, 0.5)
 * // 返回: { width: 960, height: 540 }
 * ```
 */
export const calculateNewSize = (
    image: HTMLImageElement,
    scaleFactor: number,
): { width: number; height: number } => {
    return {
        width: Math.round(image.naturalWidth * scaleFactor),
        height: Math.round(image.naturalHeight * scaleFactor),
    }
}