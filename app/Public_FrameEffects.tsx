import { useIsMobile } from './hooks/useAppState'
import { useForm } from 'react-hook-form'
import { PublicSliderComponent } from './components/Public_SliderComponet'

export const Public_FrameEffects = ({
    isActiveBackgroundEffectsModal,
    setIsActiveBackgroundEffectsModal,
    isActivePortraitModal,
    setIsActivePortraitModal,
    isActiveVefModal,
    setIsActiveVefModal,
}: {
    isActiveBackgroundEffectsModal: boolean
    setIsActiveBackgroundEffectsModal: (isActive: boolean) => void
    isActivePortraitModal: boolean
    setIsActivePortraitModal: (isActive: boolean) => void
    isActiveVefModal: boolean
    setIsActiveVefModal: (isActive: boolean) => void
}) => {
    // 初始化react-hook-form
    const { setValue, watch } = useForm({
        defaultValues: {
            noise: 10,
        },
    })

    // 监听当前值
    const currentValue = watch('noise')

    const publicRender = () => {}
    const mobileInViewRender = () => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile undefined'
                style={{ opacity: 1, transform: 'none' }}
            >
                <PublicSliderComponent
                    config='noise'
                    value={currentValue}
                    setValue={value => setValue('noise', value)}
                />
            </div>
        )
    }
    const pcInViewRender = () => {
        return (
            <div className='panel-control undefined '>
                <span className='label gray-text'>effects</span>
                <div className='controls'>
                    <div className='panel-control-grid col-3'>
                        <div
                            className='panel-button frame-scene-panel-button frame-lens-scene-button undefined-active '
                            onClick={() =>
                                setIsActiveBackgroundEffectsModal(!isActiveBackgroundEffectsModal)
                            }
                        >
                            <div className='preview' style={{ aspectRatio: '5 / 4.5' }}>
                                <div
                                    className={`rich-preview ${
                                        isActiveBackgroundEffectsModal ? 'bg-panel-active' : ''
                                    }`}
                                >
                                    <div className='label-wrapper'>
                                        <span>Effects</span>
                                        <p>
                                            <span>None</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className='panel-button frame-scene-panel-button frame-lens-scene-button undefined-active '
                            onClick={() => setIsActivePortraitModal(!isActivePortraitModal)}
                        >
                            <div className='preview' style={{ aspectRatio: '5 / 4.5' }}>
                                <div className='rich-preview '>
                                    <div className='label-wrapper'>
                                        <span>Portrait</span>
                                        <p>none</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className='panel-button frame-scene-panel-button frame-effect-scene-button undefined-active '
                            onClick={() => setIsActiveVefModal(!isActiveVefModal)}
                        >
                            <div className='preview' style={{ aspectRatio: '5 / 4.5' }}>
                                <div className='rich-preview '>
                                    <div className='label-wrapper'>
                                        <span>VFX</span>
                                        <p>none</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
    if (useIsMobile()) {
        return mobileInViewRender()
    }
    return pcInViewRender()
}
