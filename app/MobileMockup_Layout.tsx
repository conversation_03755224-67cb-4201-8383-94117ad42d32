import { useState } from 'react'
import {
    LayoutType,
    singleDeviceLayoutConfigs,
    dualDeviceLayoutConfigs,
    tripleDeviceLayoutConfigs,
} from './components/DisplayContainer/DisplayConfig'
import { LayoutPreviewRenderer } from './components/LayoutPreviewRenderer/LayoutPreviewRenderer'

/**
 * @interface MobileMockupLayoutProps
 * @description MobileMockup_Layout 组件的属性
 * @property {{type: LayoutType, id: number}} activeLayout - 当前全局激活的布局信息。
 * @property {(layout: {type: LayoutType, id: number}) => void} setActiveLayout - 更新全局激活布局的回调函数。
 */
interface MobileMockupLayoutProps {
    activeLayout: { type: LayoutType; id: number }
    setActiveLayout: (layout: { type: LayoutType; id: number }) => void
}

export const MobileMockup_Layout = ({ activeLayout, setActiveLayout }: MobileMockupLayoutProps) => {
    const tabs = {
        Presets: 'Presets',
        Custom: 'Custom',
    }
    const [activeTab, setActiveTab] = useState<string>('Presets')

    return (
        <div
            id='panel-undefined-control-mobile'
            className='panel-control-mobile undefined'
            style={{ opacity: 1, transform: 'none' }}
        >
            <div className='panel-control-segment-wrapper' style={{ flexDirection: 'column' }}>
                {activeTab === 'Presets' && (
                    <section className='segment-section'>
                        <div className='panel-control undefined '>
                            <div className='controls'>
                                <div className='panel-control-stack'>
                                    <div className='stack-content'>
                                        {/* 1. */}
                                        <LayoutPreviewRenderer
                                            type={LayoutType.Single}
                                            configs={singleDeviceLayoutConfigs}
                                            activeLayout={activeLayout}
                                            setActiveLayout={setActiveLayout}
                                            width={117}
                                            height={88}
                                        />

                                        {/* 2.  */}
                                        <LayoutPreviewRenderer
                                            type={LayoutType.Dual}
                                            configs={dualDeviceLayoutConfigs}
                                            activeLayout={activeLayout}
                                            setActiveLayout={setActiveLayout}
                                            width={117}
                                            height={88}
                                        />

                                        {/* 3. */}
                                        <LayoutPreviewRenderer
                                            type={LayoutType.Triple}
                                            configs={tripleDeviceLayoutConfigs}
                                            activeLayout={activeLayout}
                                            setActiveLayout={setActiveLayout}
                                            width={117}
                                            height={88}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                )}

                {activeTab === 'Custom' && (
                    <section className='segment-section'>
                        <div style={{ display: 'flex', justifyContent: 'center' }}>
                            <div
                                className='drag-pad-wrapper'
                                style={{
                                    padding: 4,
                                    width: 152,
                                    maxWidth: 152,
                                    height: 152,
                                    maxHeight: 152,
                                }}
                            >
                                <div
                                    className='drag-pad'
                                    style={{
                                        position: 'relative',
                                        width: 144,
                                        maxWidth: 144,
                                        height: 144,
                                        maxHeight: 144,
                                        boxSizing: 'border-box',
                                    }}
                                >
                                    <div
                                        className='drag-handle'
                                        tabIndex={0}
                                        style={{
                                            transform: 'translateX(57.6px) translateY(57.6px)',
                                            willChange: 'transform',
                                            width: '28.8px',
                                            height: '28.8px',
                                            cursor: 'grab',
                                            touchAction: 'none',
                                            display: 'grid',
                                            placeItems: 'center',
                                        }}
                                    />
                                    <div
                                        className='drag-grid-tile flex-1'
                                        style={{ display: 'flex' }}
                                    >
                                        <div className='preset-col'>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                        </div>
                                        <div className='preset-col'>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                        </div>
                                        <div className='preset-col'>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                        </div>
                                        <div className='preset-col'>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                            <div className='preset-button'>
                                                <div
                                                    className='tile'
                                                    style={{ borderRadius: 12 }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <form>
                            <span
                                dir='ltr'
                                data-orientation='horizontal'
                                aria-disabled='false'
                                className='slider-component undefined-disabled hide-rail'
                                style={
                                    {
                                        width: '100%',
                                        '--radix-slider-thumb-transform': 'translateX(-50%)',
                                    } as React.CSSProperties
                                }
                            >
                                <span data-orientation='horizontal' className='SliderTrack track'>
                                    <span
                                        data-orientation='horizontal'
                                        className='SliderRange rail'
                                        style={{ left: '0%', right: '92.3077%' }}
                                    />
                                </span>
                                <span
                                    style={{
                                        transform: 'var(--radix-slider-thumb-transform)',
                                        position: 'absolute',
                                        left: 'calc(7.69231% + 4.23077px)',
                                    }}
                                >
                                    <span
                                        role='slider'
                                        aria-label='Volume'
                                        aria-valuemin={75}
                                        aria-valuemax={400}
                                        aria-orientation='horizontal'
                                        data-orientation='horizontal'
                                        tabIndex={0}
                                        className='SliderThumb thumb'
                                        data-radix-collection-item=''
                                        aria-valuenow={100}
                                        style={{}}
                                    />
                                    <input defaultValue={100} style={{ display: 'none' }} />
                                </span>
                                <div className='labels'>
                                    <span id='zoom-slider-label'>Zoom</span>
                                    <span>100%</span>
                                </div>
                            </span>
                        </form>
                    </section>
                )}

                <div className='segment-buttons'>
                    <button
                        onClick={() => setActiveTab('Presets')}
                        type='button'
                        className={`button default-button small-button undefined-button undefined-blur true-round ${activeTab === 'Presets' ? 'true-active' : 'false-active'} undefined`}
                        style={{ flexDirection: 'row', minWidth: 88 }}
                    >
                        <span>Presets</span>
                    </button>
                    <button
                        onClick={() => setActiveTab('Custom')}
                        type='button'
                        className={`button default-button small-button undefined-button undefined-blur true-round ${activeTab === 'Custom' ? 'true-active' : 'false-active'} undefined`}
                        style={{ flexDirection: 'row', minWidth: 88 }}
                    >
                        <span>Size & Position</span>
                    </button>
                </div>
            </div>
        </div>
    )
}
