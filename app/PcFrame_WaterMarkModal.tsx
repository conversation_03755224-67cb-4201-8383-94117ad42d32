import { useIsMobile } from './hooks/useAppState'
import { HTML_ACCEPT_ATTRIBUTES } from './ImageMange/imageConfig'

export const PcFrame_WaterMarkModal = () => {
    return (
        <div
            className='popover undefined'
            style={{
                top: 197,
                right: 'unset',
                bottom: 'unset',
                left: 240,
                width: 236,
                height: 'auto',
                margin: '-100px 0px 0px',
                filter: 'blur(0px)',
                opacity: 1,
                transform: 'none',
            }}
        >
            <div className='v-stack'>
                <div className='scroll'>
                    <div className='v-stack-content' style={{ gap: 20, padding: '0px 10px 10px' }}>
                        <div className='panel-control undefined'>
                            <div className='controls'>
                                <div className='watermark-layout-control'>
                                    <div
                                        style={{
                                            position: 'absolute',
                                            inset: '10px 0px',
                                            zIndex: 5,
                                            cursor: 'pointer',
                                        }}
                                    >
                                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                            <div
                                                className='plus-badge'
                                                style={{ width: 16, height: 16 }}
                                            >
                                                <svg
                                                    xmlns='http://www.w3.org/2000/svg'
                                                    viewBox='0 0 24 24'
                                                >
                                                    <path
                                                        fill='currentColor'
                                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                                    />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <div className='backdrop'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='data:,'
                                        />
                                    </div>
                                    <button
                                        type='button'
                                        className='button icon-button tiny-button undefined-button true-blur true-round undefined-active undefined'
                                        style={{ flexDirection: 'row' }}
                                    >
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='none'
                                                stroke='currentColor'
                                                strokeLinecap='round'
                                                strokeLinejoin='round'
                                                strokeWidth={2}
                                                d='m15 18-6-6 6-6'
                                            />
                                        </svg>
                                    </button>
                                    <button
                                        type='button'
                                        className='button icon-button tiny-button undefined-button true-blur true-round undefined-active undefined'
                                        style={{ flexDirection: 'row' }}
                                    >
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M17.179 12.168a.95.95 0 0 0-.293-.692L9.644 4.281A.9.9 0 0 0 8.964 4c-.55 0-.972.41-.972.96 0 .258.117.504.281.692l6.551 6.516-6.551 6.515a1.05 1.05 0 0 0-.281.692c0 .55.422.96.972.96a.9.9 0 0 0 .68-.281l7.242-7.195a.95.95 0 0 0 .293-.691'
                                            />
                                        </svg>
                                    </button>
                                    <div className='watermark-layout-indicators'>
                                        <span className='dot is-active' />
                                        <span className='dot' />
                                        <span className='dot' />
                                        <span className='dot' />
                                    </div>
                                    <div className='items-scroll'>
                                        <div className='all-items'>
                                            <div className='watermark-layout-item'>
                                                <div className='custom-watermark-display'>
                                                    <div className='watermark-element watermark-none'>
                                                        <svg
                                                            xmlns='http://www.w3.org/2000/svg'
                                                            viewBox='0 0 24 24'
                                                        >
                                                            <path
                                                                fill='currentColor'
                                                                fillRule='evenodd'
                                                                d='M3.575 7.088A9.7 9.7 0 0 0 2.25 12c0 5.384 4.365 9.75 9.75 9.75 1.79 0 3.468-.483 4.911-1.326l-1.104-1.104A8.25 8.25 0 0 1 3.75 12a8.2 8.2 0 0 1 .929-3.808zm15.686 8.831A8.25 8.25 0 0 0 12 3.75a8.2 8.2 0 0 0-3.92.988L6.981 3.639A9.7 9.7 0 0 1 12 2.25c5.384 0 9.75 4.365 9.75 9.75a9.7 9.7 0 0 1-1.39 5.018z'
                                                            />
                                                            <rect
                                                                width='1.89'
                                                                height='26.833'
                                                                x='1.788'
                                                                y='3.211'
                                                                fill='currentColor'
                                                                rx='0.945'
                                                                ry='0.945'
                                                                transform='rotate(-45 1.789 3.211)'
                                                            />
                                                        </svg>
                                                        <div className='texts-wrapper'>
                                                            <span className='title-text'>
                                                                No watermark
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='watermark-layout-item'>
                                                <div className='custom-watermark-display'>
                                                    <div className='watermark-element watermark-layout-1 watermark-default watermark-light watermark-font-default'>
                                                        <div className='image-placeholder no-image'>
                                                            <svg
                                                                xmlns='http://www.w3.org/2000/svg'
                                                                viewBox='0 0 24 24'
                                                            >
                                                                <path
                                                                    fill='currentColor'
                                                                    d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                                />
                                                            </svg>
                                                        </div>
                                                        <div className='texts-wrapper'>
                                                            <span className='title-text'>
                                                                Watermark
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='watermark-layout-item'>
                                                <div className='custom-watermark-display'>
                                                    <div className='watermark-element watermark-layout-2 watermark-default watermark-light watermark-font-default'>
                                                        <div className='image-placeholder no-image'>
                                                            <svg
                                                                xmlns='http://www.w3.org/2000/svg'
                                                                viewBox='0 0 24 24'
                                                            >
                                                                <path
                                                                    fill='currentColor'
                                                                    d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                                />
                                                            </svg>
                                                        </div>
                                                        <div className='texts-wrapper'>
                                                            <span className='title-text'>
                                                                Watermark
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='watermark-layout-item'>
                                                <div className='custom-watermark-display'>
                                                    <div className='watermark-element watermark-layout-3 watermark-default watermark-light watermark-font-default'>
                                                        <div className='image-placeholder no-image'>
                                                            <svg
                                                                xmlns='http://www.w3.org/2000/svg'
                                                                viewBox='0 0 24 24'
                                                            >
                                                                <path
                                                                    fill='currentColor'
                                                                    d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                                />
                                                            </svg>
                                                        </div>
                                                        <div className='texts-wrapper'>
                                                            <span className='title-text'>
                                                                Watermark
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='panel-control undefined'>
                            <div className='controls'>
                                <div className='watermark-content-control'>
                                    <div className='panel-button watermark-icon-button undefined-active'>
                                        <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                            <div className='icon'>
                                                <svg
                                                    xmlns='http://www.w3.org/2000/svg'
                                                    viewBox='0 0 24 24'
                                                >
                                                    <path
                                                        fill='currentColor'
                                                        d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                    />
                                                </svg>
                                            </div>
                                            <input
                                                className='d-none'
                                                accept={HTML_ACCEPT_ATTRIBUTES.LEGACY_IMAGES}
                                                type='file'
                                            />
                                        </div>
                                    </div>
                                    <div className='input-box undefined icon-true small-size'>
                                        <input
                                            className='input-text'
                                            maxLength={25}
                                            placeholder='Watermark'
                                            type='text'
                                            defaultValue=''
                                        />
                                        <div className='icon-wrap'>
                                            <svg
                                                xmlns='http://www.w3.org/2000/svg'
                                                viewBox='0 0 24 24'
                                            >
                                                <g fill='currentColor'>
                                                    <path d='M15.46 22h5.67c.388 0 .603-.185.603-.509 0-.328-.215-.5-.603-.5h-2.256V3.013h2.256c.388 0 .603-.185.603-.517 0-.312-.215-.496-.603-.496h-5.67c-.396 0-.598.184-.598.496 0 .332.202.517.598.517h2.248v17.978H15.46c-.396 0-.598.172-.598.5 0 .324.202.509.598.509M3.221 19.09c.44 0 .664-.173.825-.642L5.41 14.78h5.868l1.36 3.668c.165.469.385.642.825.642.447 0 .739-.266.739-.701 0-.143-.03-.27-.097-.46L9.421 5.561c-.195-.513-.534-.774-1.075-.774-.533 0-.88.258-1.062.766L2.596 17.938c-.076.19-.096.316-.096.46 0 .435.278.692.721.692m2.623-5.623 2.486-6.78h.054l2.482 6.78z' />
                                                </g>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='panel-control undefined'>
                            <span className='label gray-text'>style</span>
                            <div className='controls'>
                                <div className='panel-control-grid col-4'>
                                    <div className='panel-button undefined true-active has-label'>
                                        <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                            <div className='image-wrapper'>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    src='/image/watermark-styles/Default.png'
                                                />
                                            </div>
                                        </div>
                                        <div className='label-wrapper'>
                                            <span className='footnote truncate'>Default</span>
                                        </div>
                                    </div>
                                    <div className='panel-button undefined false-active has-label'>
                                        <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                            <div className='image-wrapper'>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    src='/image/watermark-styles/Shadow.png'
                                                />
                                            </div>
                                        </div>
                                        <div className='label-wrapper'>
                                            <span className='footnote truncate'>Shadow</span>
                                        </div>
                                    </div>
                                    <div className='panel-button undefined false-active has-label'>
                                        <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                            <div className='image-wrapper'>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    src='/image/watermark-styles/Glass.png'
                                                />
                                            </div>
                                        </div>
                                        <div className='label-wrapper'>
                                            <span className='footnote truncate'>Glass</span>
                                        </div>
                                    </div>
                                    <div className='panel-button undefined false-active has-label'>
                                        <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                            <div className='image-wrapper'>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    src='/image/watermark-styles/Badge.png'
                                                />
                                            </div>
                                        </div>
                                        <div className='label-wrapper'>
                                            <span className='footnote truncate'>Badge</span>
                                        </div>
                                    </div>
                                </div>
                                <div />
                                <div className='panel-control undefined'>
                                    <div className='controls'>
                                        <div className='panel-control-grid col-2'>
                                            <div className='panel-button undefined true-active has-label'>
                                                <div
                                                    className='preview'
                                                    style={{ aspectRatio: '3 / 1' }}
                                                >
                                                    <div
                                                        className='color-display'
                                                        style={{
                                                            background: 'rgb(255, 255, 255)',
                                                            position: 'absolute',
                                                            inset: 0,
                                                        }}
                                                    />
                                                </div>
                                                <div className='label-wrapper'>
                                                    <span className='footnote truncate'>Light</span>
                                                </div>
                                            </div>
                                            <div className='panel-button undefined false-active has-label'>
                                                <div
                                                    className='preview'
                                                    style={{ aspectRatio: '3 / 1' }}
                                                >
                                                    <div
                                                        className='color-display'
                                                        style={{
                                                            background: 'rgb(0, 0, 0)',
                                                            position: 'absolute',
                                                            inset: 0,
                                                        }}
                                                    />
                                                </div>
                                                <div className='label-wrapper'>
                                                    <span className='footnote truncate'>Dark</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='panel-control undefined'>
                            <span className='label gray-text'>Size &amp; position</span>
                            <div className='controls'>
                                <div style={{ display: 'flex', gap: 8 }}>
                                    <div className='watermark-position-button'>
                                        <div className='position-display'>
                                            <div className='knob-area'>
                                                <div
                                                    style={{
                                                        position: 'absolute',
                                                        left: 0,
                                                        right: 0,
                                                        top: 'unset',
                                                        bottom: 0,
                                                        margin: 'auto',
                                                    }}
                                                >
                                                    <svg
                                                        xmlns='http://www.w3.org/2000/svg'
                                                        viewBox='0 0 24 24'
                                                    >
                                                        <path
                                                            fill='currentColor'
                                                            d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                        />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: 8,
                                            flex: '1 1 0%',
                                        }}
                                    >
                                        <form>
                                            <span
                                                dir='ltr'
                                                data-orientation='horizontal'
                                                aria-disabled='false'
                                                className='slider-component undefined-disabled hide-rail'
                                                style={{
                                                    width: '100%',
                                                    '--radix-slider-thumb-transform':
                                                        'translateX(-50%)',
                                                }}
                                            >
                                                <span
                                                    data-orientation='horizontal'
                                                    className='SliderTrack track'
                                                >
                                                    <span
                                                        data-orientation='horizontal'
                                                        className='SliderRange rail'
                                                        style={{ left: '0%', right: '50%' }}
                                                    />
                                                </span>
                                                <span
                                                    style={{
                                                        transform:
                                                            'var(--radix-slider-thumb-transform)',
                                                        position: 'absolute',
                                                        left: 'calc(50% - 4.44089e-16px)',
                                                    }}
                                                >
                                                    <span
                                                        role='slider'
                                                        aria-label='Volume'
                                                        aria-valuemin={40}
                                                        aria-valuemax={120}
                                                        aria-orientation='horizontal'
                                                        data-orientation='horizontal'
                                                        tabIndex={0}
                                                        className='SliderThumb thumb'
                                                        data-radix-collection-item=''
                                                        aria-valuenow={80}
                                                        style={{}}
                                                    />
                                                    <input
                                                        defaultValue={80}
                                                        style={{ display: 'none' }}
                                                    />
                                                </span>
                                                <div className='labels'>
                                                    <span id='size-slider-label'>Size</span>
                                                    <span>80</span>
                                                </div>
                                            </span>
                                        </form>
                                        <form>
                                            <span
                                                dir='ltr'
                                                data-orientation='horizontal'
                                                aria-disabled='false'
                                                className='slider-component undefined-disabled hide-rail is-at-start'
                                                style={{
                                                    width: '100%',
                                                    '--radix-slider-thumb-transform':
                                                        'translateX(-50%)',
                                                }}
                                            >
                                                <span
                                                    data-orientation='horizontal'
                                                    className='SliderTrack track'
                                                >
                                                    <span
                                                        data-orientation='horizontal'
                                                        className='SliderRange rail'
                                                        style={{ left: '0%', right: '100%' }}
                                                    />
                                                </span>
                                                <span
                                                    style={{
                                                        transform:
                                                            'var(--radix-slider-thumb-transform)',
                                                        position: 'absolute',
                                                        left: 'calc(0% + 3.5px)',
                                                    }}
                                                >
                                                    <span
                                                        role='slider'
                                                        aria-label='Volume'
                                                        aria-valuemin={5}
                                                        aria-valuemax={15}
                                                        aria-orientation='horizontal'
                                                        data-orientation='horizontal'
                                                        tabIndex={0}
                                                        className='SliderThumb thumb'
                                                        data-radix-collection-item=''
                                                        aria-valuenow={5}
                                                        style={{}}
                                                    />
                                                    <input
                                                        defaultValue={5}
                                                        style={{ display: 'none' }}
                                                    />
                                                </span>
                                                <div className='labels'>
                                                    <span id='inset-slider-label'>Inset</span>
                                                    <span>5</span>
                                                </div>
                                            </span>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
