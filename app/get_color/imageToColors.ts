/**
 * 图片颜色提取工具模块
 *
 * 该模块提供基于 ColorThief 库的图片主色调提取功能
 * 支持从图片 URL、文件对象或 HTML 图片元素中提取颜色调色板
 */

// @ts-expect-error ColorThief 库没有类型声明文件
import ColorThief from 'colorthief'

/**
 * 颜色提取结果接口
 * @interface ColorExtractionResult
 */
export interface ColorExtractionResult {
    /** 十六进制颜色代码数组 */
    hexColors: string[]
    /** RGB 颜色值数组 */
    rgbColors: number[][]
    /** 提取状态 */
    success: boolean
    /** 错误信息（如果失败） */
    error?: string
}

/**
 * 颜色提取配置选项
 * @interface ColorExtractionOptions
 */
export interface ColorExtractionOptions {
    /** 要提取的颜色数量，默认为 5 */
    colorCount?: number
    /** 是否启用跨域，默认为 true */
    crossOrigin?: boolean | string
}

/**
 * 将 RGB 数组转换为十六进制颜色代码
 *
 * @param {number[]} rgb - RGB 颜色值数组 [r, g, b]
 * @returns {string} 十六进制颜色代码 (如: #FF0000)
 */
function rgbToHex(rgb: number[]): string {
    if (!rgb || rgb.length !== 3) {
        throw new Error('RGB 数组必须包含 3 个元素')
    }

    return (
        '#' +
        rgb
            .map(colorValue => {
                // 确保颜色值在 0-255 范围内
                const clampedValue = Math.max(0, Math.min(255, Math.round(colorValue)))
                const hex = clampedValue.toString(16)
                // 确保两位数的十六进制表示
                return hex.length === 1 ? '0' + hex : hex
            })
            .join('')
            .toUpperCase()
    )
}

/**
 * 从图片元素中提取颜色调色板
 *
 * @param {HTMLImageElement} imageElement - 已加载完成的图片元素
 * @param {ColorExtractionOptions} options - 提取配置选项
 * @returns {Promise<ColorExtractionResult>} 颜色提取结果
 */
async function extractColorsFromImageElement(
    imageElement: HTMLImageElement,
    options: ColorExtractionOptions = {},
): Promise<ColorExtractionResult> {
    const { colorCount = 5 } = options

    try {
        // 验证图片元素
        if (!imageElement) {
            throw new Error('图片元素不存在')
        }

        // 确保图片完全加载
        if (!imageElement.complete || imageElement.naturalWidth === 0) {
            throw new Error('图片未完全加载')
        }

        // 创建 ColorThief 实例
        const colorThief = new ColorThief()

        // 从图片中提取颜色调色板
        const palette = colorThief.getPalette(imageElement, colorCount)

        if (!palette || palette.length === 0) {
            throw new Error('无法从图片中提取颜色调色板')
        }

        // 将 RGB 数组转换为十六进制颜色代码
        const hexColors = palette.map((rgbArray: number[]) => rgbToHex(rgbArray))

        return {
            hexColors,
            rgbColors: palette,
            success: true,
        }
    } catch (error) {
        return {
            hexColors: [],
            rgbColors: [],
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
        }
    }
}

/**
 * 从图片 URL 中提取颜色调色板
 *
 * @param {string} imageUrl - 图片 URL
 * @param {ColorExtractionOptions} options - 提取配置选项
 * @returns {Promise<ColorExtractionResult>} 颜色提取结果
 */
async function extractColorsFromUrl(
    imageUrl: string,
    options: ColorExtractionOptions = {},
): Promise<ColorExtractionResult> {
    const { colorCount = 5, crossOrigin = 'anonymous' } = options

    return new Promise(resolve => {
        const img = new Image()

        // 设置跨域属性
        if (crossOrigin) {
            img.crossOrigin = typeof crossOrigin === 'string' ? crossOrigin : 'anonymous'
        }

        img.onload = async () => {
            try {
                const result = await extractColorsFromImageElement(img, { colorCount })
                resolve(result)
            } catch (error) {
                resolve({
                    hexColors: [],
                    rgbColors: [],
                    success: false,
                    error: error instanceof Error ? error.message : '处理图片时出错',
                })
            }
        }

        img.onerror = () => {
            resolve({
                hexColors: [],
                rgbColors: [],
                success: false,
                error: '图片加载失败，请检查图片路径是否正确',
            })
        }

        img.src = imageUrl
    })
}

/**
 * 从文件对象中提取颜色调色板
 *
 * @param {File} file - 图片文件对象
 * @param {ColorExtractionOptions} options - 提取配置选项
 * @returns {Promise<ColorExtractionResult>} 颜色提取结果
 */
async function extractColorsFromFile(
    file: File,
    options: ColorExtractionOptions = {},
): Promise<ColorExtractionResult> {
    return new Promise(resolve => {
        if (!file.type.startsWith('image/')) {
            resolve({
                hexColors: [],
                rgbColors: [],
                success: false,
                error: '请选择有效的图片文件',
            })
            return
        }

        const reader = new FileReader()

        reader.onload = async e => {
            const imageUrl = e.target?.result as string
            if (!imageUrl) {
                resolve({
                    hexColors: [],
                    rgbColors: [],
                    success: false,
                    error: '无法读取文件',
                })
                return
            }

            try {
                const result = await extractColorsFromUrl(imageUrl, options)
                resolve(result)
            } catch (error) {
                resolve({
                    hexColors: [],
                    rgbColors: [],
                    success: false,
                    error: error instanceof Error ? error.message : '处理文件时出错',
                })
            }
        }

        reader.onerror = () => {
            resolve({
                hexColors: [],
                rgbColors: [],
                success: false,
                error: '文件读取失败',
            })
        }

        reader.readAsDataURL(file)
    })
}

/**
 * 主颜色提取函数 - 支持多种输入类型
 *
 * @param {string | File | HTMLImageElement} input - 图片输入（URL、文件对象或图片元素）
 * @param {ColorExtractionOptions} options - 提取配置选项
 * @returns {Promise<ColorExtractionResult>} 颜色提取结果
 */
export async function extractColors(
    input: string | File | HTMLImageElement,
    options: ColorExtractionOptions = {},
): Promise<ColorExtractionResult> {
    if (typeof input === 'string') {
        // 处理 URL 字符串
        return extractColorsFromUrl(input, options)
    } else if (input instanceof File) {
        // 处理文件对象
        return extractColorsFromFile(input, options)
    } else if (input instanceof HTMLImageElement) {
        // 处理图片元素
        return extractColorsFromImageElement(input, options)
    } else {
        return {
            hexColors: [],
            rgbColors: [],
            success: false,
            error: '不支持的输入类型',
        }
    }
}

// 导出所有功能函数供不同场景使用
export const imageToColors = {
    extractColors,
    extractColorsFromUrl,
    extractColorsFromFile,
    extractColorsFromImageElement,
    rgbToHex,
}
