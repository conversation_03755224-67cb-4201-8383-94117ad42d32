import { Public_MockUp_SizeSelector } from './Public_MockUp_SizeSelector'

/**
 * MobileFrame 标签页的属性接口
 * @interface MobileFrame_TabsProps
 */
interface MobileFrame_TabsProps {
    /** 当前激活的标签 */
    activeTab: string
    /** 设置激活标签的回调函数 */
    setActiveTab: (tab: string) => void
    /** 是否激活尺寸选择器模态框 */
    isActiveModel: boolean
    /** 设置尺寸选择器模态框状态的回调函数 */
    setIsActiveModel: (isActiveModel: boolean) => void
}

/**
 * 标签配置接口
 * @interface TabConfig
 */
interface TabConfig {
    /** 标签标识 */
    label: string
    /** 渲染内容的函数 */
    renderContent: () => React.ReactElement
    /** 是否有变化指示器 */
    hasChanged?: boolean
}

/**
 * MobileFrame 标签页组件
 * @param props MobileFrame_TabsProps
 * @returns React.ReactElement
 */
export const MobileFrame_Tabs = ({
    activeTab,
    setActiveTab,
    isActiveModel,
    setIsActiveModel,
}: MobileFrame_TabsProps) => {
    /** 移动端框架标签配置数组 */
    const mobileFrameTabs: TabConfig[] = [
        {
            label: 'scene',
            hasChanged: true,
            renderContent: () => (
                <>
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            fillRule='evenodd'
                            d='M7.775 17.824A7.5 7.5 0 1 0 17.824 7.775q.174.837.176 1.724a8.5 8.5 0 0 1-10.225 8.325'
                            opacity='0.2'
                        />
                        <rect width={15} height={15} x={2} y={2} fill='currentColor' rx='7.5' />
                    </svg>
                    <span>scene</span>
                </>
            ),
        },
        {
            label: 'effects',
            renderContent: () => (
                <>
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M12 20c.281 0 .501-.197.541-.493.66-5.74 1.325-6.409 6.95-6.976.298-.025.509-.244.509-.531s-.211-.507-.509-.538c-5.625-.561-6.29-1.23-6.95-6.976A.534.534 0 0 0 12 4a.54.54 0 0 0-.542.486c-.66 5.746-1.325 6.415-6.95 6.976-.298.031-.508.25-.508.538 0 .287.21.498.508.531 5.602.713 6.231 1.219 6.95 6.976.042.296.266.493.542.493'
                        />
                    </svg>
                    <span>effects</span>
                </>
            ),
        },
        {
            label: 'custom',
            renderContent: () => (
                <>
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='m14.438 10.609-3.542 3.559c-.316.315-.719.425-1.051.073-.34-.377-.269-.724.088-1.088l3.513-3.565-.533-.536-6.364 6.403C4.198 17.823 5.279 17.51 4 19.326l.627.674c1.744-1.281 1.556-.072 3.946-2.466l6.392-6.398zm1.621 1.407.189-.211c.354-.382.378-.827-.01-1.215L16 10.351c1.163-1.051 2.484-1.223 3.283-2.042 1.132-1.147.769-2.759-.031-3.564-.794-.807-2.377-1.152-3.54-.031-.82.797-.984 2.135-2.028 3.306l-.235-.24c-.379-.385-.829-.364-1.209-.01l-.204.19c-.463.43-.367.833.018 1.223l2.79 2.81c.391.393.793.484 1.215.023'
                        />
                    </svg>
                    <span>custom</span>
                </>
            ),
        },
        {
            label: 'magic',
            renderContent: () => (
                <>
                    <span>Magic</span>
                    <div className='backpack-preview'>
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='image/mobile-magic-icon.png'
                        />
                        <div style={{ background: 'rgb(248, 249, 250)' }} />
                    </div>
                </>
            ),
        },
        {
            label: 'solid',
            renderContent: () => (
                <>
                    <span>Solid Color</span>
                    <div className='backpack-preview'>
                        <div style={{ background: 'rgb(248, 249, 250)' }} />
                        <div style={{ background: 'rgb(222, 226, 230)' }} />
                    </div>
                </>
            ),
        },
        {
            label: 'gradient',
            renderContent: () => (
                <>
                    <span>Gradient</span>
                    <div className='backpack-preview'>
                        <div
                            style={{
                                background:
                                    'linear-gradient(140deg, rgb(255, 100, 50) 12.8%, rgb(255, 0, 101) 43.52%, rgb(123, 46, 255) 84.34%)',
                            }}
                        />
                        <div
                            style={{
                                background:
                                    'linear-gradient(140deg, rgb(244, 229, 240), rgb(229, 54, 171), rgb(92, 3, 188), rgb(14, 7, 37))',
                            }}
                        />
                    </div>
                </>
            ),
        },
        {
            label: 'Cosmic Gradient',
            renderContent: () => (
                <>
                    <span>Cosmic Gradient</span>
                    <div className='backpack-preview'>
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/preview/cosmic/1.jpg'
                        />
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/preview/cosmic/2.jpg'
                        />
                    </div>
                </>
            ),
        },
        {
            label: 'Mystic Gradient',
            renderContent: () => (
                <>
                    <span>Mystic Gradient</span>
                    <div className='backpack-preview'>
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/mystic-gradients/preview/9.jpg'
                        />
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/mystic-gradients/preview/10.jpg'
                        />
                    </div>
                </>
            ),
        },
        {
            label: 'Desktop Background',
            renderContent: () => (
                <>
                    <span>Desktop Background</span>
                    <div className='backpack-preview'>
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/preview/desktop/sequoia-light.jpeg'
                        />
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/preview/desktop/sonoma-light.jpg'
                        />
                    </div>
                </>
            ),
        },
        {
            label: 'Radiant Gradient',
            renderContent: () => (
                <>
                    <span>Radiant Gradient</span>
                    <div className='backpack-preview'>
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/preview/radiant/1.jpg'
                        />
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/preview/radiant/2.jpg'
                        />
                    </div>
                </>
            ),
        },
    ]

    /**
     * 处理标签点击事件
     * @param tab 点击的标签配置
     */
    const handleTabClick = (tab: TabConfig): void => {
        if (activeTab === tab.label) {
            setActiveTab('')
            return
        }

        if (isActiveModel) {
            setIsActiveModel(false)
        }
        setActiveTab(tab.label)
    }

    /**
     * 生成按钮的CSS类名
     * @param tab 标签配置
     * @returns 完整的CSS类名字符串
     */
    /**
     * 生成按钮的CSS类名
     * @param tab 标签配置
     * @returns 完整的CSS类名字符串
     */
    const getButtonClassName = (tab: TabConfig): string => {
        // 背包项目类名判断逻辑保持不变
        const backpackClassName =
            tab.label.includes('Magic') ||
            tab.label.includes('Color') ||
            tab.label.includes('Gradient') ||
            tab.label.includes('Background')
                ? 'mobile-backpack-item'
                : ''

        // 简化其他类名的拼接
        return `button default-button large-button undefined-button undefined-blur undefined-round ${activeTab === tab.label ? 'true-active' : 'false-active'} mobile-control-switcher-button ${backpackClassName}`.trim()
    }

    return (
        <div className='panel-control-switcher'>
            <div className='stack'>
                <Public_MockUp_SizeSelector
                    isActiveModel={isActiveModel}
                    setIsActiveModel={setIsActiveModel}
                />
                <div className='divider' />

                {mobileFrameTabs.map(tab => (
                    <button
                        key={tab.label}
                        type='button'
                        className={getButtonClassName(tab)}
                        style={{ flexDirection: 'row' }}
                        onClick={() => handleTabClick(tab)}
                    >
                        {tab.renderContent()}
                        {tab.hasChanged && <div className='is-changed' />}
                    </button>
                ))}
            </div>
        </div>
    )
}
