/**
 * @file app/config/ImageSources.ts
 * @description 定义一个全局共享的图片源数组。
 * 这个文件作为所有设备显示内容的唯一真实来源，
 * 将图片内容与布局结构解耦。
 */

/**
 * @const globalImageSources
 * @description 一个包含多个图片URL的数组。
 * 索引起始位置的图片将用于单设备、双设备和三设备布局。
 * - 索引 0: 用于单设备布局，以及双/三设备布局中的第一个设备。
 * - 索引 1: 用于双设备布局的第二个设备，以及三设备布局的第二个设备。
 * - 索引 2: 用于三设备布局的第三个设备。
 */
export const globalImageSources = [
    '/__壁纸测试/walller__1.jpg',
    '/__壁纸测试/walller__2.jpg',
    '/__壁纸测试/walller__4.jpg',
]
