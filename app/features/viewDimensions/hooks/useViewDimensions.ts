import {
    RatioType,
    browserAllSizes,
    cptWidthAndHeight,
    getDimensionsForSelectedType,
    testAllAspectRatios,
} from '@/app/features/viewDimensions/utils/viewAlgorithm'
import { useEffect, useState } from 'react'

/**
 * 使用视图尺寸的自定义Hook
 *
 * 该Hook管理视图尺寸的相关状态和计算
 *
 * @param defaultDeviceMode 默认设备模式
 * @param dependencies 额外的依赖项，当这些值变化时会触发重新计算
 * @returns 视图尺寸状态和操作方法
 */
export function useViewDimensions(defaultDeviceMode: 'pc' | 'mobile' = 'pc', dependencies?: any) {
    // 新增：设备模式
    const [deviceMode, setDeviceMode] = useState<'pc' | 'mobile'>(defaultDeviceMode)

    // 维护当前展示尺寸的状态
    const [selfSize, setSelfSize] = useState({ width: 0, height: 0 })

    // 默认选择type3(4:3比例)作为初始比例类型
    const [typeSelected, setTypeSelected] = useState<RatioType>('type3')

    // 方案选择状态：1为使用预设尺寸，2为使用动态计算尺寸
    const [sizeOption, setSizeOption] = useState<1 | 2>(2)

    // 测试结果状态
    const [testResults, setTestResults] = useState<any[]>([])

    // 新增：自定义尺寸相关状态
    const [customUseWidth, setCustomUseWidth] = useState<number>(1920)
    const [customUseHeight, setCustomUseHeight] = useState<number>(1080)
    const [useCustomDimensions, setUseCustomDimensions] = useState<boolean>(false)

    // 获取当前尺寸集合
    const getCurrentBrowserSizes = () => {
        return deviceMode === 'pc'
            ? browserAllSizes.macAirBrowserPcSizes
            : browserAllSizes.macAirBrowserMobileSizes
    }

    /**
     * 初始化和比例类型变更时的副作用
     *
     * 1. 在客户端渲染时调用testAllRatioTypes获取测试结果
     * 2. 根据当前选择的方案计算尺寸并更新状态
     */
    useEffect(() => {
        // 当浏览器窗口尺寸变化或比例类型变化时重新计算尺寸
        const handleResize = () => {
            console.log('窗口大小变化，重新计算尺寸...')
            const { width, height } = getDimensionsForSelectedType(typeSelected, {
                typeSelected,
                presets: getCurrentBrowserSizes(),
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setSelfSize({ width, height })
        }

        // 立即执行一次计算
        handleResize()

        // 对于选项1(使用预设尺寸)，执行所有比例类型的测试
        if (sizeOption === 1) {
            const results = testAllAspectRatios(sizeOption, {
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setTestResults(results)
        } else {
            setTestResults([])
        }

        // 添加窗口大小改变事件监听器
        window.addEventListener('resize', handleResize)

        // 组件销毁时移除事件监听器
        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [
        typeSelected,
        sizeOption,
        deviceMode,
        dependencies,
        // 添加自定义尺寸相关依赖项
        useCustomDimensions,
        customUseWidth,
        customUseHeight,
    ]) // 添加新的依赖项

    // 切换类型的处理函数
    const handleTypeChange = (type: RatioType) => {
        setTypeSelected(type)

        // 根据当前选择的方案设置尺寸
        if (sizeOption === 1) {
            // 方案1：使用预设尺寸
            const size = cptWidthAndHeight(type, {
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setSelfSize(size)
        } else {
            // 方案2：使用动态计算尺寸
            const calcResult = getDimensionsForSelectedType(type, {
                typeSelected: type,
                presets: getCurrentBrowserSizes(),
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setSelfSize({
                width: calcResult.width,
                height: calcResult.height,
            })
        }
    }

    // 切换尺寸选项
    const handleToggleSizeOption = (option: 1 | 2) => {
        setSizeOption(option)

        // 根据选择的计算方式设置尺寸
        if (option === 1) {
            // 方案1：使用预设尺寸
            const size = cptWidthAndHeight(typeSelected, {
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setSelfSize(size)
            // 执行测试
            const results = testAllAspectRatios(option, {
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setTestResults(results)
        } else {
            // 方案2：使用动态计算尺寸
            const calcResult = getDimensionsForSelectedType(typeSelected, {
                typeSelected,
                presets: getCurrentBrowserSizes(),
                deviceMode,
                // 如果使用自定义尺寸，传递自定义尺寸数据
                customDimensions: useCustomDimensions
                    ? { useWidth: customUseWidth, useHeight: customUseHeight }
                    : undefined,
            })
            setSelfSize({
                width: calcResult.width,
                height: calcResult.height,
            })
            // 清空测试结果
            setTestResults([])
        }
    }

    // 新增：切换设备模式
    const handleDeviceModeChange = (mode: 'pc' | 'mobile') => {
        setDeviceMode(mode)
    }

    // 新增：自定义宽度变更处理函数
    const handleCustomWidthChange = (width: number) => {
        setCustomUseWidth(width)
    }

    // 新增：自定义高度变更处理函数
    const handleCustomHeightChange = (height: number) => {
        setCustomUseHeight(height)
    }

    // 新增：切换使用自定义尺寸状态
    const toggleCustomDimensions = (value: boolean) => {
        setUseCustomDimensions(value)
    }

    return {
        selfSize,
        typeSelected,
        sizeOption,
        testResults,
        deviceMode,
        // 新增：自定义尺寸相关状态和函数
        customUseWidth,
        customUseHeight,
        useCustomDimensions,
        // 处理函数
        handleTypeChange,
        handleToggleSizeOption,
        handleDeviceModeChange,
        // 新增：自定义尺寸处理函数
        handleCustomWidthChange,
        handleCustomHeightChange,
        toggleCustomDimensions,
    }
}
