/**
 * 视图尺寸功能模块导出文件
 *
 * 集中导出视图尺寸相关的所有组件、hooks和工具函数
 */

// 导出工具函数
export {
    browserAllSizes,
    calculateDimensionsForRatio,
    cptWidthAndHeight,
    getAspectRatioTypes,
    getAvailableSpace,
    getDimensionsForSelectedType,
    getPageDistances,
    getReferenceInfo,
    innnerSize,
    matchPresetsForCurrentWindow,
    testAllAspectRatios,
    type AllBrowserSizes,
    type BrowserSizes,
    type RatioType,
    type SizeType,
} from './utils/viewAlgorithm'

// 导出hooks
export { useViewDimensions } from './hooks/useViewDimensions'
