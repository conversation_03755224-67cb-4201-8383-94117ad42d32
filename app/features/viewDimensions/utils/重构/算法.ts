import { getAvailableSpace, setNeedViewDimension } from './获取'

/**
 * 接口定义：可用空间的尺寸
 *
 * 表示容器的可用宽度和高度
 */
export type AvailableSpace = {
    /** 可用宽度 */
    width: number
    /** 可用高度 */
    height: number
}

/**
 * 接口定义：参考信息
 *
 * 包含目标内容的实际尺寸信息
 */
export type ReferenceInfo = {
    /** 目标内容宽度 */
    useWidth: number
    /** 目标内容高度 */
    useHeight: number
}

/**
 * 接口定义：计算结果
 *
 * 包含计算出的尺寸和比例信息
 */
export type CalculationResult = {
    /** 计算出的宽度 */
    width: number
    /** 计算出的高度 */
    height: number
    /** 使用的计算方法 */
    method: string
}

/**
 * 计算基于参考信息和可用空间的最佳显示尺寸
 *
 * 这是一个核心算法函数，根据目标内容的尺寸和可用空间，计算出最合适的显示尺寸
 *
 * @param {AvailableSpace} availableSpace - 可用空间的宽度和高度
 * @param {ReferenceInfo | null} referenceInfo - 目标内容的宽度和高度信息
 * @returns {CalculationResult} 计算结果，包含宽度、高度和使用的计算方法
 */
export const calculateDimensions = (
    availableSpace: AvailableSpace,
    referenceInfo: ReferenceInfo | null,
): CalculationResult => {
    console.log('可用空间参数尺寸：', availableSpace)
    // 确保传入的参数有效
    if (!availableSpace || !availableSpace.width || !availableSpace.height) {
        throw new Error('可用空间参数无效')
    }

    // 声明用于存储计算结果的变量
    let finalSize: { width: number; height: number }
    let method = ''

    // 基于referenceInfo的缩放计算 (主要方法)
    if (referenceInfo && referenceInfo.useWidth && referenceInfo.useHeight) {
        // 计算可用空间与目标尺寸的宽度和高度缩放比例
        const widthScale = availableSpace.width / referenceInfo.useWidth
        const heightScale = availableSpace.height / referenceInfo.useHeight

        // 选择较小的缩放因子以确保完全适应可用空间
        const scalingFactor = Math.min(widthScale, heightScale)
        method = widthScale < heightScale ? '算法1：基于宽度比例缩放' : '算法2：基于高度比例缩放'

        // 根据缩放因子计算尺寸
        finalSize = {
            width: Math.round(referenceInfo.useWidth * scalingFactor),
            height: Math.round(referenceInfo.useHeight * scalingFactor),
        }
    } else {
        // 如果没有参考信息，使用默认值
        const defaultWidth = Math.min(500, availableSpace.width)
        const defaultHeight = Math.min(300, availableSpace.height)

        finalSize = {
            width: defaultWidth,
            height: defaultHeight,
        }
        method = '算法0：使用默认尺寸(无参考信息)'
    }

    // 确保尺寸为整数并不超过可用空间
    finalSize = {
        width: Math.min(Math.round(finalSize.width), availableSpace.width),
        height: Math.min(Math.round(finalSize.height), availableSpace.height),
    }

    const result = {
        width: finalSize.width,
        height: finalSize.height,
        method: method,
    }
    console.log('计算后的用户空间尺寸', result)
    // 返回结果
    return result
}

/**
 * 更新视图尺寸
 * @param {ReferenceInfo | null} referenceInfo - 目标内容的宽度和高度信息
 * @returns
 */
export const updateViewDimension = (referenceInfo: ReferenceInfo) => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('updateViewDimension: SSR环境中无法更新视图尺寸')
        return
    }

    const availableSpace = getAvailableSpace()
    if (!availableSpace) {
        console.error('availableSpace is null')
        return
    }

    const sizes = calculateDimensions(availableSpace, referenceInfo)
    setNeedViewDimension(sizes.width, sizes.height)
}
