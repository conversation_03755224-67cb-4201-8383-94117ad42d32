/**
 * 获取元素的尺寸和相对于页面的位置信息
 *
 * 该函数返回元素的宽高以及相对于页面四边的距离，
 * 考虑了页面滚动的影响
 *
 * @param element 目标HTML元素
 * @returns 包含元素宽高和位置信息的对象
 */
export const getPageDistances = (element: HTMLElement) => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        throw new Error('getPageDistances 只能在客户端环境中使用')
    }

    // 获取元素的位置和尺寸信息
    const rect = element.getBoundingClientRect()

    // 获取页面滚动位置
    const { scrollX, scrollY } = window

    // 获取文档整体尺寸
    const { scrollWidth, scrollHeight } = document.documentElement

    // 直接从rect获取元素宽高
    const { width, height } = element.getBoundingClientRect()

    // 返回完整的元素尺寸和位置信息
    return {
        width, // 元素的宽度
        height, // 元素的高度
        top: rect.top + scrollY, // 元素距离页面顶部的距离
        left: rect.left + scrollX, // 元素距离页面左侧的距离
        right: scrollWidth - (rect.left + scrollX + rect.width), // 元素距离页面右侧的距离
        bottom: scrollHeight - (rect.top + scrollY + rect.height), // 元素距离页面底部的距离
    }
}
/**
 * 获取可用空间信息算法
 *
 * 该函数获取Canvas安全区域元素并计算可用空间
 *
 * @returns {{width: number, height: number} | null} 可用空间尺寸或null（如果无法获取）
 */
export const getAvailableSpace = () => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('getAvailableSpace: SSR环境中无法获取可用空间')
        return null
    }

    // 获取Canvas安全区域元素
    const canvasSafeArea = document.querySelector('.canvas-safe-area') as HTMLElement
    if (!canvasSafeArea) {
        console.error('获取Canvas安全区域元素错误')
        return null
    }

    // 获取元素尺寸和位置信息
    const metrics = getPageDistances(canvasSafeArea)
    // console.log('Canvas安全区域指标:', metrics, canvasSafeArea)

    // 返回可用空间尺寸
    return {
        element: canvasSafeArea,
        width: metrics.width,
        height: metrics.height,
    }
}

/**
 * 获取Canvas展示为用户实际看到的元素
 * 现在专门用于获取导出用的 captureCanvas 元素
 * @returns {{element: HTMLElement, width: number, height: number} | null} 返回Canvas展示为用户实际看到的元素或null（如果无法获取）
 */
export const getCanvasFrame = () => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('getCanvasFrame: SSR环境中无法获取Canvas元素')
        return null
    }

    // 修改为查找导出用的 captureCanvas 元素，而不是预览用的 preview-frame
    // const canvasFrame = document.querySelector('.canvas__frame') as HTMLElement
    // const canvasFrame = document.querySelector('.preview-frame') as HTMLElement
    const canvasFrame = document.querySelector('#captureCanvas') as HTMLElement
    console.log('🚀 导出用画布元素获取:', canvasFrame)
    if (!canvasFrame) {
        console.error('获取导出用 captureCanvas 元素失败', canvasFrame)
        return null
    }

    // 获取元素尺寸和位置信息
    const metrics = getPageDistances(canvasFrame)

    // 返回元素尺寸和位置信息
    return {
        element: canvasFrame,
        width: metrics.width,
        height: metrics.height,
    }
}

/**
 * 获取预览画布元素（用于视图尺寸计算）
 * 专门用于获取用户在界面上看到的预览画布，不影响导出功能
 * @returns {{element: HTMLElement, width: number, height: number} | null} 返回预览画布元素或null（如果无法获取）
 */
export const getPreviewFrame = () => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('getPreviewFrame: SSR环境中无法获取预览Canvas元素')
        return null
    }

    // 查找预览用的 preview-frame 元素
    const previewFrame = document.querySelector('.preview-frame') as HTMLElement
    console.log('🖼️ 预览用画布元素获取:', previewFrame)
    if (!previewFrame) {
        console.error('获取预览用 preview-frame 元素失败', previewFrame)
        return null
    }

    // 获取元素尺寸和位置信息
    const metrics = getPageDistances(previewFrame)

    // 返回元素尺寸和位置信息
    return {
        element: previewFrame,
        width: metrics.width,
        height: metrics.height,
    }
}

/**
 * 设置的算法计算后呈现给用户元素
 *
 * 该函数获取需要设置的元素尺寸信息
 * 现在使用预览画布而不是导出画布
 *
 * @param {number} width - 需要设置的元素宽度
 * @param {number} height - 需要设置的元素高度
 * @returns {{element: HTMLElement, width: number, height: number} | null}
 */
export const setNeedViewDimension = (width: number, height: number) => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('setNeedViewDimension: SSR环境中无法设置元素尺寸')
        return null
    }

    // 使用预览画布而不是导出画布，因为这个函数用于设置用户看到的预览尺寸
    const frame = getPreviewFrame()
    if (!frame) {
        console.error('获取需要设置的预览元素尺寸信息错误')
        return null
    }

    const canvasFrame = frame.element
    // 设置新的元素尺寸
    canvasFrame.style.width = `${width}px`
    canvasFrame.style.height = `${height}px`

    // 获取新的元素尺寸和位置信息
    const newCanvasFrame = getPageDistances(canvasFrame)
    return {
        element: canvasFrame,
        width: newCanvasFrame.width,
        height: newCanvasFrame.height,
    }
}
