export enum boxKey {
    user_custom = 'user_custom', // 用户自定义
}
// 使用尺寸
interface useSize {
    useWidth: number // 宽度
    useHeight: number // 高度
}
// 比例尺寸
interface ratioSize {
    ratioWidth: number // 宽度比例
    ratioHeight: number // 高度比例
}
// 尺寸信息
export interface sizeInfo extends useSize, ratioSize {
    key: string // 唯一标识
}
/**
 * 使用尺寸和比例尺寸的组合
 */
interface useBox extends sizeInfo {
    title: string // 标题
}
/**
 * 视图容器
 */
export interface viewContainer {
    name: string // 名称
    cover: string // 封面
    title: string // 标题
    sizes: useBox[] // 尺寸
}
/**
 * 视图尺寸
 */
export const viewSizes: viewContainer[] = [
    {
        name: 'Default',
        cover: '',
        title: '',
        sizes: [
            {
                key: 'default_1920x1080',
                title: '',
                useWidth: 1920,
                useHeight: 1080,
                ratioWidth: 16,
                ratioHeight: 9,
            },
            {
                key: 'default_1920x1280',
                title: '',
                useWidth: 1920,
                useHeight: 1280,
                ratioWidth: 3,
                ratioHeight: 2,
            },
            {
                key: 'default_1920x1440',
                title: '',
                useWidth: 1920,
                useHeight: 1440,
                ratioWidth: 4,
                ratioHeight: 3,
            },
            {
                key: 'default_1920x1536',
                title: '',
                useWidth: 1920,
                useHeight: 1536,
                ratioWidth: 5,
                ratioHeight: 4,
            },
            {
                key: 'default_1920x1920',
                title: '',
                useWidth: 1920,
                useHeight: 1920,
                ratioWidth: 1,
                ratioHeight: 1,
            },
            {
                key: 'default_1080x1350',
                title: '',
                useWidth: 1080,
                useHeight: 1350,
                ratioWidth: 4,
                ratioHeight: 5,
            },
            {
                key: 'default_1080x1440',
                title: '',
                useWidth: 1080,
                useHeight: 1440,
                ratioWidth: 3,
                ratioHeight: 4,
            },
            {
                key: 'default_1080x1620',
                title: '',
                useWidth: 1080,
                useHeight: 1620,
                ratioWidth: 2,
                ratioHeight: 3,
            },
            {
                key: 'default_1080x1920',
                title: '',
                useWidth: 1080,
                useHeight: 1920,
                ratioWidth: 9,
                ratioHeight: 16,
            },
        ],
    },
    {
        name: 'Instagram',
        cover: '/icon/socials/instagram.png',
        title: 'Instagram', // 如果是默认其他的，这里就是空
        sizes: [
            {
                key: 'instagram_post',
                title: 'Post',
                useWidth: 1080,
                useHeight: 1080,
                ratioWidth: 1,
                ratioHeight: 1,
            },
            {
                key: 'instagram_portrait',
                title: 'Portrait',
                useWidth: 1080,
                useHeight: 1350,
                ratioWidth: 4,
                ratioHeight: 5,
            },
            {
                key: 'instagram_story',
                title: 'Story',
                useWidth: 1080,
                useHeight: 1920,
                ratioWidth: 9,
                ratioHeight: 16,
            },
            {
                key: 'instagram_xiaohongshu',
                title: '小红书',
                useWidth: 1200,
                useHeight: 1600,
                ratioWidth: 3,
                ratioHeight: 4,
            },
            {
                key: 'instagram_kuaishou',
                title: '快手',
                useWidth: 1200,
                useHeight: 675,
                ratioWidth: 16,
                ratioHeight: 9,
            },
        ],
    },
    {
        name: 'Twitter',
        cover: '/icon/socials/instagram.png',
        title: 'Twitter',
        sizes: [
            {
                key: 'twitter_tweet',
                title: 'Tweet',
                useWidth: 1200,
                useHeight: 675,
                ratioWidth: 16,
                ratioHeight: 9,
            },
            {
                key: 'twitter_cover',
                title: 'Cover',
                useWidth: 1500,
                useHeight: 500,
                ratioWidth: 3,
                ratioHeight: 1,
            },
        ],
    },
    {
        name: 'Dribbble',
        cover: '/icon/socials/instagram.png',
        title: 'Dribbble',
        sizes: [
            {
                key: 'dribbble_shot',
                title: 'Shot',
                useWidth: 2800,
                useHeight: 2100,
                ratioWidth: 4,
                ratioHeight: 3,
            },
        ],
    },
    {
        name: 'YouTube',
        cover: '/icon/socials/instagram.png',
        title: 'YouTube',
        sizes: [
            {
                key: 'youtube_banner',
                title: 'Banner',
                useWidth: 2560,
                useHeight: 1440,
                ratioWidth: 16,
                ratioHeight: 9,
            },
            {
                key: 'youtube_thumbnail',
                title: 'Thumbnail',
                useWidth: 1280,
                useHeight: 720,
                ratioWidth: 16,
                ratioHeight: 9,
            },
            {
                key: 'youtube_video',
                title: 'Video',
                useWidth: 1920,
                useHeight: 1080,
                ratioWidth: 16,
                ratioHeight: 9,
            },
        ],
    },
    {
        name: 'Pinterest',
        cover: '/icon/socials/instagram.png',
        title: 'Pinterest',
        sizes: [
            {
                key: 'pinterest_long',
                title: 'Long',
                useWidth: 1000,
                useHeight: 2100,
                ratioWidth: 10,
                ratioHeight: 21,
            },
            {
                key: 'pinterest_optimal',
                title: 'Optimal',
                useWidth: 1000,
                useHeight: 1500,
                ratioWidth: 2,
                ratioHeight: 3,
            },
            {
                key: 'pinterest_square',
                title: 'Square',
                useWidth: 1000,
                useHeight: 1000,
                ratioWidth: 1,
                ratioHeight: 1,
            },
        ],
    },
    {
        name: 'App Store',
        cover: '/icon/socials/instagram.png',
        title: 'App Store',
        sizes: [
            {
                key: 'appstore_iphone65',
                title: 'iPhone 6.5"',
                useWidth: 1284,
                useHeight: 2778,
                ratioWidth: 1284,
                ratioHeight: 2778,
            },
            {
                key: 'appstore_iphone55',
                title: 'iPhone 5.5"',
                useWidth: 1242,
                useHeight: 2208,
                ratioWidth: 1242,
                ratioHeight: 2208,
            },
            {
                key: 'appstore_ipadpro129',
                title: 'iPad Pro 12.9"',
                useWidth: 2048,
                useHeight: 2732,
                ratioWidth: 2048,
                ratioHeight: 2732,
            },
            {
                key: 'appstore_iphone65_landscape',
                title: 'iPhone 6.5" 横屏',
                useWidth: 2778,
                useHeight: 1284,
                ratioWidth: 2778,
                ratioHeight: 1284,
            },
            {
                key: 'appstore_iphone55_landscape',
                title: 'iPhone 5.5" 横屏',
                useWidth: 2208,
                useHeight: 1242,
                ratioWidth: 2208,
                ratioHeight: 1242,
            },
            {
                key: 'appstore_ipadpro129_landscape',
                title: 'iPad Pro 12.9" 横屏',
                useWidth: 2732,
                useHeight: 2048,
                ratioWidth: 2732,
                ratioHeight: 2048,
            },
            {
                key: 'appstore_mac',
                title: 'Mac',
                useWidth: 2880,
                useHeight: 1800,
                ratioWidth: 16,
                ratioHeight: 10,
            },
        ],
    },
]
