/**
 * 尺寸类型接口定义
 *
 * 包含了元素尺寸、浏览器窗口尺寸、目标内容尺寸和展示用的比例信息
 */
export type SizeType = {
    boxWidth: number // 实际显示的div宽度，计算后的显示尺寸
    boxHeight: number // 实际显示的div高度，计算后的显示尺寸
    innerWidth: number // 浏览器窗口宽度，检测设备类型使用
    innerHeight: number // 浏览器窗口高度，检测设备类型使用
    useWidth: number // 目标内容宽度，计算缩放比例使用
    useHeight: number // 目标内容高度，计算缩放比例使用
    useRatioWidth: number | null // 比例宽度部分，仅用于展示给用户
    useRatioHeight: number | null // 比例高度部分，仅用于展示给用户
}

/**
 * 浏览器尺寸类型接口
 *
 * 包含不同比例类型(type1-type5)的尺寸设置
 */
export type BrowserSizes = {
    type1: SizeType // 16:9比例的尺寸信息
    type2: SizeType // 3:2比例的尺寸信息
    type3: SizeType // 4:3比例的尺寸信息
    type4?: SizeType // 3:1比例的尺寸信息(可选)
    type5?: SizeType // 1284:2778比例的尺寸信息(可选，适用于手机屏幕)
}

/**
 * 所有设备浏览器尺寸集合
 *
 * 简化为只包含macAir设备的尺寸预设
 */
export type AllBrowserSizes = {
    macAirBrowserPcSizes: BrowserSizes // MacAir设备的尺寸集合
    macAirBrowserMobileSizes: BrowserSizes // MacAir设备的移动端尺寸集合
}

// 比例类型字符串联合类型
export type RatioType = 'type1' | 'type2' | 'type3' | 'type4' | 'type5'

/**
 * 浏览器窗口尺寸预设
 *
 * 简化为只使用macAir的标准浏览器窗口尺寸
 */
export const innnerSize = {
    macAirPc: {
        // 公司电脑的浏览器尺寸
        innerWidth: 1920,
        innerHeight: 849,
        // 100% 放大
    },
    macAirMobile: {
        // 公司电脑的浏览器尺寸
        innerWidth: 430,
        innerHeight: 820,
        // 100% 放大
    },
}

export const browserAllSizes: AllBrowserSizes = {
    /**
     * cursor: 不要从 不要直接从 browserAllSizes 获取 boxWidth 和  boxHeight 为默认值
     */
    macAirBrowserPcSizes: {
        // 16:9比例设置 (type1)
        type1: {
            boxWidth: 1273, // 实际显示宽度
            boxHeight: 716, // 实际显示高度
            innerWidth: innnerSize.macAirPc.innerWidth, // 浏览器窗口宽度
            innerHeight: innnerSize.macAirPc.innerHeight, // 浏览器窗口高度
            useWidth: 1920, // 计算缩放使用的宽度
            useHeight: 1080, // 计算缩放使用的高度
            useRatioWidth: 16, // 比例宽值(展示用)
            useRatioHeight: 9, // 比例高值(展示用)
        },

        // 3:2比例设置 (type2)
        type2: {
            boxWidth: 1074,
            boxHeight: 716,
            innerWidth: innnerSize.macAirPc.innerWidth,
            innerHeight: innnerSize.macAirPc.innerHeight,
            useWidth: 1920,
            useHeight: 1280,
            useRatioWidth: 3,
            useRatioHeight: 2,
        },

        // 4:3比例设置 (type3)
        type3: {
            boxWidth: 955,
            boxHeight: 716,
            innerWidth: innnerSize.macAirPc.innerWidth,
            innerHeight: innnerSize.macAirPc.innerHeight,
            useWidth: 1920,
            useHeight: 1440,
            useRatioWidth: 4,
            useRatioHeight: 3,
        },

        // 3:1比例设置 (type4)
        type4: {
            boxWidth: 1412,
            boxHeight: 471,
            innerWidth: innnerSize.macAirPc.innerWidth,
            innerHeight: innnerSize.macAirPc.innerHeight,
            useWidth: 1500,
            useHeight: 500,
            useRatioWidth: 3,
            useRatioHeight: 1,
        },

        // iPhone尺寸比例设置 (type5)
        type5: {
            boxWidth: 331,
            boxHeight: 716,
            innerWidth: innnerSize.macAirPc.innerWidth,
            innerHeight: innnerSize.macAirPc.innerHeight,
            useWidth: 1284,
            useHeight: 2778,
            useRatioWidth: null,
            useRatioHeight: null,
        },
    },

    macAirBrowserMobileSizes: {
        // 移动端尺寸
        // 16:9比例设置 (type1)
        type1: {
            boxWidth: 430, // 实际显示宽度
            boxHeight: 242, // 实际显示高度
            innerWidth: innnerSize.macAirMobile.innerWidth, // 浏览器窗口宽度
            innerHeight: innnerSize.macAirMobile.innerHeight, // 浏览器窗口高度
            useWidth: 1920, // 计算缩放使用的宽度
            useHeight: 1080, // 计算缩放使用的高度
            useRatioWidth: 16, // 比例宽值(展示用)
            useRatioHeight: 9, // 比例高值(展示用)
        },

        // 3:2比例设置 (type2)
        type2: {
            boxWidth: 430,
            boxHeight: 287,
            innerWidth: innnerSize.macAirMobile.innerWidth,
            innerHeight: innnerSize.macAirMobile.innerHeight,
            useWidth: 1920,
            useHeight: 1280,
            useRatioWidth: 3,
            useRatioHeight: 2,
        },

        // 4:3比例设置 (type3)
        type3: {
            boxWidth: 430,
            boxHeight: 323,
            innerWidth: innnerSize.macAirMobile.innerWidth,
            innerHeight: innnerSize.macAirMobile.innerHeight,
            useWidth: 1920,
            useHeight: 1440,
            useRatioWidth: 4,
            useRatioHeight: 3,
        },

        // 3:1比例设置 (type4)
        type4: {
            boxWidth: 430,
            boxHeight: 143,
            innerWidth: innnerSize.macAirMobile.innerWidth,
            innerHeight: innnerSize.macAirMobile.innerHeight,
            useWidth: 1500,
            useHeight: 500,
            useRatioWidth: 3,
            useRatioHeight: 1,
        },

        // iPhone尺寸比例设置 (type5)
        type5: {
            boxWidth: 227,
            boxHeight: 492,
            innerWidth: innnerSize.macAirMobile.innerWidth,
            innerHeight: innnerSize.macAirMobile.innerHeight,
            useWidth: 1284,
            useHeight: 2778,
            useRatioWidth: null,
            useRatioHeight: null,
        },
    },
}

/**
 * 获取基于预设的尺寸计算
 *
 * 根据选择的类型和预设数据计算合适的尺寸
 *
 * @param selectedType 选择的比例类型
 * @param options 可选参数
 * @returns 计算后的尺寸对象，包含宽高
 */
export const cptWidthAndHeight = (
    selectedType: RatioType = 'type2',
    options?: {
        deviceMode?: 'pc' | 'mobile'
        customDimensions?: { useWidth: number; useHeight: number }
    },
) => {
    // 默认使用macAir PC 预设，或根据 deviceMode 选择
    const deviceMode = options?.deviceMode || 'pc'
    const useMac =
        deviceMode === 'pc'
            ? browserAllSizes.macAirBrowserPcSizes
            : browserAllSizes.macAirBrowserMobileSizes

    // 获取当前浏览器窗口尺寸(处理SSR)
    const innerWidth = typeof window !== 'undefined' ? window.innerWidth : 0
    const innerHeight = typeof window !== 'undefined' ? window.innerHeight : 0

    // 获取对应模式的预设尺寸
    const currentInnerSize = deviceMode === 'pc' ? innnerSize.macAirPc : innnerSize.macAirMobile

    // 检查窗口尺寸是否匹配当前模式预设
    if (
        innerWidth !== currentInnerSize.innerWidth ||
        innerHeight !== currentInnerSize.innerHeight
    ) {
        // 窗口尺寸不匹配预设时的提示
        if (typeof window !== 'undefined') {
            console.log('不支持的窗口尺寸', window.innerWidth, window.innerHeight)
            console.warn('当前窗口尺寸与预设不匹配，将使用动态计算方法')
            alert('当前窗口尺寸与预设不匹配，只用于标准的测试校验')
            return { width: 0, height: 0 }
        }
    }

    // 根据选择的类型获取对应的预设值
    let value = null

    // 检查所选类型在预设中是否存在
    if (selectedType === 'type1' && useMac.type1) {
        value = useMac.type1 // 16:9比例
    } else if (selectedType === 'type2' && useMac.type2) {
        value = useMac.type2 // 3:2比例
    } else if (selectedType === 'type3' && useMac.type3) {
        value = useMac.type3 // 4:3比例
    } else if (selectedType === 'type4' && useMac.type4) {
        value = useMac.type4 // 3:1比例
    } else if (selectedType === 'type5' && useMac.type5) {
        value = useMac.type5 // iPhone尺寸比例
    } else {
        // 如果找不到选择的类型，回退到可用的第一个类型
        if (useMac.type2) {
            value = useMac.type2 // 默认使用type2(3:2比例)
        } else if (useMac.type1) {
            value = useMac.type1 // 备选type1(16:9比例)
        } else if (useMac.type3) {
            value = useMac.type3 // 备选type3(4:3比例)
        } else if (useMac.type4) {
            value = useMac.type4 // 备选type4(3:1比例)
        } else if (useMac.type5) {
            value = useMac.type5 // 备选type5(iPhone尺寸)
        } else {
            console.error('无法找到有效的尺寸类型')
            return { width: 0, height: 0 }
        }
    }

    // 如果提供了自定义尺寸，则应用自定义的useWidth和useHeight
    if (options?.customDimensions) {
        // 创建新对象，避免修改原始预设
        const customValue = { ...value }
        customValue.useWidth = options.customDimensions.useWidth
        customValue.useHeight = options.customDimensions.useHeight

        // 使用自定义值重新计算boxWidth和boxHeight
        // 这里使用简单的比例计算，保持预设的宽高比
        // 移除未使用的 originalRatio 变量

        // 假设我们要保持原始盒子的高度
        customValue.boxHeight = value.boxHeight
        // 根据新的useWidth/useHeight比例调整boxWidth
        const newRatio = options.customDimensions.useWidth / options.customDimensions.useHeight
        customValue.boxWidth = Math.round(customValue.boxHeight * newRatio)

        console.log('使用自定义尺寸:', customValue.useWidth, 'x', customValue.useHeight)
        console.log('调整后的盒子尺寸:', customValue.boxWidth, 'x', customValue.boxHeight)

        value = customValue
    }

    // 返回预设中的宽高值
    return {
        width: value.boxWidth,
        height: value.boxHeight,
    }
}

/**
 * 获取元素的尺寸和相对于页面的位置信息
 *
 * 该函数返回元素的宽高以及相对于页面四边的距离，
 * 考虑了页面滚动的影响
 *
 * @param element 目标HTML元素
 * @returns 包含元素宽高和位置信息的对象
 */
export const getPageDistances = (element: HTMLElement) => {
    // 获取元素的位置和尺寸信息
    const rect = element.getBoundingClientRect()

    // 获取页面滚动位置
    const { scrollX, scrollY } = window

    // 获取文档整体尺寸
    const { scrollWidth, scrollHeight } = document.documentElement

    // 直接从rect获取元素宽高
    const { width, height } = element.getBoundingClientRect()

    // 返回完整的元素尺寸和位置信息
    return {
        width, // 元素的宽度
        height, // 元素的高度
        top: rect.top + scrollY, // 元素距离页面顶部的距离
        left: rect.left + scrollX, // 元素距离页面左侧的距离
        right: scrollWidth - (rect.left + scrollX + rect.width), // 元素距离页面右侧的距离
        bottom: scrollHeight - (rect.top + scrollY + rect.height), // 元素距离页面底部的距离
    }
}

/**
 * 获取可用空间信息算法
 *
 * 该函数获取Canvas安全区域元素并计算可用空间
 *
 * @returns {{width: number, height: number} | null} 可用空间尺寸或null（如果无法获取）
 */
export const getAvailableSpace = () => {
    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        return null
    }

    // 获取Canvas安全区域元素
    const canvasSafeArea = document.querySelector('.canvas-safe-area') as HTMLElement
    if (!canvasSafeArea) {
        console.error('获取Canvas安全区域元素错误')
        return null
    }

    // 获取元素尺寸和位置信息
    const metrics = getPageDistances(canvasSafeArea)
    console.log('Canvas安全区域指标:', metrics, canvasSafeArea)

    // 返回可用空间尺寸
    return {
        width: metrics.width,
        height: metrics.height,
    }
}

/**
 * 匹配当前窗口尺寸对应的预设数据
 *
 * @param options 可选参数
 * @returns {BrowserSizes | null} 匹配的预设数据或null（如果无匹配）
 */
export const matchPresetsForCurrentWindow = (options?: { deviceMode?: 'pc' | 'mobile' }) => {
    // SSR检查
    if (typeof window === 'undefined') {
        return null
    }

    // 获取当前窗口尺寸
    const windowInfo = {
        width: window.innerWidth,
        height: window.innerHeight,
    }
    console.log('窗口尺寸:', windowInfo.width, 'x', windowInfo.height)

    // 根据设备模式选择预设
    const deviceMode = options?.deviceMode || 'pc'
    const currentInnerSize = deviceMode === 'pc' ? innnerSize.macAirPc : innnerSize.macAirMobile

    // 检查是否匹配当前设备模式的预设
    if (
        windowInfo.width === currentInnerSize.innerWidth &&
        windowInfo.height === currentInnerSize.innerHeight
    ) {
        // 使用对应设备模式的预设数据
        console.log(`使用 ${deviceMode === 'pc' ? 'PC' : '移动端'} 预设数据`)
        return deviceMode === 'pc'
            ? browserAllSizes.macAirBrowserPcSizes
            : browserAllSizes.macAirBrowserMobileSizes
    } else {
        // 无匹配预设时，将使用纯比例计算
        console.log('窗口尺寸不匹配预设，使用纯比例计算')
        return null
    }
}

/**
 * 获取当前类型的参考信息
 *
 * @param {string} typeName 类型名称
 * @param {BrowserSizes | null} currentPresets 当前预设数据
 * @param {object} options 可选参数
 * @returns {{useWidth: number, useHeight: number} | null} 参考信息
 */
export const getReferenceInfo = (
    typeName: string,
    currentPresets: BrowserSizes | null,
    options?: {
        customDimensions?: { useWidth: number; useHeight: number }
    },
) => {
    // 如果有自定义尺寸，直接返回
    if (options?.customDimensions) {
        return {
            useWidth: options.customDimensions.useWidth,
            useHeight: options.customDimensions.useHeight,
        }
    }

    if (!currentPresets) {
        return null
    }

    // 根据类型名称匹配对应的预设信息
    let referenceInfo = null
    if (typeName.includes('type1') && currentPresets.type1) {
        referenceInfo = {
            useWidth: currentPresets.type1.useWidth,
            useHeight: currentPresets.type1.useHeight,
        }
    } else if (typeName.includes('type2') && currentPresets.type2) {
        referenceInfo = {
            useWidth: currentPresets.type2.useWidth,
            useHeight: currentPresets.type2.useHeight,
        }
    } else if (typeName.includes('type3') && currentPresets.type3) {
        referenceInfo = {
            useWidth: currentPresets.type3.useWidth,
            useHeight: currentPresets.type3.useHeight,
        }
    } else if (typeName.includes('type4') && currentPresets.type4) {
        referenceInfo = {
            useWidth: currentPresets.type4.useWidth,
            useHeight: currentPresets.type4.useHeight,
        }
    } else if (typeName.includes('type5') && currentPresets.type5) {
        referenceInfo = {
            useWidth: currentPresets.type5.useWidth,
            useHeight: currentPresets.type5.useHeight,
        }
    }

    return referenceInfo
}

/**
 * 计算基于特定比例和可用空间的显示尺寸
 *
 * 该纯函数使用三种计算方式的优先级策略，计算最佳的显示尺寸：
 * 1. 基于useWidth/useHeight的缩放计算（最优先）
 * 2. 基于target的缩放计算（备用）
 * 3. 基于比例(ratio)的计算（保底）
 *
 * @param {string} name 比例类型名称
 * @param {{width: number, height: number} | null} ratio 比例信息
 * @param {{width: number, height: number}} target 目标尺寸
 * @param {{width: number, height: number}} availableSpace 可用空间
 * @param {{useWidth: number, useHeight: number} | null} referenceInfo 参考信息
 * @returns {object} 计算结果
 */
export const calculateDimensionsForRatio = (
    name: string,
    ratio: { width: number; height: number } | null,
    target: { width: number; height: number },
    availableSpace: { width: number; height: number },
    referenceInfo: { useWidth: number; useHeight: number } | null,
) => {
    // 声明用于存储计算结果的变量
    let finalSize: { width: number; height: number }
    let sizeMethod = ''

    /**
     * 计算方法1: 基于useWidth和useHeight的缩放计算 (优先方法)
     */
    let scaledSize = null
    let scalingFactor = 1
    let scaleMethod = ''

    // 当有可用的referenceInfo时执行此计算
    if (referenceInfo && referenceInfo.useWidth && referenceInfo.useHeight) {
        // 计算可用空间与目标尺寸的宽度和高度缩放比例
        const widthScale = availableSpace.width / referenceInfo.useWidth
        const heightScale = availableSpace.height / referenceInfo.useHeight

        // 选择较小的缩放因子以确保完全适应可用空间
        if (widthScale < heightScale) {
            scalingFactor = widthScale
            scaleMethod = '基于宽度比例缩放' // 宽度限制更严格
        } else {
            scalingFactor = heightScale
            scaleMethod = '基于高度比例缩放' // 高度限制更严格
        }

        // 根据缩放因子计算尺寸
        scaledSize = {
            width: Math.round(referenceInfo.useWidth * scalingFactor),
            height: Math.round(referenceInfo.useHeight * scalingFactor),
        }

        // 检查缩放后的尺寸是否适合可用空间
        const fitsInSpace =
            scaledSize.width <= availableSpace.width && scaledSize.height <= availableSpace.height

        // 打印缩放信息
        console.log('基于useWidth/useHeight缩放计算:')
        console.log(`目标尺寸: ${referenceInfo.useWidth}x${referenceInfo.useHeight}`)
        console.log(
            `缩放因子: 宽度=${widthScale.toFixed(4)}, 高度=${heightScale.toFixed(4)}, 选择=${scalingFactor.toFixed(4)}`,
        )
        console.log(
            `缩放结果: ${scaledSize.width}x${scaledSize.height} (${scaleMethod}, 适合空间: ${fitsInSpace ? '是' : '否'})`,
        )

        // 如果缩放结果不适合可用空间，放弃此方法
        if (!fitsInSpace) {
            console.log('警告: 缩放尺寸超出可用空间，尝试其他计算方法')
            scaledSize = null
        }
    } else {
        console.log('没有可用的useWidth/useHeight信息，尝试其他计算方法')
    }

    /**
     * 计算方法2: 基于target的缩放计算 (备用方法)
     */
    if (!scaledSize && target) {
        // 计算可用空间与目标尺寸的缩放比例
        const widthScale = availableSpace.width / target.width
        const heightScale = availableSpace.height / target.height

        // 选择较小的缩放因子以确保完全适应
        if (widthScale < heightScale) {
            scalingFactor = widthScale
            scaleMethod = '基于宽度比例缩放(target)'
        } else {
            scalingFactor = heightScale
            scaleMethod = '基于高度比例缩放(target)'
        }

        // 根据缩放因子计算尺寸
        scaledSize = {
            width: Math.round(target.width * scalingFactor),
            height: Math.round(target.height * scalingFactor),
        }

        // 检查缩放后的尺寸是否适合可用空间
        const fitsInSpace =
            scaledSize.width <= availableSpace.width && scaledSize.height <= availableSpace.height

        // 打印缩放信息
        console.log('基于target缩放计算:')
        console.log(`目标尺寸: ${target.width}x${target.height}`)
        console.log(
            `缩放因子: 宽度=${widthScale.toFixed(4)}, 高度=${heightScale.toFixed(4)}, 选择=${scalingFactor.toFixed(4)}`,
        )
        console.log(
            `缩放结果: ${scaledSize.width}x${scaledSize.height} (${scaleMethod}, 适合空间: ${fitsInSpace ? '是' : '否'})`,
        )

        // 如果缩放结果不适合可用空间，放弃此方法
        if (!fitsInSpace) {
            console.log('警告: 缩放尺寸超出可用空间，尝试比例计算方法')
            scaledSize = null
        }
    }

    /**
     * 计算方法3: 基于比例(ratio)的计算（保底方法）
     */
    let ratioSize = null
    let ratioMethod = ''

    // 如果前两种方法都失败，使用比例计算
    if (!scaledSize && ratio) {
        console.log(`执行比例计算 (${ratio.width}:${ratio.height})...`)

        // 计算基于比例和可用空间的可能尺寸
        const byWidth = {
            width: availableSpace.width,
            height: Math.round(availableSpace.width * (ratio.height / ratio.width)),
        }

        const byHeight = {
            width: Math.round(availableSpace.height * (ratio.width / ratio.height)),
            height: availableSpace.height,
        }

        // 打印可能的尺寸选项
        console.log(`基于宽度计算: ${byWidth.width}x${byWidth.height}`)
        console.log(`基于高度计算: ${byHeight.width}x${byHeight.height}`)

        // 检查两种尺寸是否适合可用空间
        const widthFits = byWidth.height <= availableSpace.height
        const heightFits = byHeight.width <= availableSpace.width

        // 根据适应性选择合适的尺寸
        if (widthFits && heightFits) {
            // 如果两种方法都适合，选择面积较大的
            const areaByWidth = byWidth.width * byWidth.height
            const areaByHeight = byHeight.width * byHeight.height

            if (areaByWidth >= areaByHeight) {
                ratioSize = byWidth
                ratioMethod = '基于宽度的比例计算(更大面积)'
            } else {
                ratioSize = byHeight
                ratioMethod = '基于高度的比例计算(更大面积)'
            }
        } else if (widthFits) {
            // 只有宽度方法适合
            ratioSize = byWidth
            ratioMethod = '基于宽度的比例计算(唯一适合)'
        } else if (heightFits) {
            // 只有高度方法适合
            ratioSize = byHeight
            ratioMethod = '基于高度的比例计算(唯一适合)'
        } else {
            // 两种方法都不完全适合，选择最接近的
            // 计算溢出比例
            const widthOverflow = byWidth.height / availableSpace.height
            const heightOverflow = byHeight.width / availableSpace.width

            if (widthOverflow <= heightOverflow) {
                // 基于宽度计算的溢出较小
                const adjustedHeight = availableSpace.height
                const adjustedWidth = Math.round(adjustedHeight * (ratio.width / ratio.height))
                ratioSize = {
                    width: adjustedWidth,
                    height: adjustedHeight,
                }
                ratioMethod = '基于宽度的比例计算(调整后)'
            } else {
                // 基于高度计算的溢出较小
                const adjustedWidth = availableSpace.width
                const adjustedHeight = Math.round(adjustedWidth * (ratio.height / ratio.width))
                ratioSize = {
                    width: adjustedWidth,
                    height: adjustedHeight,
                }
                ratioMethod = '基于高度的比例计算(调整后)'
            }
        }

        // 打印最终选择的比例计算结果
        console.log(`比例计算结果: ${ratioSize.width}x${ratioSize.height} (${ratioMethod})`)

        // 验证比例准确性
        const actualRatio = ratioSize.width / ratioSize.height
        const targetRatio = ratio.width / ratio.height
        const ratioDiff = Math.abs(actualRatio - targetRatio) / targetRatio

        console.log(
            `比例准确性: 目标=${targetRatio.toFixed(4)}, 实际=${actualRatio.toFixed(4)}, 误差=${(ratioDiff * 100).toFixed(2)}%`,
        )

        // 如果误差过大，打印警告
        if (ratioDiff > 0.01) {
            // 1%误差容忍度
            console.warn('警告: 计算结果的比例与目标比例有显著差异')
        }
    } else {
        console.log('跳过比例计算，使用已计算的尺寸')
    }

    /**
     * 最终尺寸选择和结果汇总
     */
    // 按优先级选择最终尺寸
    if (scaledSize) {
        finalSize = scaledSize
        sizeMethod = scaleMethod
    } else if (ratioSize) {
        finalSize = ratioSize
        sizeMethod = ratioMethod
    } else {
        // 极端情况下的默认值
        finalSize = {
            width: Math.min(500, availableSpace.width),
            height: Math.min(300, availableSpace.height),
        }
        sizeMethod = '默认尺寸(无法计算)'
        console.warn('警告: 使用默认尺寸，所有计算方法均失败')
    }

    // 确保尺寸为整数
    finalSize = {
        width: Math.round(finalSize.width),
        height: Math.round(finalSize.height),
    }

    // 打印最终选择结果
    console.log('最终尺寸选择:')
    console.log(`尺寸: ${finalSize.width}x${finalSize.height}`)
    console.log(`计算方法: ${sizeMethod}`)
    console.log(`可用空间: ${availableSpace.width}x${availableSpace.height}`)

    // 计算使用率
    const widthUsage = (finalSize.width / availableSpace.width) * 100
    const heightUsage = (finalSize.height / availableSpace.height) * 100
    console.log(`空间使用率: 宽度=${widthUsage.toFixed(1)}%, 高度=${heightUsage.toFixed(1)}%`)

    // 确保比例准确性 - 根据需要调整尺寸
    if (ratio) {
        // 对于有固定比例的类型，确保精确匹配比例
        const exactRatio = ratio.width / ratio.height
        const actualRatio = finalSize.width / finalSize.height

        // 如果实际比例与目标比例有显著差异，进行校正
        if (Math.abs(actualRatio - exactRatio) > 0.01) {
            // 1%的容差
            if (sizeMethod.includes('基于宽度')) {
                // 保持宽度不变，调整高度
                finalSize.height = Math.round(finalSize.width / exactRatio)
            } else {
                // 保持高度不变，调整宽度
                finalSize.width = Math.round(finalSize.height * exactRatio)
            }
            console.log(
                `应用比例校正: 新尺寸 ${finalSize.width}x${finalSize.height} (匹配比例 ${ratio.width}:${ratio.height})`,
            )
        }
    } else if (target) {
        // 对于没有固定比例的类型，检查与目标比例的差异
        const targetRatio = target.width / target.height
        const actualRatio = finalSize.width / finalSize.height

        // 使用更宽松的容差，仅记录差异而不调整
        if (Math.abs(actualRatio - targetRatio) > 0.05) {
            // 5%的容差
            console.log(
                `注意: 最终尺寸比例(${actualRatio.toFixed(4)})与目标比例(${targetRatio.toFixed(4)})有差异，但不进行校正以保持原始计算`,
            )
        }
    }

    // 计算比例统计信息
    const actualRatio = finalSize.width / finalSize.height
    const actualRatioStr = actualRatio.toFixed(2)

    // 计算与目标比例的差异
    let targetRatioValue = ''
    let ratioDifference = ''
    let ratioDifferencePercent = ''
    let ratioMatch = ''

    // 根据是否有固定比例计算差异
    if (ratio) {
        targetRatioValue = (ratio.width / ratio.height).toFixed(2)
        ratioDifference = (Number(actualRatioStr) - Number(targetRatioValue)).toFixed(2)
        ratioDifferencePercent = (
            (Number(ratioDifference) / Number(targetRatioValue)) *
            100
        ).toFixed(1)
        ratioMatch = Math.abs(Number(ratioDifference)) < 0.02 ? '✓ 匹配' : '✗ 不匹配'
    } else if (target) {
        const targetRatio = target.width / target.height
        targetRatioValue = targetRatio.toFixed(2)
        ratioDifference = (actualRatio - targetRatio).toFixed(2)
        ratioDifferencePercent = ((Number(ratioDifference) / targetRatio) * 100).toFixed(1)
        ratioMatch = Math.abs(Number(ratioDifference)) < 0.02 ? '✓ 匹配' : '✗ 不匹配'
    }

    // 输出最终结果摘要
    console.log(`---- ${name} 最终结果 ----`)
    console.log(`计算方法: ${sizeMethod}`)
    console.log(`最终尺寸: ${finalSize.width}x${finalSize.height}`)
    console.log(
        `实际比例: ${actualRatioStr} ${ratioMatch ? `(${ratioMatch}), 差值: ${ratioDifference} (${ratioDifferencePercent}%)` : ''}`,
    )

    // 返回结果对象
    return {
        name, // 比例类型名称
        boxWidth: finalSize.width, // 计算出的宽度
        boxHeight: finalSize.height, // 计算出的高度
        ratio: ratio
            ? `${ratio.width}:${ratio.height}`
            : target
                ? `${target.width}:${target.height}`
                : 'custom', // 比例表示
        targetRatio: targetRatioValue, // 目标比例值
        calculatedRatio: actualRatioStr, // 实际计算得到的比例
        ratioMatch, // 比例匹配状态
        ratioDifference, // 与目标比例的差异
        ratioDifferencePercent: ratioDifferencePercent ? `${ratioDifferencePercent}%` : '', // 差异百分比
        method: sizeMethod, // 使用的计算方法
        reference: referenceInfo, // 参考信息(仅包括useWidth和useHeight)
        calculationDetails: {
            // 详细计算信息
            hasFixedRatio: !!ratio,
            target: target,
            finalWidth: finalSize.width,
            finalHeight: finalSize.height,
        },
    }
}

/**
 * 获取标准的比例类型定义
 *
 * @returns 预定义的比例类型数组
 */
export const getAspectRatioTypes = () => {
    return [
        {
            name: '16:9 比例 (type1)',
            ratio: { width: 16, height: 9 },
            target: { width: 1920, height: 1080 },
        },
        {
            name: '3:2 比例 (type2)',
            ratio: { width: 3, height: 2 },
            target: { width: 1920, height: 1280 },
        },
        {
            name: '4:3 比例 (type3)',
            ratio: { width: 4, height: 3 },
            target: { width: 1920, height: 1440 },
        },
        {
            name: '3:1 比例 (type4)',
            ratio: { width: 3, height: 1 },
            target: { width: 1500, height: 500 },
        },
        {
            name: '1284:2778 比例 (type5)',
            ratio: null, // 没有固定比例，使用target的宽高比
            target: { width: 1284, height: 2778 },
        },
    ]
}

/**
 * 获取所选比例类型的最佳显示尺寸
 *
 * 该函数根据选定的比例类型计算最佳的显示尺寸
 *
 * @param {RatioType} [selectedType] 选择的比例类型
 * @param {object} [options] 额外选项
 * @returns {{width: number, height: number}} 计算结果，包含宽度和高度
 */
export const getDimensionsForSelectedType = (
    selectedType?: RatioType,
    options?: {
        availableSpace?: { width: number; height: number }
        presets?: BrowserSizes | null
        typeSelected?: RatioType
        deviceMode?: 'pc' | 'mobile'
        customDimensions?: { useWidth: number; useHeight: number }
    },
) => {
    // SSR检查 - 如果不在客户端环境，返回默认值
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        return { width: 0, height: 0 }
    }

    // 确定选择的类型（如果未提供，使用传入的当前已选类型或默认值）
    const typeToUse = selectedType || options?.typeSelected || 'type2'

    // 获取可用空间
    const availableSpace = options?.availableSpace || getAvailableSpace()
    if (!availableSpace) {
        console.error('无法获取可用空间')
        return { width: 0, height: 0 }
    }

    // 获取匹配的预设
    const deviceMode = options?.deviceMode || 'pc'
    const currentPresets = options?.presets || matchPresetsForCurrentWindow({ deviceMode })

    // 获取所有比例类型定义
    const ratioTypes = getAspectRatioTypes()

    // 查找与当前选择类型匹配的比例类型
    const selectedRatioType = ratioTypes.find(type => type.name.includes(typeToUse))
    if (!selectedRatioType) {
        console.error('无法找到匹配的比例类型')
        return { width: 0, height: 0 }
    }

    // 解构获取当前类型的名称、比例和目标尺寸
    const { name, ratio, target } = selectedRatioType

    // 获取当前类型的参考信息，可能使用自定义尺寸
    const referenceInfo = getReferenceInfo(name, currentPresets, {
        customDimensions: options?.customDimensions,
    })

    // 计算单个比例类型的尺寸
    const result = calculateDimensionsForRatio(name, ratio, target, availableSpace, referenceInfo)

    // 返回计算结果
    return {
        width: result.boxWidth,
        height: result.boxHeight,
    }
}

/**
 * 测试所有比例类型的尺寸计算
 *
 * 该函数计算每种预定义比例类型的最佳尺寸，并与预设尺寸比较
 * 仅在选择预设尺寸(sizeOption=1)时执行测试
 *
 * @param {number} [currentSizeOption] 当前尺寸选项
 * @param {object} [options] 额外选项
 * @returns {Array} 所有比例类型的测试结果
 */
export const testAllAspectRatios = (
    currentSizeOption?: number,
    options?: {
        deviceMode?: 'pc' | 'mobile'
        customDimensions?: { useWidth: number; useHeight: number }
    },
) => {
    // 使用传入的选项或默认值
    const sizeOptionToUse = currentSizeOption !== undefined ? currentSizeOption : 1
    const deviceMode = options?.deviceMode || 'pc'

    // 如果不是使用预设尺寸，则跳过测试
    if (sizeOptionToUse !== 1) {
        console.log('当前使用动态计算尺寸，跳过测试')
        return [] as any[]
    }

    // SSR检查
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        return [] as any[]
    }

    console.log(`======== 开始测试${deviceMode === 'pc' ? 'PC端' : '移动端'}各比例尺寸 ========`)

    // 如果使用自定义尺寸，显示提示
    if (options?.customDimensions) {
        console.log(
            '使用自定义尺寸进行测试:',
            options.customDimensions.useWidth,
            'x',
            options.customDimensions.useHeight,
        )
    }

    // 获取可用空间
    const availableSpace = getAvailableSpace()
    if (!availableSpace) {
        console.error('无法获取可用空间')
        return [] as any[]
    }
    console.log('可用空间:', availableSpace.width, 'x', availableSpace.height)

    // 获取匹配的预设
    const currentPresets = matchPresetsForCurrentWindow({ deviceMode })

    // 获取所有比例类型
    const ratioTypes = getAspectRatioTypes()

    // 遍历所有比例类型计算尺寸
    const results = ratioTypes.map(type => {
        // 解构获取当前类型的名称、比例和目标尺寸
        const { name, ratio, target } = type

        // 获取当前类型的参考信息，可能使用自定义尺寸
        const referenceInfo = getReferenceInfo(name, currentPresets, {
            customDimensions: options?.customDimensions,
        })

        // 计算盒子尺寸
        return calculateDimensionsForRatio(name, ratio, target, availableSpace, referenceInfo)
    })

    // 输出测试结果摘要
    console.log(`======== ${deviceMode === 'pc' ? 'PC端' : '移动端'}测试结果摘要 ========`)
    results.forEach(result => {
        const summaryText = `${result.name}: ${result.boxWidth}x${result.boxHeight} (比例: ${result.calculatedRatio}, ${result.method.split('(')[0].trim()})`
        console.log(summaryText)
    })

    // 与预设尺寸比较
    if (currentPresets) {
        console.log(`======== 与${deviceMode === 'pc' ? 'PC端' : '移动端'}预设尺寸比较 ========`)
        results.forEach(result => {
            let presetSize = null
            const typeName = result.name

            if (typeName.includes('type1')) presetSize = currentPresets.type1
            else if (typeName.includes('type2')) presetSize = currentPresets.type2
            else if (typeName.includes('type3')) presetSize = currentPresets.type3
            else if (typeName.includes('type4')) presetSize = currentPresets.type4
            else if (typeName.includes('type5')) presetSize = currentPresets.type5

            if (presetSize) {
                // 计算与预设尺寸的差异
                const widthDiff = result.boxWidth - presetSize.boxWidth
                const heightDiff = result.boxHeight - presetSize.boxHeight
                const widthDiffPercent = ((widthDiff / presetSize.boxWidth) * 100).toFixed(1)
                const heightDiffPercent = ((heightDiff / presetSize.boxHeight) * 100).toFixed(1)

                // 输出预设比较信息
                console.log(`${typeName}:`)
                console.log(`计算尺寸: ${result.boxWidth}x${result.boxHeight}`)
                console.log(`预设尺寸: ${presetSize.boxWidth}x${presetSize.boxHeight}`)
                console.log(
                    `尺寸差异: 宽度 ${widthDiff}px (${widthDiffPercent}%), 高度 ${heightDiff}px (${heightDiffPercent}%)`,
                )
            }
        })
    }

    return results
}
