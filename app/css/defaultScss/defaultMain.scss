// 主题预设
@use 'defaultTheme.scss';

// 颜色预设
@use 'defaultColor.scss';

// 浏览器默认
@use 'defaultBrowser.scss';

// 字体预设
@use 'defaultFontFamil.scss';

// h1-h6 元素预设
@use 'defaultH1_h6Element.scss';

// 其他样式预设
@use 'defaultOtherStyle.scss';

.Public_MockupSmallLayout {
    display: none; // 这里不应该用style控制而是用if代码判断
}
@media only screen and (width>=0) and (width<=1200px) {
    .sidebar:last-child {
        display: none !important;
    }
    .Public_MockupSmallLayout {
        display: flex; // 这里不应该用style控制而是用if代码判断
    }
}
