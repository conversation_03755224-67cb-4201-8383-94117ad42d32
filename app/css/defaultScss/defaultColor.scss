.primary {
    color: rgba(var(--primary), 1) !important;
}
.secondary {
    color: rgba(var(--secondary), 1) !important;
}
.gray-text {
    color: rgba(var(--primary), 0.36) !important;
}
.gray-text2 {
    color: rgba(var(--primary), 0.6) !important;
}
.gray-icon {
    color: rgba(var(--primary), 0.12) !important;
}
.accent {
    color: linear-gradient(120deg, #ff6432 25%, #ff0065 45%, #7b2eff 75%) !important;
}
.danger {
    color: rgba(var(--danger), 1) !important;
}
.success {
    color: rgba(var(--success), 1) !important;
}
.alert {
    color: rgba(var(--alert), 1) !important;
}
.performance {
    color: rgba(var(--performance), 1) !important;
}
.bg-none {
    background: 0 0 !important;
}
.bg-primary {
    background: rgba(var(--primary), 1) !important;
}
.bg-highlight {
    background: rgba(var(--primary), 0.06) !important;
}
.bg-gray-icon {
    background: rgba(var(--primary), 0.12) !important;
}
.bg-accent {
    color: #fff !important;
    background: linear-gradient(120deg, #ff6432 25%, #ff0065 45%, #7b2eff 75%) !important;
}
.bg-overlay {
    background: rgba(var(--background), 0.8) !important;
}
.bg-danger {
    background: rgba(var(--danger), 1) !important;
}
.bg-success {
    background: rgba(var(--success), 1) !important;
}
.bg-panel {
    background: rgba(var(--panel), 1) !important;
}
.bg-panel-dim {
    background: rgba(var(--panel-dim), 1) !important;
}
.bg-panel-active {
    background: rgba(var(--panel-active), 1) !important;
}
