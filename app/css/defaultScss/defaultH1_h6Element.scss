.header {
    letter-spacing: -1px;
    font:
        600 52px/64px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .header {
        letter-spacing: -1px;
        font:
            600 40px/48px Inter,
            sans-serif;
    }
}

h1,
.h1 {
    letter-spacing: -1px;
    font:
        500 34px/44px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    h1,
    .h1 {
        letter-spacing: -1.1px;
        font:
            500 33px/40px Inter,
            sans-serif;
    }
}
h2,
.h2,
.text-font-picker.variant-7 .text-item:nth-child(2),
.text-elements.variant-7 .text-item:nth-child(2),
.text-font-picker.variant-8 .text-item:nth-child(2),
.text-elements.variant-8 .text-item:nth-child(2),
.text-font-picker.variant-1 .text-item:nth-child(2),
.text-elements.variant-1 .text-item:nth-child(2),
.text-font-picker.variant-2 .text-item:nth-child(2),
.text-elements.variant-2 .text-item:nth-child(2),
.text-font-picker.variant-3 .text-item:nth-child(2),
.text-elements.variant-3 .text-item:nth-child(2),
.text-font-picker.variant-4 .text-item:nth-child(2),
.text-elements.variant-4 .text-item:nth-child(2),
.text-font-picker.variant-5 .text-item:nth-child(2),
.text-elements.variant-5 .text-item:nth-child(2),
.text-font-picker.variant-6 .text-item:nth-child(2),
.text-elements.variant-6 .text-item:nth-child(2) {
    letter-spacing: -1px;
    font:
        500 28px/38px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    h2,
    .h2,
    .text-font-picker.variant-7 .text-item:nth-child(2),
    .text-elements.variant-7 .text-item:nth-child(2),
    .text-font-picker.variant-8 .text-item:nth-child(2),
    .text-elements.variant-8 .text-item:nth-child(2),
    .text-font-picker.variant-1 .text-item:nth-child(2),
    .text-elements.variant-1 .text-item:nth-child(2),
    .text-font-picker.variant-2 .text-item:nth-child(2),
    .text-elements.variant-2 .text-item:nth-child(2),
    .text-font-picker.variant-3 .text-item:nth-child(2),
    .text-elements.variant-3 .text-item:nth-child(2),
    .text-font-picker.variant-4 .text-item:nth-child(2),
    .text-elements.variant-4 .text-item:nth-child(2),
    .text-font-picker.variant-5 .text-item:nth-child(2),
    .text-elements.variant-5 .text-item:nth-child(2),
    .text-font-picker.variant-6 .text-item:nth-child(2),
    .text-elements.variant-6 .text-item:nth-child(2) {
        letter-spacing: -0.9px;
        font:
            500 27px/34px Inter,
            sans-serif;
    }
}
h3,
.h3 {
    letter-spacing: -0.8px;
    font:
        500 23px/32px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    h3,
    .h3 {
        letter-spacing: -0.75px;
        font:
            500 21.5px/28px Inter,
            sans-serif;
    }
}
h4,
.h4,
.text-font-picker.variant-1 .text-item:nth-child(3),
.text-elements.variant-1 .text-item:nth-child(3) {
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    h4,
    .h4,
    .text-font-picker.variant-1 .text-item:nth-child(3),
    .text-elements.variant-1 .text-item:nth-child(3) {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
h5,
.h5,
.text-font-picker.variant-7 .text-item:first-child,
.text-elements.variant-7 .text-item:first-child,
.text-font-picker.variant-8 .text-item:first-child,
.text-elements.variant-8 .text-item:first-child,
.text-font-picker.variant-3 .text-item:first-child,
.text-elements.variant-3 .text-item:first-child,
.text-font-picker.variant-4 .text-item:nth-child(3),
.text-elements.variant-4 .text-item:nth-child(3),
.text-font-picker.variant-5 .text-item:first-child,
.text-elements.variant-5 .text-item:first-child,
.text-font-picker.variant-6 .text-item:first-child,
.text-elements.variant-6 .text-item:first-child {
    letter-spacing: -0.6px;
    font:
        500 17px/24px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    h5,
    .h5,
    .text-font-picker.variant-7 .text-item:first-child,
    .text-elements.variant-7 .text-item:first-child,
    .text-font-picker.variant-8 .text-item:first-child,
    .text-elements.variant-8 .text-item:first-child,
    .text-font-picker.variant-3 .text-item:first-child,
    .text-elements.variant-3 .text-item:first-child,
    .text-font-picker.variant-4 .text-item:nth-child(3),
    .text-elements.variant-4 .text-item:nth-child(3),
    .text-font-picker.variant-5 .text-item:first-child,
    .text-elements.variant-5 .text-item:first-child,
    .text-font-picker.variant-6 .text-item:first-child,
    .text-elements.variant-6 .text-item:first-child {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
h6,
.h6,
.text-font-picker.variant-3 .text-item:nth-child(3),
.text-elements.variant-3 .text-item:nth-child(3),
.select,
button.default-button.small-button,
.button.default-button.small-button {
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    h6,
    .h6,
    .text-font-picker.variant-3 .text-item:nth-child(3),
    .text-elements.variant-3 .text-item:nth-child(3),
    .select,
    button.default-button.small-button,
    .button.default-button.small-button {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
