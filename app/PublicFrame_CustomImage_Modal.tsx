import { useIsMobile } from './hooks/useAppState'
import { useCustomImageStore } from './hooks/useCustomImageStore'
import { useBackgroundStore } from './hooks/useBackgroundStore'
import { HTML_ACCEPT_ATTRIBUTES } from './ImageMange/imageConfig'

/**
 * 组件属性接口
 * 现在所有状态都通过 useBackgroundStore 管理，不需要props
 */
interface PublicFrameCustomImageModalProps {
    // 所有状态现在通过 useBackgroundStore 管理，不需要props
}

/**
 * 自定义图片选择器组件 - 支持移动端和PC端不同的UI展示
 * 移动端使用模态框样式，PC端使用弹出框样式
 * 支持拖拽上传和点击选择图片功能
 *
 * @param {PublicFrameCustomImageModalProps} props - 组件属性
 * @returns {JSX.Element} 渲染的图片选择器组件
 */
export const PublicFrame_CustomImage_Modal = ({}: PublicFrameCustomImageModalProps) => {
    // 检测是否为移动端设备
    const isMobile = useIsMobile()

    // 使用背景存储获取状态
    const { isImagePickerVisible, toggleImagePicker } = useBackgroundStore()

    // 使用自定义Zustand存储
    const { image, isDragging, error, uploadImage, clearImage, setDragging } = useCustomImageStore()

    /**
     * 处理关闭按钮点击事件
     * 关闭图片选择器模态框
     */
    const handleCloseClick = (): void => {
        // 通过 useBackgroundStore 关闭弹窗
        toggleImagePicker(false)
    }

    /**
     * 处理文件选择事件
     * 当用户选择图片文件时触发
     * @param {Event} event - 文件选择事件
     */
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>): void => {
        const file = event.target.files?.[0]
        if (file) {
            uploadImage(file)
        }
    }

    /**
     * 处理拖拽放置事件
     * 当用户拖拽图片到区域时触发
     * @param {DragEvent} event - 拖拽事件
     */
    const handleDrop = (event: React.DragEvent<HTMLDivElement>): void => {
        event.preventDefault()
        setDragging(false)

        const files = event.dataTransfer.files
        if (files.length > 0) {
            const file = files[0]
            uploadImage(file)
        }
    }

    /**
     * 处理拖拽悬停事件
     * 阻止默认行为以允许拖拽
     * @param {DragEvent} event - 拖拽事件
     */
    const handleDragOver = (event: React.DragEvent<HTMLDivElement>): void => {
        event.preventDefault()
        setDragging(true)
    }

    /**
     * 触发文件选择器
     * 模拟点击隐藏的文件输入框
     */
    const triggerFileSelect = (): void => {
        const fileInput = document.querySelector('.custom-image-file-input') as HTMLInputElement
        if (fileInput) {
            fileInput.click()
        } else {
            console.warn('未找到文件输入框元素')
        }
    }

    // 如果弹窗不可见，则不渲染任何内容
    if (!isImagePickerVisible) {
        return null
    }

    /**
     * 渲染移动端版本的图片选择器
     * 使用模态框样式，包含标题栏和关闭按钮
     *
     * @returns {JSX.Element} 移动端图片选择器
     */
    const mobileRender = () => {
        return (
            <div
                className='modal-container'
                style={{ background: 'transparent', justifyContent: 'center', opacity: 1 }}
            >
                <div
                    className='modal-sheet sheet-type undefined'
                    style={{
                        width: 'max-content',
                        maxWidth: 'max-content',
                        height: 'max-content',
                        maxHeight: 'max-content',
                        transform: 'none',
                    }}
                >
                    <div className='modal-head modal-title-bar'>
                        <h4>Custom Image</h4>
                        <button
                            type='button'
                            className='button icon-button small-button secondary-button undefined-blur true-round undefined-active undefined'
                            style={{ flexDirection: 'row' }}
                            onClick={handleCloseClick}
                        >
                            <svg
                                xmlns='http://www.w3.org/2000/svg'
                                fill='currentColor'
                                viewBox='0 0 24 24'
                            >
                                <path d='M4.362 17.793c-.48.48-.49 1.332.01 1.831.51.5 1.361.49 1.832.02L12 13.846l5.788 5.788c.49.49 1.332.49 1.831-.01.5-.51.5-1.341.01-1.831l-5.788-5.788 5.788-5.798c-.49-.49.5-1.332-.01-1.831-.499-.5-1.341-.5-1.83-.01L12 10.154 6.204 4.366c-.47-.48-1.332-.5-1.832.01-.5.5-.49 1.361-.01 1.831l5.788 5.798z' />
                            </svg>
                        </button>
                    </div>
                    <div id='modalScrollView' className='modal-scroll-view'>
                        <div className='content-view undefined'>
                            <div
                                className='btn-dropzone'
                                onDrop={handleDrop}
                                onDragOver={handleDragOver}
                                onDragEnter={e => {
                                    e.preventDefault()
                                    setDragging(true)
                                }}
                                onDragLeave={e => {
                                    e.preventDefault()
                                    setDragging(false)
                                }}
                                onClick={triggerFileSelect}
                            >
                                <div className='file-drop' />
                                <div className='empty-drop trans-200'>
                                    <div className='content default'>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                            />
                                        </svg>
                                        <span>Choose image</span>
                                        <p>Or drop it here</p>
                                    </div>
                                    <div className='content hovered'>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                fillRule='evenodd'
                                                d='M12.97 11.16h3.88c.46 0 .83.37.83.83s-.38.83-.84.83h-3.89v3.88c0 .46-.38.83-.84.83-.47 0-.84-.38-.84-.84V12.8H7.38c-.47 0-.84-.38-.84-.84 0-.47.37-.84.83-.84h3.88V7.23a.83.83 0 1 1 1.66-.01v3.88ZM5.06 4.92c-3.91 3.9-3.91 10.23 0 14.14 3.9 3.9 10.23 3.9 14.14 0 3.9-3.91 3.9-10.24 0-14.15a10 10 0 0 0-14.15 0Z'
                                            />
                                        </svg>
                                        <span>Choose image</span>
                                        <p>Or drop it here</p>
                                    </div>
                                </div>
                                <input
                                    className='d-none custom-image-file-input'
                                    accept={HTML_ACCEPT_ATTRIBUTES.LEGACY_IMAGES}
                                    type='file'
                                    onChange={handleFileSelect}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    /**
     * 渲染PC端版本的图片选择器
     * 使用弹出框样式，结构更加紧凑
     *
     * @returns {JSX.Element} PC端图片选择器
     */
    const pcRender = () => {
        return (
            <div
                className='popover undefined'
                style={{
                    top: '351.383px',
                    right: 'unset',
                    bottom: 'unset',
                    left: 240,
                    width: 228,
                    height: 'auto',
                    margin: '-80px 0px 0px',
                    filter: 'blur(0px)',
                    opacity: 1,
                    transform: 'none',
                }}
            >
                <div className='v-stack'>
                    <div className='scroll'>
                        <div className='v-stack-content' style={{ gap: 12, padding: 12 }}>
                            <span className='h5'>Custom image</span>
                            <div
                                className={`btn-dropzone ${isDragging ? 'active-dropzone' : ''}`}
                                onDrop={handleDrop}
                                onDragOver={handleDragOver}
                                onDragEnter={e => {
                                    e.preventDefault()
                                    setDragging(true)
                                }}
                                onDragLeave={e => {
                                    e.preventDefault()
                                    setDragging(false)
                                }}
                                onClick={triggerFileSelect}
                            >
                                <div className='file-drop' />
                                <div className='empty-drop trans-200'>
                                    <div className='content default'>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                            />
                                        </svg>
                                        <span>Choose image</span>
                                        <p>Or drop it here</p>
                                    </div>
                                    <div className='content hovered'>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                fillRule='evenodd'
                                                d='M12.97 11.16h3.88c.46 0 .83.37.83.83s-.38.83-.84.83h-3.89v3.88c0 .46-.38.83-.84.83-.47 0-.84-.38-.84-.84V12.8H7.38c-.47 0-.84-.38-.84-.84 0-.47.37-.84.83-.84h3.88V7.23a.83.83 0 1 1 1.66-.01v3.88ZM5.06 4.92c-3.91 3.9-3.91 10.23 0 14.14 3.9 3.9 10.23 3.9 14.14 0 3.9-3.91 3.9-10.24 0-14.15a10 10 0 0 0-14.15 0Z'
                                            />
                                        </svg>
                                        <span>Choose image</span>
                                        <p>Or drop it here</p>
                                    </div>
                                </div>

                                {/* 用户拖拽图片放在设备上时显示的提示 */}
                                <div
                                    className='active-drop'
                                    style={{
                                        opacity: isDragging ? '1' : '0', // // 我自己增加的样式
                                    }}
                                >
                                    <div className='content'>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                fillRule='evenodd'
                                                d='M22.141 7.756q.858 1.99.859 4.25 0 2.248-.859 4.237a11.1 11.1 0 0 1-2.386 3.508 11.4 11.4 0 0 1-3.516 2.384q-1.99.864-4.239.865-2.25 0-4.24-.865a11.4 11.4 0 0 1-3.516-2.378 11 11 0 0 1-2.386-3.5A10.6 10.6 0 0 1 1 12.02a10.6 10.6 0 0 1 .858-4.25 11.2 11.2 0 0 1 2.386-3.515 11.3 11.3 0 0 1 3.516-2.39A10.5 10.5 0 0 1 12 .999q2.249 0 4.239.866a11.4 11.4 0 0 1 3.516 2.383 11.1 11.1 0 0 1 2.386 3.508M12.736 6.68q-.369-.368-.75-.368-.368 0-.736.368l-3.64 3.61q-.245.245-.245.654a.886.886 0 0 0 .913.913.97.97 0 0 0 .668-.273l1.159-1.171 1.049-1.308-.109 2.506v5.163q0 .409.266.674a.91.91 0 0 0 .675.266.91.91 0 0 0 .675-.266.91.91 0 0 0 .265-.674v-5.163l-.095-2.52 1.05 1.322 1.145 1.171a.9.9 0 0 0 .667.273.92.92 0 0 0 .662-.259.88.88 0 0 0 .265-.654.89.89 0 0 0-.259-.654z'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <input
                                    className='d-none custom-image-file-input'
                                    accept={HTML_ACCEPT_ATTRIBUTES.LEGACY_IMAGES}
                                    type='file'
                                    onChange={handleFileSelect}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    // 根据设备类型返回对应的渲染结果
    if (isMobile) {
        return mobileRender()
    } else {
        return pcRender()
    }
}
