import { useIsMobile } from './hooks/useAppState'

export const Public_MockupDetails = () => {
    // 🔥 重要：hooks必须在组件顶部调用，不能在条件语句中
    const isMobile = useIsMobile()

    const publicRender = () => {
        return (
            <>
                <div className='mockup-details'>
                    <div className='row'>
                        <span>Device</span>
                        <p>iPhone 15 Plus</p>
                    </div>
                    <div className='row'>
                        <span>Screen pixels</span>
                        <p>430 / 932</p>
                    </div>
                    <div className='row'>
                        <span>Release year</span>
                        <p>2023</p>
                    </div>
                </div>
            </>
        )
    }
    const mobileInViewRender = () => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile undefined'
                style={{ opacity: 1, transform: 'none' }}
            >
                {publicRender()}
            </div>
        )
    }
    const pcInViewRender = () => {
        return (
            <div className='panel-control undefined '>
                <span className='label gray-text'>details</span>
                <div className='controls'>{publicRender()}</div>
            </div>
        )
    }

    if (isMobile) {
        return mobileInViewRender()
    }
    return pcInViewRender()
}
