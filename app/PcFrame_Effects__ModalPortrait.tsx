import { useIsMobile } from './hooks/useAppState'

export const PcFrame_Effects__ModalPortrait = () => {
    return (
        <div
            className='popover undefined'
            style={{
                top: '282.594px',
                right: 'unset',
                bottom: 'unset',
                left: 240,
                width: 236,
                height: 'auto',
                margin: '-40px 0px 0px',
                filter: 'blur(0px)',
                opacity: 1,
                transform: 'none',
            }}
        >
            <div className='v-stack'>
                <div className='scroll'>
                    <div className='v-stack-content' style={{ gap: 12, padding: 10 }}>
                        <div className='scene-popover-header'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/image/portrait-scene-header.png'
                            />
                            <span>Portrait</span>
                        </div>
                        <div className='panel-control-grid col-3'>
                            <div className='panel-button undefined true-active has-label'>
                                <div className='preview' style={{ aspectRatio: '3 / 2' }}>
                                    <div className='icon'>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                fillRule='evenodd'
                                                d='M3.575 7.088A9.7 9.7 0 0 0 2.25 12c0 5.384 4.365 9.75 9.75 9.75 1.79 0 3.468-.483 4.911-1.326l-1.104-1.104A8.25 8.25 0 0 1 3.75 12a8.2 8.2 0 0 1 .929-3.808zm15.686 8.831A8.25 8.25 0 0 0 12 3.75a8.2 8.2 0 0 0-3.92.988L6.981 3.639A9.7 9.7 0 0 1 12 2.25c5.384 0 9.75 4.365 9.75 9.75a9.7 9.7 0 0 1-1.39 5.018z'
                                            />
                                            <rect
                                                width='1.89'
                                                height='26.833'
                                                x='1.788'
                                                y='3.211'
                                                fill='currentColor'
                                                rx='0.945'
                                                ry='0.945'
                                                transform='rotate(-45 1.789 3.211)'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='label-wrapper'>
                                    <span className='footnote truncate'>none</span>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active has-label'>
                                <div className='preview' style={{ aspectRatio: '3 / 2' }}>
                                    <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                        <div
                                            className='plus-badge'
                                            style={{ width: 16, height: 16 }}
                                        >
                                            <svg
                                                xmlns='http://www.w3.org/2000/svg'
                                                viewBox='0 0 24 24'
                                            >
                                                <path
                                                    fill='currentColor'
                                                    d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                                />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='default mode'
                                            src='/image/lens-scene-default.png'
                                        />
                                    </div>
                                </div>
                                <div className='label-wrapper'>
                                    <span className='footnote truncate'>Lens Blur</span>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active has-label'>
                                <div className='preview' style={{ aspectRatio: '3 / 2' }}>
                                    <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                        <div
                                            className='plus-badge'
                                            style={{ width: 16, height: 16 }}
                                        >
                                            <svg
                                                xmlns='http://www.w3.org/2000/svg'
                                                viewBox='0 0 24 24'
                                            >
                                                <path
                                                    fill='currentColor'
                                                    d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                                />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='stage mode'
                                            src='/image/lens-scene-stage.png'
                                        />
                                    </div>
                                </div>
                                <div className='label-wrapper'>
                                    <span className='footnote truncate'>stage</span>
                                </div>
                            </div>
                        </div>
                        <div className='panel-control undefined'>
                            <span className='label gray-text'>adjust</span>
                            <div className='controls'>
                                <form>
                                    <span
                                        dir='ltr'
                                        data-orientation='horizontal'
                                        aria-disabled='false'
                                        className='slider-component true-disabled'
                                        style={{
                                            width: '100%',
                                            '--radix-slider-thumb-transform': 'translateX(-50%)',
                                        }}
                                    >
                                        <span
                                            data-orientation='horizontal'
                                            className='SliderTrack track'
                                        >
                                            <span
                                                data-orientation='horizontal'
                                                className='SliderRange rail'
                                                style={{ left: '0%', right: '50%' }}
                                            />
                                        </span>
                                        <span
                                            style={{
                                                transform: 'var(--radix-slider-thumb-transform)',
                                                position: 'absolute',
                                                left: 'calc(50% + 0px)',
                                            }}
                                        >
                                            <span
                                                role='slider'
                                                aria-label='Volume'
                                                aria-valuemin={10}
                                                aria-valuemax={90}
                                                aria-orientation='horizontal'
                                                data-orientation='horizontal'
                                                tabIndex={0}
                                                className='SliderThumb thumb'
                                                data-radix-collection-item=''
                                                aria-valuenow={50}
                                                style={{}}
                                            />
                                            <input defaultValue={50} style={{ display: 'none' }} />
                                        </span>
                                        <div className='labels'>
                                            <span id='distance-slider-label'>Distance</span>
                                            <span>5</span>
                                        </div>
                                    </span>
                                </form>
                                <form>
                                    <span
                                        dir='ltr'
                                        data-orientation='horizontal'
                                        aria-disabled='false'
                                        className='slider-component true-disabled hide-rail'
                                        style={{
                                            width: '100%',
                                            '--radix-slider-thumb-transform': 'translateX(-50%)',
                                        }}
                                    >
                                        <span
                                            data-orientation='horizontal'
                                            className='SliderTrack track'
                                        >
                                            <span
                                                data-orientation='horizontal'
                                                className='SliderRange rail'
                                                style={{ left: '0%', right: '50%' }}
                                            />
                                        </span>
                                        <span
                                            style={{
                                                transform: 'var(--radix-slider-thumb-transform)',
                                                position: 'absolute',
                                                left: 'calc(50% - 4.44089e-16px)',
                                            }}
                                        >
                                            <span
                                                role='slider'
                                                aria-label='Volume'
                                                aria-valuemin={15}
                                                aria-valuemax={85}
                                                aria-orientation='horizontal'
                                                data-orientation='horizontal'
                                                tabIndex={0}
                                                className='SliderThumb thumb'
                                                data-radix-collection-item=''
                                                aria-valuenow={50}
                                                style={{}}
                                            />
                                            <input defaultValue={50} style={{ display: 'none' }} />
                                        </span>
                                        <div className='labels'>
                                            <span id='position-slider-label'>Position</span>
                                            <span>50</span>
                                        </div>
                                    </span>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
