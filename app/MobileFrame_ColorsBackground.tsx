import { useIsMobile } from '@/app/hooks/useAppState'
import { useBackgroundStore, BackgroundTypeEnum } from './hooks/useBackgroundStore'
import { useMagicBackgroundStore, MagicBackgroundType } from './hooks/useMagicBackgroundStore'
import { useMagicBackground } from './hooks/useMagicBackground'

export const MobileFrame_ColorsBackground = () => {
    const isMobile = useIsMobile()
    
    // 使用魔法背景统一Hook
    const { setMagicBackground } = useMagicBackground()

    // 预定义的魔法渐变背景数据，与主系统保持一致
    const magicGradients = [
        {
            id: 'gradient-1',
            type: MagicBackgroundType.GRADIENT,
            value: {
                background: 'linear-gradient(140deg, rgb(255, 100, 50) 12.8%, rgb(255, 0, 101) 43.52%, rgb(123, 46, 255) 84.34%)',
            },
            label: 'Sunset Vibes',
        },
        {
            id: 'gradient-2',
            type: MagicBackgroundType.GRADIENT,
            value: {
                background: 'linear-gradient(140deg, rgb(244, 229, 240), rgb(229, 54, 171), rgb(92, 3, 188), rgb(14, 7, 37))',
            },
            label: 'Purple Dream',
        },
        {
            id: 'gradient-3',
            type: MagicBackgroundType.GRADIENT,
            value: {
                background: 'linear-gradient(135deg, rgb(238, 221, 243), rgb(238, 146, 177), rgb(99, 48, 180))',
            },
            label: 'Rose Magic',
        },
    ]

    // 移动端扩展的渐变背景
    const mobileGradients = [
        ...magicGradients,
        {
            id: 'gradient-4',
            type: MagicBackgroundType.GRADIENT,
            value: {
                background: 'linear-gradient(113.96deg, rgb(69, 190, 232) 13.54%, rgb(214, 161, 172) 50%, rgb(232, 140, 93) 85.42%)',
            },
            label: 'Ocean Sunset',
        },
        {
            id: 'gradient-5',
            type: MagicBackgroundType.GRADIENT,
            value: {
                background: 'linear-gradient(113.96deg, rgb(69, 233, 159) 11.98%, rgb(213, 168, 155) 50%, rgb(232, 70, 152) 85.42%)',
            },
            label: 'Mint Rose',
        },
        {
            id: 'gradient-6',
            type: MagicBackgroundType.GRADIENT,
            value: {
                background: 'linear-gradient(113.96deg, rgb(69, 223, 232) 11.98%, rgb(211, 170, 175) 50%, rgb(232, 103, 100) 85.42%)',
            },
            label: 'Coral Wave',
        },
    ]

    // 获取当前要显示的渐变列表
    const currentGradients = isMobile ? mobileGradients : magicGradients

    /**
     * 处理渐变背景选择
     * 切换到魔法背景类型并设置对应的渐变
     */
    const handleGradientSelect = (gradient: any) => {
        setMagicBackground(gradient.type, gradient.value)
    }

    const publicRender = () => {
        return (
            <>
                {currentGradients.map((gradient, index) => (
                    <button
                        key={gradient.id}
                        className='background-item gradient-item'
                        onClick={() => handleGradientSelect(gradient)}
                    >
                        <div className={`display ${index === 0 ? 'active' : 'false'}`}>
                            <div
                                style={{
                                    background: gradient.value.background,
                                }}
                            />
                        </div>
                    </button>
                ))}
            </>
        )
    }
    const publicMagicExpandButton = () => {
        return (
            // is-expanded 点击之后增加的类名
            <div className='expand-button '>
                <div className='items-preview'>
                    <button className='background-item image-item'>
                        <div className='display false'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='data:,'
                                alt='back'
                            />
                        </div>
                    </button>
                    <button className='background-item image-item'>
                        <div className='display false'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='data:,'
                                alt='back'
                            />
                        </div>
                    </button>
                </div>
                <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                    <path
                        fill='currentColor'
                        d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                    />
                </svg>
            </div>
        )
    }
    const mobileInViewRender = () => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile pack-control-mobile'
                style={{ opacity: 1, transform: 'none' }}
            >
                <div className='panel-control-stack'>
                    <div className='stack-content'>{publicRender()}</div>
                </div>
            </div>
        )
    }
    const pcInViewRender = () => {
        return (
            <div className='backpack-new'>
                <div className='title'>
                    <span className='caption2 font-weight-medium'>Cosmic Gradient</span>
                </div>
                <div className='backpack-colors-list image-list'>{publicRender()}</div>
            </div>
        )
    }
    if (useIsMobile()) {
        return mobileInViewRender()
    }
    return pcInViewRender()
}
