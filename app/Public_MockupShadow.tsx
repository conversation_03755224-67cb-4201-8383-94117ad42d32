import React from 'react'
import { useIsMobile } from './hooks/useAppState'
import { PublicSliderComponent } from './components/Public_SliderComponet'
import { DragPad } from './components/DragPad/DragPad'
// 1. 导入 ShadowTypeEnum
import { useMockupShadowStore, ShadowTypeEnum } from './hooks/useMockupShadowStore'

/**
 * 位置坐标接口
 */
interface Position {
    x: number
    y: number
}

export const Public_MockupShadow = () => {
    const activeTabs = {
        basic: {
            value: 'basic',
            label: 'Basic',
        },
        adjustLight: {
            value: 'adjustLight',
            label: 'Adjust Light',
        },
    }
    const [activeShadow, setActiveShadow] = React.useState<string>(activeTabs.basic.value)

    const isMobile = useIsMobile()

    // 使用全局阴影状态管理
    const {
        shadowStyles, // 从 store 中获取样式列表
        shadowType,
        opacity,
        position,
        setShadowType,
        setOpacity,
        setPosition,
    } = useMockupShadowStore()

    /**
     * 处理拖拽位置变化
     * @param position 新位置
     */
    const handlePositionChange = (position: { x: number; y: number }) => {
        setPosition(position)
    }

    // 根据设备类型决定显示的样式列表
    let itemList = isMobile ? shadowStyles.slice(0, 3) : shadowStyles

    const handleStyleTabClick = (label: ShadowTypeEnum) => {
        setShadowType(label)
    }

    const publicForm = () => {
        return (
            <PublicSliderComponent
                value={opacity}
                setValue={setOpacity}
                config='mockup_shadow_opacity'
                disabled={shadowType === ShadowTypeEnum.None} // 2. 使用枚举进行比较
            />
        )
    }

    const publicRender = () => {
        return (
            <>
                {itemList.map(item => {
                    return (
                        <div
                            key={item.label}
                            className={`panel-button undefined ${
                                shadowType === item.label ? 'true-active' : 'false-active'
                            } has-label `}
                            onClick={() => handleStyleTabClick(item.label)}
                        >
                            <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                <div className='image-wrapper'>
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src={item.src}
                                    />
                                </div>
                            </div>
                            <div className='label-wrapper'>
                                <span className='footnote truncate'>{item.label}</span>
                            </div>
                        </div>
                    )
                })}
            </>
        )
    }
    const mobileInViewRender = () => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile undefined'
                style={{ opacity: 1, transform: 'none' }}
            >
                <div className='panel-control-segment-wrapper' style={{ flexDirection: 'column' }}>
                    {activeShadow === activeTabs.basic.value && (
                        <section
                            className='segment-section'
                            //  style={{ display: 'none' }}
                        >
                            <div style={{ display: 'flex', justifyContent: 'space-evenly' }}>
                                {publicRender()}
                            </div>
                            {publicForm()}
                        </section>
                    )}
                    {activeShadow === activeTabs.adjustLight.value && (
                        <section
                            className='segment-section'
                            // style={{ display: 'none' }}
                        >
                            <div style={{ display: 'flex', justifyContent: 'center' }}>
                                <DragPad
                                    isMobile={isMobile}
                                    initialPosition={position}
                                    onPositionChange={handlePositionChange}
                                />
                            </div>
                        </section>
                    )}

                    <div className='segment-buttons'>
                        {Object.keys(activeTabs).map(key => {
                            return (
                                <button
                                    onClick={() => {
                                        setActiveShadow(key as keyof typeof activeTabs)
                                    }}
                                    key={key}
                                    type='button'
                                    className={`button default-button small-button undefined-button undefined-blur true-round ${
                                        activeShadow === key ? 'true-active' : 'false-active'
                                    } undefined`}
                                    style={{ flexDirection: 'row', minWidth: 88 }}
                                >
                                    <span>{activeTabs[key as keyof typeof activeTabs].label}</span>
                                </button>
                            )
                        })}
                    </div>
                </div>
            </div>
        )
    }
    const pcInViewRender = () => {
        return (
            <div className='panel-control undefined'>
                <span className='label gray-text'>Shadow</span>
                <div className='controls'>
                    <div style={{ display: 'flex', gap: 7 }}>
                        {publicRender()}
                        {/*  */}
                    </div>
                    {publicForm()}
                    <button
                        type='button'
                        className='button default-button tiny-button undefined-button undefined-blur undefined-round undefined-active gray-text2 bg-gray-icon'
                        style={{ flexDirection: 'row', minWidth: '100%' }}
                        disabled={shadowType === 'None'}
                    >
                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                            <path
                                fill='currentColor'
                                d='M18.56 14.01c1.9 0 3.43 1.51 3.43 3.37s-1.54 3.37-3.44 3.37-3.44-1.52-3.44-3.38c0-1.87 1.54-3.38 3.43-3.38Zm-8.48 1.93c.83 0 1.5.66 1.5 1.48 0 .81-.68 1.48-1.51 1.48H3.49c-.84 0-1.51-.67-1.51-1.49s.67-1.49 1.5-1.49h6.57ZM5.43 2.98c1.89 0 3.43 1.51 3.43 3.37S7.32 9.72 5.42 9.72 1.98 8.2 1.98 6.34c0-1.87 1.53-3.38 3.43-3.38Zm15.05 1.89c.83 0 1.5.66 1.5 1.48 0 .81-.68 1.48-1.51 1.48h-6.58c-.84 0-1.51-.67-1.51-1.49s.67-1.49 1.5-1.49h6.57Z'
                            />
                        </svg>
                        <span>Adjust Light</span>
                    </button>
                    <div style={{ display: 'flex', justifyContent: 'center', paddingTop: 8 }}>
                        <DragPad
                            isMobile={isMobile}
                            initialPosition={position}
                            onPositionChange={handlePositionChange}
                        />
                    </div>
                </div>
            </div>
        )
    }
    if (isMobile) {
        return mobileInViewRender()
    }
    return pcInViewRender()
}
