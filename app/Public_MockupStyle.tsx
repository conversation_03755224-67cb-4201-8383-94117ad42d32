import { useIsMobile } from '@/app/hooks/useAppState'
import { useMockupStore, MockupStyleEnum } from '@/app/hooks/useMockupStore'

export const Public_MockupStyle = () => {
    // 从全局store获取样式数据和状态
    const { styles, activeStyle, setActiveStyle } = useMockupStore()
    const publicRender = () => {
        return (
            <>
                {styles.map(item => {
                    return (
                        <div
                            key={item.label}
                            className={`panel-button undefined ${
                                activeStyle === item.label ? 'true-active' : 'false-active'
                            } has-label `}
                            onClick={() => setActiveStyle(item.label)}
                        >
                            <div className='preview' style={{ aspectRatio: '4 / 3' }}>
                                {item.label === MockupStyleEnum.Display && (
                                    <div className='mock-style-3d-tag'>3D</div>
                                )}
                                <div className='image-wrapper'>
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src={item.src}
                                    />
                                </div>
                            </div>
                            <div className='label-wrapper'>
                                <span className='footnote truncate'>{item.label}</span>
                            </div>
                        </div>
                    )
                })}
            </>
        )
    }

    const mobileInViewRender = () => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile undefined'
                style={{ opacity: 1, transform: 'none' }}
            >
                <div className='panel-control-stack'>
                    <div className='stack-content'>{publicRender()}</div>
                </div>
            </div>
        )
    }
    const pcInViewRender = () => {
        return (
            <div className='panel-control undefined '>
                <span className='label gray-text'>style</span>
                <div className='controls'>
                    <div className='panel-control-grid col-3'>{publicRender()}</div>
                </div>
            </div>
        )
    }
    if (useIsMobile()) {
        return mobileInViewRender()
    }
    return pcInViewRender()
}
