import { useIsMobile, useViewDimensions, useViewRatio } from '@/app/hooks/useAppState'
import { Public_FrameSizeModal } from './Public_FrameSizeModal'

/**
 * Public_MockUp_SizeSelector 组件的属性接口
 * @interface Public_MockUp_SizeSelectorProps
 */
interface Public_MockUp_SizeSelectorProps {
    /** 是否激活尺寸选择器模态框 */
    isActiveModel: boolean
    /** 设置尺寸选择器模态框状态的回调函数 */
    setIsActiveModel: (isActiveModel: boolean) => void
}

/**
 * 公共尺寸选择器组件
 * 提供移动端和桌面端不同的显示样式和交互逻辑
 * @param props Public_MockUp_SizeSelectorProps
 * @returns React.ReactElement
 */
export const Public_MockUp_SizeSelector = ({
    isActiveModel,
    setIsActiveModel,
}: Public_MockUp_SizeSelectorProps) => {
    const isMobile = useIsMobile()
    const viewDimensions = useViewDimensions()
    const viewRatio = useViewRatio()

    /**
     * 处理点击事件，切换模态框显示状态
     */
    const handleClick = (): void => {
        setIsActiveModel(!isActiveModel)
    }

    /**
     * 渲染公共内容部分
     * 包含预览图标、详细信息和箭头图标
     * @returns React.ReactElement
     */
    const publicRender = (): React.ReactElement => {
        return (
            <>
                <div className='preview'>
                    <div className='frame-preview'>
                        <div className='current-frame-icon' style={{ aspectRatio: '4 / 3' }} />
                    </div>
                </div>
                <div className='details'>
                    <p>
                        <span className='text-capitalize'>default</span> {viewRatio.ratioWidth}:
                        {viewRatio.ratioHeight}
                    </p>
                    <span>
                        {viewDimensions.useWidth} × {viewDimensions.useHeight}
                    </span>
                </div>
                {isMobile ? (
                    <div className='icon'>
                        <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                            <path
                                fill='currentColor'
                                d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                            />
                        </svg>
                        <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                            <path
                                fill='currentColor'
                                d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                            />
                        </svg>
                    </div>
                ) : (
                    <div
                        className='chevron'
                        style={{
                            transform: isActiveModel ? 'rotate(180deg)' : 'rotate(0deg)',
                        }}
                    >
                        <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                            <path
                                fill='currentColor'
                                d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                            />
                        </svg>
                    </div>
                )}
            </>
        )
    }

    /**
     * 渲染移动端视图
     * 使用按钮形式，支持点击切换状态
     * @returns React.ReactElement
     */
    const mobileInViewRender = (): React.ReactElement => {
        return (
            <>
                <button
                    onClick={handleClick}
                    type='button'
                    className={`button icon-button large-button undefined-button undefined-blur undefined-round ${isActiveModel ? 'true-active' : 'false-active'} panel-picker-button-mobile frame-picker-button-mobile`}
                    style={{ flexDirection: 'row' }}
                >
                    {publicRender()}
                </button>
            </>
        )
    }

    /**
     * 渲染桌面端视图
     * 使用下拉框形式，包含模态框组件
     * @returns React.ReactElement
     */
    const pcInViewRender = (): React.ReactElement => {
        return (
            <>
                <div className='panel-selector'>
                    <div className='dropdown frame-picker-desktop-dropdown'>
                        <div className='button-wrapper' onClick={handleClick}>
                            <button className='panel-selector-btn-desktop'>{publicRender()}</button>
                        </div>

                        {/* 框架尺寸选择模态框 */}
                        {isActiveModel && <Public_FrameSizeModal />}
                    </div>
                </div>
            </>
        )
    }

    // 根据设备类型返回对应的渲染结果
    // 🔥 重要：hooks必须在组件顶部调用，不能在条件语句中
    // isMobile已在组件顶部声明

    if (isMobile) {
        return mobileInViewRender()
    } else {
        return pcInViewRender()
    }
}
