import { useIsMobile } from '@/app/hooks/useAppState'
import { Public_MockupModal } from './Public_MockupModal'

interface Public_MockUp_ModelSelectorProps {
    isActiveModel: boolean
    setIsActiveModel: (isActiveModel: boolean) => void
}

export const Public_MockUp_ModelSelector = ({
    isActiveModel,
    setIsActiveModel,
}: Public_MockUp_ModelSelectorProps) => {
    const mobileInViewRender = () => {
        return (
            <button
                onClick={() => {
                    setIsActiveModel(!isActiveModel)
                }}
                type='button'
                className={`button icon-button large-button undefined-button undefined-blur undefined-round ${isActiveModel ? 'true-active' : 'false-active'} panel-picker-button-mobile`}
                style={{ flexDirection: 'row' }}
            >
                <div className='preview'>
                    <img
                        crossOrigin='anonymous'
                        loading='lazy'
                        decoding='async'
                        alt='Thumbnail'
                        src='/mockups/iPhone 15 Plus/thumbs/1.png'
                    />
                </div>
                <div className='details'>
                    <p>iPhone 15 Plus</p>
                    <span>430 / 932</span>
                </div>
                <div className='icon'>
                    <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                        />
                    </svg>
                    <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                        />
                    </svg>
                </div>
            </button>
        )
    }
    const pcInViewRender = () => {
        return (
            <div className='panel-selector'>
                {/* <div className='dropdown mockup-picker-desktop-dropdown is-active'> */}
                <div className='dropdown mockup-picker-desktop-dropdown'>
                    <div
                        className='button-wrapper'
                        onClick={() => {
                            setIsActiveModel(!isActiveModel)
                        }}
                    >
                        <button className='panel-selector-btn-desktop'>
                            <div className='current-mock'>
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    alt='Thumbnail'
                                    src='/mockups/iPhone 15 Plus/thumbs/1.png'
                                />
                            </div>
                            <div className='details'>
                                <p>iPhone 15 Plus</p>
                                <span>430 / 932</span>
                            </div>
                            <div
                                className='chevron'
                                style={{
                                    transform: isActiveModel ? 'rotate(180deg)' : 'rotate(0deg)',
                                }}
                            >
                                <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                >
                                    <path
                                        fill='currentColor'
                                        d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                                    />
                                </svg>
                            </div>
                        </button>
                    </div>

                    {/* 弹窗内容 */}
                    <Public_MockupModal
                        isActiveModel={isActiveModel}
                        setIsActiveModel={setIsActiveModel}
                    />
                </div>
            </div>
        )
    }

    // 🔥 重要：hooks必须在组件顶部调用，不能在条件语句中
    const isMobile = useIsMobile()

    if (isMobile) {
        return mobileInViewRender()
    }

    return pcInViewRender()
}
