import { useColorGenerationFromImage } from '@/app/hooks/useColorGenerationFromImage'

/**
 * @description 为单个图片路径生成颜色数据的Hook
 * @param imagePath - 图片路径
 * @returns 颜色数据对象
 */
export function useColorData(imagePath: string) {
    const { solidColors, gradientColors, meshColors, isLoading, error } =
        useColorGenerationFromImage(imagePath, {
            colorCount: 5,
            quality: 8,
        })

    const imageBackgrounds = solidColors

    return {
        solidColors: solidColors || [],
        gradientColors: gradientColors || [],
        meshColors: meshColors || [],
        imageBackgrounds: imageBackgrounds || [],
        DEFAULT_WALLPAPER_PATH: imagePath,
        isLoading,
        error,
    }
}

/**
 * @description 为多个图片路径批量生成颜色数据的Hook
 * @param imagePaths - 图片路径数组
 * @returns 颜色数据对象数组
 */
export function useMultipleColorData(imagePaths: string[]) {
    return imagePaths.map(imagePath => useColorData(imagePath))
}
