# `pixel-storm.backup.ts` 脚本解释文档

## 脚本目的

`pixel-storm.backup.ts` 是我们在寻求"像素级完美"颜色匹配过程中的一个重要版本。它的核心策略是**"像素风暴" (Pixel Storm)**，其设计目的是解决之前颜色提取库可能存在的"漏报"问题，确保只要一个颜色在图片中存在，就一定能被找到。

## 核心技术与依赖

- **核心依赖**: `get-pixels`
    - 这是一个用于从图像文件中提取像素数据的 Node.js 库。我们选择它来实现对图像所有像素的地毯式扫描。
- **辅助依赖**:
    - `color-diff`: 用于计算两种颜色之间的视觉感知差异（Delta E），当找不到精确匹配时，用它来寻找最佳替代品。
    - `fs`, `path`: Node.js 内置模块，用于文件和路径操作。

## 算法逻辑详解

脚本的执行流程如下：

1.  **加载测试用例**: 脚本首先会加载定义在文件内部的 `testCases` 数组，其中包含了要测试的图片路径和对应的期望颜色列表（`expectedSolidColors`）。

2.  **构建终极候选池**:

    - 对于每一个测试用例，脚本使用 `get-pixels` 库读取图片文件。
    - 它会遍历返回结果中的每一个像素（在 `pixels.data` 中，数据以 `[R, G, B, A, R, G, B, A, ...]` 的一维数组形式存储）。
    - 每找到一个独一无二的 RGB 颜色组合，就将其存入一个 `Map` 数据结构中（`ultimatePool`）。`Map` 的键是格式化的 `rgb(r, g, b)` 字符串，值是颜色的元组 `[r, g, b]`。
    - 这个过程确保了我们获得了一个不重不漏的、包含该图像所有真实颜色的"终极候选池"。

3.  **解析期望颜色**:

    - 脚本从测试用例中获取期望颜色列表。由于原始数据中的颜色是以 `{ background: 'rgb(r, g, b)' }` 的对象形式存在的，脚本会使用 `parseRgbString` 辅助函数将其解析为内部计算所需的 `[r, g, b]` 元组格式。

4.  **匹配与查找**:

    - 脚本遍历每一个解析后的"期望颜色"。
    - **精确查找**: 首先，它会在"终极候选池" (`ultimatePool`) 中进行精确查找。如果找到了完全相同的 RGB 值，则记录为 **`✅ 像素级完美匹配`**，并将该颜色从候选池中移除，以防被重复匹配。
    - **智能匹配**: 如果精确查找失败，证明该颜色在图片中不存在。此时，脚本会启动智能匹配：
        - 它会使用 `color-diff` 库，计算当前"期望颜色"与候选池中**剩余的每一个颜色**之间的视觉差异（Delta E）。
        - 它会找到那个 `Delta E` 值最小的颜色，将其作为"最佳替代品"。
        - 记录为 **`❌ 不存在, 已匹配最佳替代品`**，并同样将这个被选中的替代品从候选池中移除。

5.  **结果展示**:
    - 在处理完一个测试用例的所有期望颜色后，脚本会用 `console.table` 将详细的匹配结果（期望色、匹配色、状态、Delta E 值）清晰地打印在控制台中。

## 与最终版本的差异

这个备份脚本的核心逻辑与最终版非常相似，最大的区别在于**核心引擎**：

- 此版本使用 `get-pixels`。
- 最终版本使用了 `sharp`，这是一个性能更强、功能更全面的工业级图像处理库，其提供的解码结果被认为是更权威的"地面实况"。

该备份文件是我们技术探索路径上的一个关键快照，记录了"像素风暴"策略的首次完整实现。
