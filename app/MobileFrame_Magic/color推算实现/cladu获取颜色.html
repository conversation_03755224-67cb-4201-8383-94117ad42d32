<!doctype html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>图片颜色提取器 - ColorThief版</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #ff6b6b, #ee5a52);
                color: white;
                padding: 40px;
                text-align: center;
            }

            .header h1 {
                font-size: 2.5rem;
                margin-bottom: 10px;
                font-weight: 700;
            }

            .header p {
                font-size: 1.1rem;
                opacity: 0.9;
            }

            .content {
                padding: 40px;
            }

            .upload-area {
                border: 3px dashed #ddd;
                border-radius: 15px;
                padding: 60px 20px;
                text-align: center;
                margin-bottom: 30px;
                transition: all 0.3s ease;
                cursor: pointer;
                background: #fafafa;
            }

            .upload-area:hover {
                border-color: #ff6b6b;
                background: #fff5f5;
            }

            .upload-area.dragover {
                border-color: #ff6b6b;
                background: #fff5f5;
                transform: scale(1.02);
            }

            .upload-icon {
                font-size: 4rem;
                color: #ddd;
                margin-bottom: 20px;
            }

            .upload-text {
                font-size: 1.2rem;
                color: #666;
                margin-bottom: 15px;
            }

            .upload-hint {
                color: #999;
                font-size: 0.9rem;
            }

            #imageInput {
                display: none;
            }

            .preview-section {
                display: none;
                margin-top: 30px;
            }

            .image-preview {
                text-align: center;
                margin-bottom: 30px;
            }

            .preview-image {
                max-width: 100%;
                max-height: 400px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            }

            .palette-section {
                background: #f8f9fa;
                padding: 30px;
                border-radius: 15px;
                margin-top: 30px;
            }

            .palette-title {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 20px;
                color: #333;
            }

            .color-palette {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }

            .color-item {
                background: white;
                border-radius: 12px;
                padding: 20px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
                transition: transform 0.2s ease;
                cursor: pointer;
            }

            .color-item:hover {
                transform: translateY(-5px);
            }

            .color-preview {
                width: 100%;
                height: 80px;
                border-radius: 8px;
                margin-bottom: 15px;
                border: 1px solid #eee;
            }

            .color-info {
                text-align: center;
            }

            .color-hex {
                font-family: 'Courier New', monospace;
                font-size: 1.1rem;
                font-weight: 600;
                color: #333;
                margin-bottom: 5px;
            }

            .color-rgb {
                font-size: 0.9rem;
                color: #666;
            }

            .loading {
                text-align: center;
                padding: 40px;
                color: #666;
            }

            .spinner {
                border: 4px solid #f3f3f3;
                border-top: 4px solid #ff6b6b;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    transform: rotate(360deg);
                }
            }

            .toast {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4caf50;
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                z-index: 1000;
            }

            .toast.show {
                opacity: 1;
                transform: translateX(0);
            }

            .library-info {
                background: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;
                color: #1976d2;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 图片颜色提取器</h1>
                <p>使用ColorThief库，专业级颜色提取</p>
            </div>

            <div class="content">
                <div class="library-info">
                    ✨ 本工具使用 ColorThief.js 库，与 Canva 和 imagecolorpicker.com 使用相同的算法
                </div>

                <div
                    class="library-info"
                    style="background: #fff3e0; border-color: #ff9800; color: #e65100"
                >
                    🖼️ 默认已加载 walller__1.jpg 测试图片，您也可以上传自己的图片
                </div>

                <div class="upload-area" onclick="document.getElementById('imageInput').click()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击选择图片或拖拽图片到此处</div>
                    <div class="upload-hint">支持 JPG, PNG, GIF 格式</div>
                </div>

                <input type="file" id="imageInput" accept="image/*" />

                <div class="preview-section" id="previewSection">
                    <div class="image-preview">
                        <img
                            id="previewImage"
                            class="preview-image"
                            alt="预览图片"
                            crossorigin="anonymous"
                        />
                    </div>

                    <div class="loading" id="loading" style="display: none">
                        <div class="spinner"></div>
                        <div>正在使用ColorThief分析图片颜色...</div>
                    </div>

                    <div class="palette-section" id="paletteSection" style="display: none">
                        <h3 class="palette-title">提取的颜色调色板</h3>
                        <div class="color-palette" id="colorPalette"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="toast" id="toast">颜色代码已复制到剪贴板！</div>

        <!-- 引入ColorThief库 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/color-thief/2.3.0/color-thief.umd.js"></script>

        <script>
            class ColorExtractor {
                constructor() {
                    this.colorThief = new ColorThief()
                    this.setupEventListeners()
                }

                setupEventListeners() {
                    const imageInput = document.getElementById('imageInput')
                    const uploadArea = document.querySelector('.upload-area')

                    imageInput.addEventListener('change', e => this.handleImageSelect(e))

                    // 拖拽功能
                    uploadArea.addEventListener('dragover', e => {
                        e.preventDefault()
                        uploadArea.classList.add('dragover')
                    })

                    uploadArea.addEventListener('dragleave', () => {
                        uploadArea.classList.remove('dragover')
                    })

                    uploadArea.addEventListener('drop', e => {
                        e.preventDefault()
                        uploadArea.classList.remove('dragover')
                        const files = e.dataTransfer.files
                        if (files.length > 0) {
                            this.processImage(files[0])
                        }
                    })
                }

                handleImageSelect(event) {
                    const file = event.target.files[0]
                    if (file) {
                        this.processImage(file)
                    }
                }

                processImage(file) {
                    const reader = new FileReader()
                    reader.onload = e => {
                        const img = document.getElementById('previewImage')
                        img.src = e.target.result
                        img.onload = () => {
                            document.getElementById('previewSection').style.display = 'block'
                            this.extractColors(img)
                        }
                    }
                    reader.readAsDataURL(file)
                }

                extractColors(img) {
                    document.getElementById('loading').style.display = 'block'
                    document.getElementById('paletteSection').style.display = 'none'

                    // 等待图片完全加载
                    setTimeout(() => {
                        try {
                            // 使用ColorThief获取调色板
                            const palette = this.colorThief.getPalette(img, 5, 20)

                            // 转换为我们需要的格式
                            const colors = palette.map(rgb => ({
                                r: rgb[0],
                                g: rgb[1],
                                b: rgb[2],
                            }))

                            this.displayColors(colors)

                            document.getElementById('loading').style.display = 'none'
                            document.getElementById('paletteSection').style.display = 'block'
                        } catch (error) {
                            console.error('颜色提取错误:', error)
                            document.getElementById('loading').innerHTML =
                                '<div style="color: #f44336;">颜色提取失败，请尝试其他图片</div>'
                        }
                    }, 500)
                }

                rgbToHex(r, g, b) {
                    return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
                }

                displayColors(colors) {
                    const palette = document.getElementById('colorPalette')
                    palette.innerHTML = ''

                    colors.forEach((color, index) => {
                        const hex = this.rgbToHex(color.r, color.g, color.b)
                        const rgb = `rgb(${color.r}, ${color.g}, ${color.b})`

                        const colorItem = document.createElement('div')
                        colorItem.className = 'color-item'
                        colorItem.innerHTML = `
                        <div class="color-preview" style="background-color: ${hex}"></div>
                        <div class="color-info">
                            <div class="color-hex">${hex.toUpperCase()}</div>
                            <div class="color-rgb">${rgb}</div>
                        </div>
                    `

                        colorItem.addEventListener('click', () =>
                            this.copyToClipboard(hex.toUpperCase()),
                        )
                        palette.appendChild(colorItem)
                    })
                }

                copyToClipboard(text) {
                    navigator.clipboard
                        .writeText(text)
                        .then(() => {
                            this.showToast()
                        })
                        .catch(() => {
                            // 降级方案
                            const textArea = document.createElement('textarea')
                            textArea.value = text
                            document.body.appendChild(textArea)
                            textArea.select()
                            document.execCommand('copy')
                            document.body.removeChild(textArea)
                            this.showToast()
                        })
                }

                showToast() {
                    const toast = document.getElementById('toast')
                    toast.classList.add('show')
                    setTimeout(() => {
                        toast.classList.remove('show')
                    }, 2000)
                }

                /**
                 * 加载默认图片 walller__1.jpg
                 */
                loadDefaultImage() {
                    const defaultImagePath = '/__壁纸测试/walller__1.jpg'
                    const img = document.getElementById('previewImage')

                    img.onload = () => {
                        document.getElementById('previewSection').style.display = 'block'
                        this.extractColors(img)
                    }

                    img.onerror = () => {
                        console.warn('默认图片加载失败，请手动选择图片')
                        // 如果根路径失败，尝试相对路径
                        this.tryAlternativePath()
                    }

                    img.src = defaultImagePath
                }

                /**
                 * 尝试备用图片路径
                 */
                tryAlternativePath() {
                    const alternativePath = '../../../public/__壁纸测试/walller__1.jpg'
                    const img = document.getElementById('previewImage')

                    img.onload = () => {
                        document.getElementById('previewSection').style.display = 'block'
                        this.extractColors(img)
                    }

                    img.onerror = () => {
                        console.error('所有图片路径都加载失败，请手动选择图片')
                    }

                    img.src = alternativePath
                }
            }

            // 等待ColorThief库加载完成后初始化
            window.addEventListener('load', () => {
                const extractor = new ColorExtractor()
                // 默认加载walller__1.jpg图片
                extractor.loadDefaultImage()
            })
        </script>
    </body>
</html>
