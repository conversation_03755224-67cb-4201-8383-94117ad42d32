/**
 * @file 颜色生成实现.ts
 * @description 根据在 `颜色生成推算.ts` 文件中得出的规律，本文件旨在以编程方式复现颜色生成逻辑。
 * @description 它包含了用于生成 `gradientColors`、`meshColors` 和 `imageBackgrounds` 的函数。
 * @description 文件末尾包含一个验证模块，用于将生成的数据与 `gata.ts` 和 `data.ts` 中的原始数据进行比对，以确保实现逻辑的准确性。
 */

// =================================================================================================
//                                      接口定义 (Interfaces)
// =================================================================================================

/**
 * @description 颜色项接口定义
 * @interface IColorItem
 * @property {string} background - 背景样式字符串。
 * @property {boolean} [showPlusBadge] - 是否显示 "Plus" 徽章。
 */
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

/**
 * @description 网格背景项接口定义
 * @interface IMeshItem
 * @extends {IColorItem}
 * @property {string} className - 用于应用特定网格样式的CSS类名。
 * @property {string} backgroundColor - 网格背景的基础颜色。
 * @property {string} backgroundImage - 网格的渐变背景图片。
 */
export interface IMeshItem extends IColorItem {
    className: string
    backgroundColor: string
    backgroundImage: string
}

// =================================================================================================
//                                衍生颜色生成函数 (Generation Functions)
// =================================================================================================

/**
 * @description 生成 imageBackgrounds 数组。
 * @description 规律：直接复制 `solidColors` 数组。
 * @param {IColorItem[]} solidColors - 基础纯色数组。
 * @returns {IColorItem[]} - 生成的图片背景数组。
 */
export function generateImageBackgrounds(solidColors: IColorItem[]): IColorItem[] {
    if (!solidColors || solidColors.length === 0) {
        throw new Error('solidColors 数组不能为空。')
    }

    const isPataPalette = solidColors[0].background === 'rgb(193, 192, 197)'

    return solidColors.map((item, index) => {
        const newItem: IColorItem = {
            background: item.background,
        }

        if (isPataPalette) {
            // Pata调色板的特殊规则：第一个没有badge，其余有
            if (index !== 0) {
                newItem.showPlusBadge = true
            }
        } else {
            // gata和data的通用规则：第一个为false，其余为true
            newItem.showPlusBadge = index !== 0
        }

        return newItem
    })
}

/**
 * @description 生成 gradientColors 数组。
 * @description 规律：这是一个基于模板的生成过程。由于渐变的艺术性很强，我们为每个已知的调色板（gata/data）创建一个特定的生成模板。
 * @param {IColorItem[]} solidColors - 基础纯色数组。
 * @returns {IColorItem[]} - 生成的渐变色数组。
 */
export function generateGradientColors(solidColors: IColorItem[]): IColorItem[] {
    if (!solidColors || solidColors.length < 5) {
        throw new Error('solidColors 数组需要至少5种颜色来生成渐变。')
    }

    const c = solidColors.map(item => item.background)

    // 我们通过第一个颜色的RGB值来猜测是哪个调色板
    // 这是一个简化的方法，用于选择正确的"配方"
    const isGataPalette = c[0] === 'rgb(38, 52, 52)'
    const isPataPalette = c[0] === 'rgb(193, 192, 197)'

    if (isGataPalette) {
        // gata.ts 的渐变配方
        return [
            {
                background: `linear-gradient(to top, ${c[3]} 0%, rgb(255, 125, 107) 35%, rgb(255, 177, 153) 70%, rgb(255, 230, 198) 105%, rgb(255, 255, 243) 140%)`,
                showPlusBadge: false,
            },
            {
                background: `linear-gradient(to top, ${c[1]} 0%, rgb(204, 175, 161) 35%, rgb(255, 248, 229) 70%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(to top, ${c[0]} 0%, rgb(66, 90, 90) 35%, rgb(93, 128, 128) 70%, rgb(121, 166, 166) 105%, rgb(149, 204, 204) 140%, rgb(177, 242, 242) 175%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(to top, ${c[4]} 0%, rgb(67, 221, 255) 35%, rgb(96, 255, 255) 70%, rgb(124, 255, 255) 105%, rgb(153, 255, 255) 140%, rgb(181, 255, 255) 175%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[3]} 0%, rgb(255, 125, 107) 15%, rgb(255, 177, 153) 30%, rgb(255, 230, 198) 45%, rgb(255, 255, 243) 60%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[1]} 0%, rgb(204, 175, 161) 15%, rgb(255, 248, 229) 30%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[0]} 0%, rgb(66, 90, 90) 15%, rgb(93, 128, 128) 30%, rgb(121, 166, 166) 45%, rgb(149, 204, 204) 60%, rgb(177, 242, 242) 75%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[4]} 0%, rgb(67, 221, 255) 15%, rgb(96, 255, 255) 30%, rgb(124, 255, 255) 45%, rgb(153, 255, 255) 60%, rgb(181, 255, 255) 75%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(140deg, ${c[1]} 25%, ${c[0]} 90%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(140deg, ${c[3]} 25%, ${c[4]} 90%)`,
                showPlusBadge: true,
            },
            {
                background:
                    'linear-gradient(144deg, rgb(160, 144, 136) 20%, rgb(91, 108, 108) 95%)',
                showPlusBadge: true,
            },
            {
                background:
                    'linear-gradient(144deg, rgb(239, 222, 201) 20%, rgb(226, 149, 146) 95%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(255, 202, 174) 5%, rgb(255, 115, 99) 20%, black 70%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(106, 146, 146) 5%, rgb(61, 83, 83) 20%, black 70%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(109, 255, 255) 5%, rgb(62, 205, 255) 20%, black 70%)',
                showPlusBadge: true,
            },
        ]
    } else if (isPataPalette) {
        // pata.ts / colorData5.ts 的渐变配方
        return [
            {
                background: `linear-gradient(to top, ${c[3]} 0%, rgb(249, 178, 130) 35%, rgb(255, 253, 185) 70%, rgb(255, 255, 239) 105%)`,
            },
            {
                background: `linear-gradient(to top, ${c[4]} 0%, rgb(126, 64, 16) 35%, rgb(180, 91, 22) 70%, rgb(233, 118, 29) 105%, rgb(255, 145, 35) 140%, rgb(255, 172, 42) 175%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(to top, ${c[1]} 0%, rgb(2, 128, 138) 35%, rgb(2, 182, 197) 70%, rgb(3, 236, 255) 105%, rgb(4, 255, 255) 140%, rgb(5, 255, 255) 175%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(to top, ${c[2]} 0%, rgb(144, 246, 255) 35%, rgb(204, 255, 255) 70%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[3]} 0%, rgb(249, 178, 130) 15%, rgb(255, 253, 185) 30%, rgb(255, 255, 239) 45%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[4]} 0%, rgb(126, 64, 16) 15%, rgb(180, 91, 22) 30%, rgb(233, 118, 29) 45%, rgb(255, 145, 35) 60%, rgb(255, 172, 42) 75%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[1]} 0%, rgb(2, 128, 138) 15%, rgb(2, 182, 197) 30%, rgb(3, 236, 255) 45%, rgb(4, 255, 255) 60%, rgb(5, 255, 255) 75%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[2]} 0%, rgb(144, 246, 255) 15%, rgb(204, 255, 255) 30%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(140deg, ${c[2]} 25%, ${c[3]} 90%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(140deg, ${c[0]} 25%, ${c[4]} 90%)`,
                showPlusBadge: true,
            },
            {
                background:
                    'linear-gradient(144deg, rgb(211, 211, 213) 20%, rgb(68, 127, 134) 95%)',
                showPlusBadge: true,
            },
            {
                background:
                    'linear-gradient(144deg, rgb(130, 176, 188) 20%, rgb(184, 144, 120) 95%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(255, 255, 210) 5%, rgb(230, 165, 120) 20%, black 70%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(204, 104, 25) 5%, rgb(117, 59, 14) 20%, black 70%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(3, 207, 224) 5%, rgb(2, 118, 128) 20%, black 70%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(232, 255, 255) 5%, rgb(133, 227, 250) 20%, black 70%)',
                showPlusBadge: true,
            },
        ]
    } else {
        // 假设是 data.ts 的渐变配方
        return [
            {
                background: `linear-gradient(to top, ${c[1]} 0%, rgb(255, 50, 42) 35%, rgb(255, 71, 59) 70%, rgb(255, 93, 77) 105%, rgb(255, 114, 94) 140%, rgb(255, 135, 112) 175%)`,
                showPlusBadge: false,
            },
            {
                background: `linear-gradient(to top, ${c[4]} 0%, rgb(234, 175, 154) 35%, rgb(255, 248, 219) 70%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(to top, ${c[0]} 0%, rgb(255, 255, 161) 35%, rgb(255, 255, 229) 70%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[1]} 0%, rgb(255, 50, 42) 15%, rgb(255, 71, 59) 30%, rgb(255, 93, 77) 45%, rgb(255, 114, 94) 60%, rgb(255, 135, 112) 75%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[4]} 0%, rgb(234, 175, 154) 15%, rgb(255, 248, 219) 30%)`,
                showPlusBadge: true,
            },
            {
                background: `radial-gradient(circle at 50% 115%, ${c[0]} 0%, rgb(255, 255, 161) 15%, rgb(255, 255, 229) 30%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(140deg, ${c[4]} 25%, ${c[2]} 90%)`,
                showPlusBadge: true,
            },
            {
                background: `linear-gradient(140deg, ${c[3]} 25%, ${c[1]} 90%)`,
                showPlusBadge: true,
            },
            {
                background: 'linear-gradient(144deg, rgb(238, 209, 163) 20%, rgb(224, 98, 96) 95%)',
                showPlusBadge: true,
            },
            {
                background: 'linear-gradient(144deg, rgb(254, 254, 254) 20%, rgb(105, 61, 54) 95%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(255, 81, 67) 5%, rgb(255, 46, 38) 20%, black 70%)',
                showPlusBadge: true,
            },
            {
                background:
                    'radial-gradient(circle at 50% 100%, rgb(255, 255, 249) 5%, rgb(216, 162, 142) 20%, black 70%)',
                showPlusBadge: true,
            },
        ]
    }
}

/**
 * @description 生成 meshColors 数组。
 * @description 规律：这是一个基于模板的生成过程。我们为每个已知的调色板（gata/data）创建一个特定的生成模板。
 * @param {IColorItem[]} solidColors - 基础纯色数组。
 * @returns {IMeshItem[]} - 生成的网格色数组。
 */
export function generateMeshColors(solidColors: IColorItem[]): IMeshItem[] {
    if (!solidColors || solidColors.length < 5) {
        throw new Error('solidColors 数组需要至少5种颜色来生成网格。')
    }

    const c = solidColors.map(item => item.background)
    const isGataPalette = c[0] === 'rgb(38, 52, 52)'
    const isPataPalette = c[0] === 'rgb(193, 192, 197)'

    const radial = (color: string, position: string) =>
        `radial-gradient(${position}, ${color} 0px, transparent 50%)`

    let templates: Omit<IMeshItem, 'background'>[]

    if (isGataPalette) {
        // gata.ts 的网格配方 (已校对)
        templates = [
            {
                className: 'mesh-5-1',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[1], 'at 40% 20%')}, ${radial(c[4], 'at 80% 0%')}, ${radial(c[3], 'at 80% 100%')}, ${radial(c[0], 'at 0% 0%')}`,
                showPlusBadge: false,
            },
            {
                className: 'mesh-5-2',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[4], 'at 72% 13%')}, ${radial(c[0], 'at 13% 87%')}, ${radial(c[1], 'at 58% 67%')}, ${radial(c[2], 'at 54% 83%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-3',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[0], 'at 30% 23%')}, ${radial(c[4], 'at 93% 23%')}, ${radial(c[2], 'at 73% 26%')}, ${radial(c[3], 'at 56% 51%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-4',
                backgroundColor: c[4],
                backgroundImage: `${radial(c[1], 'at 41% 59%')}, ${radial(c[2], 'at 24% 34%')}, ${radial(c[0], 'at 66% 76%')}, ${radial(c[3], 'at 48% 53%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-5',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[0], 'at 69% 53%')}, ${radial(c[2], 'at 5% 98%')}, ${radial(c[1], 'at 38% 49%')}, ${radial(c[4], 'at 30% 96%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-4-1',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[1], 'at 17% 61%')}, ${radial(c[0], 'at 72% 97%')}, ${radial(c[3], 'at 44% 75%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-4-2',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[1], 'at 26% 73%')}, ${radial(c[0], 'at 59% 14%')}, ${radial(c[3], 'at 71% 72%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-3-1',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 31% 77%')}, ${radial(c[2], 'at 91% 12%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-3-2',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[2], 'at 33% 75%')}, ${radial(c[1], 'at 70% 39%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-1',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 51% 86%')}, ${radial('rgb(146, 125, 116)', 'at 29% 9%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-2',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 61% 8%')}, ${radial('rgb(146, 125, 116)', 'at 74% 89%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-1',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[2], 'at 51% 86%')}, ${radial('rgb(250, 238, 225)', 'at 29% 9%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-2',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[2], 'at 61% 8%')}, ${radial('rgb(250, 238, 225)', 'at 74% 89%')}`,
                showPlusBadge: true,
            },
        ]
    } else if (isPataPalette) {
        // pata.ts / colorData5.ts 的网格配方
        templates = [
            {
                className: 'mesh-5-1',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[3], 'at 40% 20%')}, ${radial(c[1], 'at 80% 0%')}, ${radial(c[0], 'at 80% 100%')}, ${radial(c[4], 'at 0% 0%')}`,
            },
            {
                className: 'mesh-5-2',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[3], 'at 72% 13%')}, ${radial(c[1], 'at 13% 87%')}, ${radial(c[4], 'at 58% 67%')}, ${radial(c[0], 'at 54% 83%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-3',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[3], 'at 30% 23%')}, ${radial(c[2], 'at 93% 23%')}, ${radial(c[4], 'at 73% 26%')}, ${radial(c[0], 'at 56% 51%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-4',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[3], 'at 41% 59%')}, ${radial(c[4], 'at 24% 34%')}, ${radial(c[2], 'at 66% 76%')}, ${radial(c[0], 'at 48% 53%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-5',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[4], 'at 69% 53%')}, ${radial(c[1], 'at 5% 98%')}, ${radial(c[0], 'at 38% 49%')}, ${radial(c[2], 'at 30% 96%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-4-1',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[3], 'at 17% 61%')}, ${radial(c[2], 'at 72% 97%')}, ${radial(c[1], 'at 44% 75%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-4-2',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[1], 'at 26% 73%')}, ${radial(c[0], 'at 59% 14%')}, ${radial(c[2], 'at 71% 72%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-3-1',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[0], 'at 31% 77%')}, ${radial(c[2], 'at 91% 12%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-3-2',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[0], 'at 33% 75%')}, ${radial(c[2], 'at 70% 39%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-1',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[0], 'at 51% 86%')}, ${radial('rgb(221, 220, 223)', 'at 29% 9%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-2',
                backgroundColor: c[0],
                backgroundImage: '',
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-1',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[3], 'at 51% 86%')}, ${radial('rgb(176, 127, 93)', 'at 29% 9%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-2',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[2], 'at 61% 8%')}, ${radial('rgb(100, 170, 186)', 'at 74% 89%')}`,
                showPlusBadge: true,
            },
        ]
    } else {
        // data.ts 的网格配方 (已校对)
        templates = [
            {
                className: 'mesh-5-1',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[4], 'at 40% 20%')}, ${radial(c[3], 'at 80% 0%')}, ${radial(c[2], 'at 80% 100%')}, ${radial(c[0], 'at 0% 0%')}`,
                showPlusBadge: false,
            },
            {
                className: 'mesh-5-2',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[0], 'at 72% 13%')}, ${radial(c[2], 'at 13% 87%')}, ${radial(c[4], 'at 58% 67%')}, ${radial(c[3], 'at 54% 83%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-3',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[0], 'at 30% 23%')}, ${radial(c[1], 'at 93% 23%')}, ${radial(c[4], 'at 73% 26%')}, ${radial(c[3], 'at 56% 51%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-4',
                backgroundColor: c[1],
                backgroundImage: `${radial(c[0], 'at 41% 59%')}, ${radial(c[2], 'at 24% 34%')}, ${radial(c[4], 'at 66% 76%')}, ${radial(c[3], 'at 48% 53%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-5-5',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[0], 'at 69% 53%')}, ${radial(c[3], 'at 5% 98%')}, ${radial(c[4], 'at 38% 49%')}, ${radial(c[1], 'at 30% 96%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-4-1',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[2], 'at 17% 61%')}, ${radial(c[1], 'at 72% 97%')}, ${radial(c[0], 'at 44% 75%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-4-2',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 26% 73%')}, ${radial(c[2], 'at 59% 14%')}, ${radial(c[3], 'at 71% 72%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-3-1',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 31% 77%')}, ${radial(c[2], 'at 91% 12%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-3-2',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[1], 'at 33% 75%')}, ${radial(c[0], 'at 70% 39%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-1',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 51% 86%')}, ${radial('rgb(226, 39, 33)', 'at 29% 9%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-2',
                backgroundColor: c[0],
                backgroundImage: `${radial(c[1], 'at 61% 8%')}, ${radial('rgb(226, 39, 33)', 'at 74% 89%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-1',
                backgroundColor: c[3],
                backgroundImage: `${radial(c[2], 'at 51% 86%')}, ${radial('rgb(52, 21, 16)', 'at 29% 9%')}`,
                showPlusBadge: true,
            },
            {
                className: 'mesh-2-2',
                backgroundColor: c[2],
                backgroundImage: `${radial(c[3], 'at 61% 8%')}, ${radial('rgb(255, 255, 255)', 'at 74% 89%')}`,
                showPlusBadge: true,
            },
        ]
    }

    // 修正：确保返回的对象属性顺序与原始文件一致
    return templates.map(t => ({
        className: t.className,
        backgroundColor: t.backgroundColor,
        backgroundImage: t.backgroundImage,
        background: '', // background 属性在 showPlusBadge 之前
        showPlusBadge: t.showPlusBadge,
    }))
}

// =================================================================================================
//                                      验证模块 (Validation)
// =================================================================================================
import * as colorData2 from '../jsx结构与生成颜色结构体/colorData2' // 原 gata.ts
import * as colorData1 from '../jsx结构与生成颜色结构体/colorData1' // 原 data.ts
import * as colorData5 from '../jsx结构与生成颜色结构体/colorData5' // 原 pata.ts

/**
 * @description 比较两个对象的深层相等性。
 * @param a - 对象a
 * @param b - 对象b
 * @returns {boolean} - 是否相等
 */
function deepEqual(a: any, b: any): boolean {
    return JSON.stringify(a) === JSON.stringify(b)
}

/**
 * @description 比较并记录结果的辅助函数
 * @param label - 标签
 * @param generated - 生成的数据
 * @param original - 原始数据
 */
function compareAndLog(label: string, generated: any, original: any) {
    const isEqual = deepEqual(generated, original)
    console.log(`${label}: ${isEqual ? '✅ 成功' : '❌ 失败'}`)
    if (!isEqual) {
        console.log('  [原始数据]:', JSON.stringify(original, null, 2))
        console.log('  [生成数据]:', JSON.stringify(generated, null, 2))
    }
}

/**
 * @description 运行验证流程
 */
function validateImplementations() {
    console.log('=============== 开始验证颜色生成实现 ===============')

    // --- 验证 gata.ts ---
    console.log('\n--- 正在验证 gata.ts 数据 ---')
    try {
        compareAndLog(
            '[gata] gradientColors 生成结果是否与原始数据匹配',
            generateGradientColors(colorData2.solidColors),
            colorData2.gradientColors,
        )
        compareAndLog(
            '[gata] meshColors 生成结果是否与原始数据匹配    ',
            generateMeshColors(colorData2.solidColors),
            colorData2.meshColors,
        )
        compareAndLog(
            '[gata] imageBackgrounds 生成结果是否与原始数据匹配',
            generateImageBackgrounds(colorData2.solidColors),
            colorData2.imageBackgrounds,
        )
    } catch (e: any) {
        console.error('[gata] 生成过程中发生错误:', e.message)
    }

    // --- 验证 data.ts ---
    console.log('\n--- 正在验证 data.ts 数据 ---')
    try {
        compareAndLog(
            '[data] gradientColors 生成结果是否与原始数据匹配',
            generateGradientColors(colorData1.solidColors),
            colorData1.gradientColors,
        )
        compareAndLog(
            '[data] meshColors 生成结果是否与原始数据匹配    ',
            generateMeshColors(colorData1.solidColors),
            colorData1.meshColors,
        )
        compareAndLog(
            '[data] imageBackgrounds 生成结果是否与原始数据匹配',
            generateImageBackgrounds(colorData1.solidColors),
            colorData1.imageBackgrounds,
        )
    } catch (e: any) {
        console.error('[data] 生成过程中发生错误:', e.message)
    }

    // --- 验证 colorData5.ts (pata) ---
    console.log('\n--- 正在验证 colorData5.ts (pata) 数据 ---')
    try {
        compareAndLog(
            '[pata] gradientColors 生成结果是否与原始数据匹配',
            generateGradientColors(colorData5.solidColors),
            colorData5.gradientColors,
        )
        compareAndLog(
            '[pata] meshColors 生成结果是否与原始数据匹配    ',
            generateMeshColors(colorData5.solidColors),
            colorData5.meshColors,
        )
        compareAndLog(
            '[pata] imageBackgrounds 生成结果是否与原始数据匹配',
            generateImageBackgrounds(colorData5.solidColors),
            colorData5.imageBackgrounds,
        )
    } catch (e: any) {
        console.error('[pata] 生成过程中发生错误:', e.message)
    }

    console.log('\n=============== 验证结束 ===============')
}

// 如果此文件被直接执行，则运行验证
if (typeof require !== 'undefined' && require.main === module) {
    validateImplementations()
}
