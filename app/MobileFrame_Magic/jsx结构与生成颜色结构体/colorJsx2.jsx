// ;<div className='panel-control undefined '>
//     <div className='controls'>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(38, 52, 52)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(118, 101, 93)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(242, 206, 156)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(242, 72, 62)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(39, 128, 163)' }} />
//                     </div>
//                 </button>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <button className='background-item solid-item'>
//                             <div className='display false'>
//                                 <div style={{ background: 'rgb(242, 72, 62)' }} />
//                             </div>
//                         </button>
//                         <button className='background-item solid-item'>
//                             <div className='display false'>
//                                 <div style={{ background: 'rgb(39, 128, 163)' }} />
//                             </div>
//                         </button>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <button className='background-item gradient-item'>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(242, 72, 62) 0%, rgb(255, 125, 107) 35%, rgb(255, 177, 153) 70%, rgb(255, 230, 198) 105%, rgb(255, 255, 243) 140%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(118, 101, 93) 0%, rgb(204, 175, 161) 35%, rgb(255, 248, 229) 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(38, 52, 52) 0%, rgb(66, 90, 90) 35%, rgb(93, 128, 128) 70%, rgb(121, 166, 166) 105%, rgb(149, 204, 204) 140%, rgb(177, 242, 242) 175%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(39, 128, 163) 0%, rgb(67, 221, 255) 35%, rgb(96, 255, 255) 70%, rgb(124, 255, 255) 105%, rgb(153, 255, 255) 140%, rgb(181, 255, 255) 175%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(242, 72, 62) 0%, rgb(255, 125, 107) 15%, rgb(255, 177, 153) 30%, rgb(255, 230, 198) 45%, rgb(255, 255, 243) 60%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(118, 101, 93) 0%, rgb(204, 175, 161) 15%, rgb(255, 248, 229) 30%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(38, 52, 52) 0%, rgb(66, 90, 90) 15%, rgb(93, 128, 128) 30%, rgb(121, 166, 166) 45%, rgb(149, 204, 204) 60%, rgb(177, 242, 242) 75%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(39, 128, 163) 0%, rgb(67, 221, 255) 15%, rgb(96, 255, 255) 30%, rgb(124, 255, 255) 45%, rgb(153, 255, 255) 60%, rgb(181, 255, 255) 75%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(140deg, rgb(118, 101, 93) 25%, rgb(38, 52, 52) 90%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(140deg, rgb(242, 72, 62) 25%, rgb(39, 128, 163) 90%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(144deg, rgb(160, 144, 136) 20%, rgb(91, 108, 108) 95%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(144deg, rgb(239, 222, 201) 20%, rgb(226, 149, 146) 95%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(255, 202, 174) 5%, rgb(255, 115, 99) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(106, 146, 146) 5%, rgb(61, 83, 83) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(109, 255, 255) 5%, rgb(62, 205, 255) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <button className='background-item gradient-item'>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     style={{
//                                         background:
//                                             'linear-gradient(to top, rgb(39, 128, 163) 0%, rgb(67, 221, 255) 35%, rgb(96, 255, 255) 70%, rgb(124, 255, 255) 105%, rgb(153, 255, 255) 140%, rgb(181, 255, 255) 175%)',
//                                     }}
//                                 />
//                             </div>
//                         </button>
//                         <button className='background-item gradient-item'>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     style={{
//                                         background:
//                                             'radial-gradient(circle at 50% 115%, rgb(242, 72, 62) 0%, rgb(255, 125, 107) 15%, rgb(255, 177, 153) 30%, rgb(255, 230, 198) 45%, rgb(255, 255, 243) 60%)',
//                                     }}
//                                 />
//                             </div>
//                         </button>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 206, 156)',
//                                     backgroundImage:
//                                         'radial-gradient(at 40% 20%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 80% 0%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 80% 100%, rgb(242, 72, 62) 0px, transparent 50%), radial-gradient(at 0% 0%, rgb(38, 52, 52) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 72, 62)',
//                                     backgroundImage:
//                                         'radial-gradient(at 72% 13%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 13% 87%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 58% 67%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 54% 83%, rgb(242, 206, 156) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-3'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(118, 101, 93)',
//                                     backgroundImage:
//                                         'radial-gradient(at 30% 23%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 93% 23%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 73% 26%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 56% 51%, rgb(242, 72, 62) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-4'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(39, 128, 163)',
//                                     backgroundImage:
//                                         'radial-gradient(at 41% 59%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(242, 72, 62) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-5'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 72, 62)',
//                                     backgroundImage:
//                                         'radial-gradient(at 69% 53%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(39, 128, 163) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-4-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 206, 156)',
//                                     backgroundImage:
//                                         'radial-gradient(at 17% 61%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 72% 97%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 44% 75%, rgb(242, 72, 62) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-4-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 206, 156)',
//                                     backgroundImage:
//                                         'radial-gradient(at 26% 73%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 59% 14%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 71% 72%, rgb(242, 72, 62) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-3-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(38, 52, 52)',
//                                     backgroundImage:
//                                         'radial-gradient(at 31% 77%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 91% 12%, rgb(242, 206, 156) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-3-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(38, 52, 52)',
//                                     backgroundImage:
//                                         'radial-gradient(at 33% 75%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 70% 39%, rgb(118, 101, 93) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(38, 52, 52)',
//                                     backgroundImage:
//                                         'radial-gradient(at 51% 86%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(146, 125, 116) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(38, 52, 52)',
//                                     backgroundImage:
//                                         'radial-gradient(at 61% 8%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(146, 125, 116) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 72, 62)',
//                                     backgroundImage:
//                                         'radial-gradient(at 51% 86%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(250, 238, 225) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(242, 72, 62)',
//                                     backgroundImage:
//                                         'radial-gradient(at 61% 8%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(250, 238, 225) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display '>
//                                 <div className='mesh-display mesh-5-4'>
//                                     <div
//                                         style={{
//                                             backgroundColor: 'rgb(118, 101, 93)',
//                                             backgroundImage:
//                                                 'radial-gradient(at 41% 59%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(242, 72, 62) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(242, 206, 156) 0px, transparent 50%)',
//                                         }}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                         <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display '>
//                                 <div className='mesh-display mesh-5-5'>
//                                     <div
//                                         style={{
//                                             backgroundColor: 'rgb(242, 206, 156)',
//                                             backgroundImage:
//                                                 'radial-gradient(at 69% 53%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(242, 72, 62) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(39, 128, 163) 0px, transparent 50%)',
//                                         }}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(38, 52, 52)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(118, 101, 93)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(242, 206, 156)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(242, 72, 62)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(39, 128, 163)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <button
//                             className='background-item magic-image-item '
//                             style={{ fontSize: 1 }}
//                         >
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     className='magic-image-display style-2'
//                                     style={{ background: 'rgb(242, 72, 62)' }}
//                                 >
//                                     <img
//                                         crossOrigin='anonymous'
//                                         loading='lazy'
//                                         decoding='async'
//                                         alt='magicImage'
//                                         src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                                     />
//                                 </div>
//                             </div>
//                         </button>
//                         <button
//                             className='background-item magic-image-item '
//                             style={{ fontSize: 1 }}
//                         >
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     className='magic-image-display style-2'
//                                     style={{ background: 'rgb(39, 128, 163)' }}
//                                 >
//                                     <img
//                                         crossOrigin='anonymous'
//                                         loading='lazy'
//                                         decoding='async'
//                                         alt='magicImage'
//                                         src='blob:https://shots.so/ce8ca7cb-25d4-470e-9b7d-d8686064c0c8'
//                                     />
//                                 </div>
//                             </div>
//                         </button>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//     </div>
// </div>
