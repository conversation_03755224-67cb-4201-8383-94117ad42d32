import React from 'react'

/**
 * @description 默认壁纸路径
 */
export const DEFAULT_WALLPAPER_PATH = '/__壁纸测试/walller__1.jpg'

/**
 * @description 背景颜色类型的枚举
 */
export enum BackgroundType {
    SOLID = 'solid',
    GRADIENT = 'gradient',
    MESH = 'mesh',
    IMAGE = 'image',
}

/**
 * @description 颜色项接口定义
 */
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

/**
 * @description 网格背景项接口定义
 */
export interface IMeshItem extends IColorItem {
    className: string
    backgroundColor: string
    backgroundImage: string
}

/**
 * @description 纯色背景的数据
 */
export const solidColors: IColorItem[] = [
    { background: 'rgb(235, 189, 93)' },
    { background: 'rgb(181, 29, 24)' },
    { background: 'rgb(13, 3, 2)' },
    { background: 'rgb(254, 254, 254)' },
    { background: 'rgb(135, 101, 89)' },
]

/**
 * @description 渐变色背景的数据 长度：12
 */
export const gradientColors: IColorItem[] = [
    {
        background:
            'linear-gradient(to top, rgb(181, 29, 24) 0%, rgb(255, 50, 42) 35%, rgb(255, 71, 59) 70%, rgb(255, 93, 77) 105%, rgb(255, 114, 94) 140%, rgb(255, 135, 112) 175%)',
        showPlusBadge: false,
    },
    {
        background:
            'linear-gradient(to top, rgb(137, 101, 89) 0%, rgb(234, 175, 154) 35%, rgb(255, 248, 219) 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'linear-gradient(to top, rgb(235, 189, 93) 0%, rgb(255, 255, 161) 35%, rgb(255, 255, 229) 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(181, 29, 24) 0%, rgb(255, 50, 42) 15%, rgb(255, 71, 59) 30%, rgb(255, 93, 77) 45%, rgb(255, 114, 94) 60%, rgb(255, 135, 112) 75%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(137, 101, 89) 0%, rgb(234, 175, 154) 15%, rgb(255, 248, 219) 30%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(235, 189, 93) 0%, rgb(255, 255, 161) 15%, rgb(255, 255, 229) 30%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(140deg, rgb(135, 101, 89) 25%, rgb(13, 3, 2) 90%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(140deg, rgb(254, 254, 254) 25%, rgb(181, 29, 24) 90%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(144deg, rgb(238, 209, 163) 20%, rgb(224, 98, 96) 95%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(144deg, rgb(254, 254, 254) 20%, rgb(105, 61, 54) 95%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(255, 81, 67) 5%, rgb(255, 46, 38) 20%, black 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(255, 255, 249) 5%, rgb(216, 162, 142) 20%, black 70%)',
        showPlusBadge: true,
    },
]

/**
 * @description 网格背景的数据 长度: 13
 */
export const meshColors: IMeshItem[] = [
    {
        className: 'mesh-5-1',
        backgroundColor: 'rgb(181, 29, 24)',
        backgroundImage:
            'radial-gradient(at 40% 20%, rgb(135, 101, 89) 0px, transparent 50%), radial-gradient(at 80% 0%, rgb(254, 254, 254) 0px, transparent 50%), radial-gradient(at 80% 100%, rgb(13, 3, 2) 0px, transparent 50%), radial-gradient(at 0% 0%, rgb(235, 189, 93) 0px, transparent 50%)',
        background: '',
        showPlusBadge: false,
    },
    {
        className: 'mesh-5-2',
        backgroundColor: 'rgb(181, 29, 24)',
        backgroundImage:
            'radial-gradient(at 72% 13%, rgb(235, 189, 93) 0px, transparent 50%), radial-gradient(at 13% 87%, rgb(13, 3, 2) 0px, transparent 50%), radial-gradient(at 58% 67%, rgb(135, 101, 89) 0px, transparent 50%), radial-gradient(at 54% 83%, rgb(254, 254, 254) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-3',
        backgroundColor: 'rgb(13, 3, 2)',
        backgroundImage:
            'radial-gradient(at 30% 23%, rgb(235, 189, 93) 0px, transparent 50%), radial-gradient(at 93% 23%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 73% 26%, rgb(135, 101, 89) 0px, transparent 50%), radial-gradient(at 56% 51%, rgb(254, 254, 254) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-4',
        backgroundColor: 'rgb(181, 29, 24)',
        backgroundImage:
            'radial-gradient(at 41% 59%, rgb(235, 189, 93) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(13, 3, 2) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(135, 101, 89) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(254, 254, 254) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-5',
        backgroundColor: 'rgb(13, 3, 2)',
        backgroundImage:
            'radial-gradient(at 69% 53%, rgb(235, 189, 93) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(254, 254, 254) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(135, 101, 89) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(181, 29, 24) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-4-1',
        backgroundColor: 'rgb(254, 254, 254)',
        backgroundImage:
            'radial-gradient(at 17% 61%, rgb(13, 3, 2) 0px, transparent 50%), radial-gradient(at 72% 97%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 44% 75%, rgb(235, 189, 93) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-4-2',
        backgroundColor: 'rgb(235, 189, 93)',
        backgroundImage:
            'radial-gradient(at 26% 73%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 59% 14%, rgb(13, 3, 2) 0px, transparent 50%), radial-gradient(at 71% 72%, rgb(254, 254, 254) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-3-1',
        backgroundColor: 'rgb(235, 189, 93)',
        backgroundImage:
            'radial-gradient(at 31% 77%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 91% 12%, rgb(13, 3, 2) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-3-2',
        backgroundColor: 'rgb(13, 3, 2)',
        backgroundImage:
            'radial-gradient(at 33% 75%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 70% 39%, rgb(235, 189, 93) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-1',
        backgroundColor: 'rgb(235, 189, 93)',
        backgroundImage:
            'radial-gradient(at 51% 86%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(226, 39, 33) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-2',
        backgroundColor: 'rgb(235, 189, 93)',
        backgroundImage:
            'radial-gradient(at 61% 8%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(226, 39, 33) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-1',
        backgroundColor: 'rgb(254, 254, 254)',
        backgroundImage:
            'radial-gradient(at 51% 86%, rgb(13, 3, 2) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(52, 21, 16) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-2',
        backgroundColor: 'rgb(13, 3, 2)',
        backgroundImage:
            'radial-gradient(at 61% 8%, rgb(254, 254, 254) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(255, 255, 255) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
]

/**
 * @description 图片背景的数据
 */
export const imageBackgrounds: IColorItem[] = [
    { background: 'rgb(235, 189, 93)', showPlusBadge: false },
    { background: 'rgb(181, 29, 24)', showPlusBadge: true },
    { background: 'rgb(13, 3, 2)', showPlusBadge: true },
    { background: 'rgb(254, 254, 254)', showPlusBadge: true },
    { background: 'rgb(135, 101, 89)', showPlusBadge: true },
]
