### 功能说明

这是一个用于自动化从 JSX 文件中提取颜色数据、生成类型化的 TypeScript 数据文件，并更新相关组件导入源的提示语模板。

### 使用方法

当你需要再次执行此操作时，请复制以下模板，修改其中的文件路径，然后发送给我即可。

---

### 提示语模板

我需要你执行以下操作：

1.  **分析源文件**：读取并解析位于 `[这里填写源JSX文件路径]` 的JSX文件。
2.  **参照模板**：使用 `[这里填写模板TS文件路径]` 中的数据结构（如接口、枚举）作为生成代码的模板。
3.  **提取与生成**：从JSX文件中提取颜色、类名等相关数据，并根据模板生成新的TypeScript数据。
4.  **写入目标文件**：将生成的数据写入到 `[这里填写目标TS文件路径]`。
5.  **更新组件**：修改 `[这里填写需要更新的组件路径]`，使其从新的目标TS文件导入数据。

---

### 本次操作示例

作为参考，以下是刚才我们完成的操作所对应的指令：

我需要你执行以下操作：

1.  **分析源文件**：读取并解析位于 `app/MobileFrame_Magic/jsx结构与生成颜色结构体/colorJsx5.jsx` 的JSX文件。
2.  **参照模板**：使用 `app/MobileFrame_Magic/jsx结构与生成颜色结构体/colorData1.ts` 中的数据结构（如接口、枚举）作为生成代码的模板。
3.  **提取与生成**：从JSX文件中提取颜色、类名等相关数据，并根据模板生成新的TypeScript数据。
4.  **写入目标文件**：将生成的数据写入到 `app/MobileFrame_Magic/jsx结构与生成颜色结构体/colorData5.ts`。
5.  **更新组件**：修改 `app/MobileFrame_Magic/MobileFrame_Magic.tsx`，使其从新的 `app/MobileFrame_Magic/jsx结构与生成颜色结构体/colorData5.ts` 导入数据。
