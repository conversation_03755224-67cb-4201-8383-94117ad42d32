/**
 * @file pata.ts
 * @description This file contains the color data for the "Pata" theme.
 * It is automatically generated by parsing '希望获取pata颜色的结构体.jsx'.
 * Please do not edit this file manually, as changes will be overwritten.
 */

/**
 * @description 默认壁纸路径
 */
export const DEFAULT_WALLPAPER_PATH = '/__壁纸测试/walller__5.jpg'

/**
 * @description 背景颜色类型的枚举
 */
export enum BackgroundType {
    SOLID = 'solid',
    GRADIENT = 'gradient',
    MESH = 'mesh',
    IMAGE = 'image',
}

/**
 * @description 颜色项接口定义
 */
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

/**
 * @description 网格背景项接口定义
 */
export interface IMeshItem extends IColorItem {
    className: string
    backgroundColor: string
    backgroundImage: string
}

/**
 * @description Defines the solid color palette for the Pata theme.
 * Each item represents a single, solid color background.
 * @type {IColorItem[]}
 */
export const solidColors: IColorItem[] = [
    { background: 'rgb(193, 192, 197)' },
    { background: 'rgb(1, 74, 80)' },
    { background: 'rgb(83, 142, 156)' },
    { background: 'rgb(144, 103, 75)' },
    { background: 'rgb(73, 37, 9)' },
]

/**
 * @description 渐变色背景的数据 长度：16
 * Each item represents a gradient background.
 * The 'showPlusBadge' property indicates if it's a premium/extra option.
 * @type {IColorItem[]}
 */
export const gradientColors: IColorItem[] = [
    {
        background:
            'linear-gradient(to top, rgb(144, 103, 75) 0%, rgb(249, 178, 130) 35%, rgb(255, 253, 185) 70%, rgb(255, 255, 239) 105%)',
    },
    {
        background:
            'linear-gradient(to top, rgb(72, 37, 9) 0%, rgb(126, 64, 16) 35%, rgb(180, 91, 22) 70%, rgb(233, 118, 29) 105%, rgb(255, 145, 35) 140%, rgb(255, 172, 42) 175%)',
        showPlusBadge: true,
    },
    {
        background:
            'linear-gradient(to top, rgb(1, 74, 80) 0%, rgb(2, 128, 138) 35%, rgb(2, 182, 197) 70%, rgb(3, 236, 255) 105%, rgb(4, 255, 255) 140%, rgb(5, 255, 255) 175%)',
        showPlusBadge: true,
    },
    {
        background:
            'linear-gradient(to top, rgb(83, 142, 156) 0%, rgb(144, 246, 255) 35%, rgb(204, 255, 255) 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(144, 103, 75) 0%, rgb(249, 178, 130) 15%, rgb(255, 253, 185) 30%, rgb(255, 255, 239) 45%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(72, 37, 9) 0%, rgb(126, 64, 16) 15%, rgb(180, 91, 22) 30%, rgb(233, 118, 29) 45%, rgb(255, 145, 35) 60%, rgb(255, 172, 42) 75%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(1, 74, 80) 0%, rgb(2, 128, 138) 15%, rgb(2, 182, 197) 30%, rgb(3, 236, 255) 45%, rgb(4, 255, 255) 60%, rgb(5, 255, 255) 75%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(83, 142, 156) 0%, rgb(144, 246, 255) 15%, rgb(204, 255, 255) 30%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(140deg, rgb(83, 142, 156) 25%, rgb(144, 103, 75) 90%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(140deg, rgb(193, 192, 197) 25%, rgb(73, 37, 9) 90%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(144deg, rgb(211, 211, 213) 20%, rgb(68, 127, 134) 95%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(144deg, rgb(130, 176, 188) 20%, rgb(184, 144, 120) 95%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(255, 255, 210) 5%, rgb(230, 165, 120) 20%, black 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(204, 104, 25) 5%, rgb(117, 59, 14) 20%, black 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(3, 207, 224) 5%, rgb(2, 118, 128) 20%, black 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(232, 255, 255) 5%, rgb(133, 227, 250) 20%, black 70%)',
        showPlusBadge: true,
    },
]

/**
 * @description Defines the mesh gradient palette for the Pata theme.
 * Each item represents a complex mesh gradient background. 长度: 13
 * @type {IMeshItem[]}
 */
export const meshColors: IMeshItem[] = [
    {
        className: 'mesh-5-1',
        backgroundColor: 'rgb(83, 142, 156)',
        backgroundImage:
            'radial-gradient(at 40% 20%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 80% 0%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 80% 100%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 0% 0%, rgb(73, 37, 9) 0px, transparent 50%)',
        background: '',
    },
    {
        className: 'mesh-5-2',
        backgroundColor: 'rgb(83, 142, 156)',
        backgroundImage:
            'radial-gradient(at 72% 13%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 13% 87%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 58% 67%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 54% 83%, rgb(193, 192, 197) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-3',
        backgroundColor: 'rgb(1, 74, 80)',
        backgroundImage:
            'radial-gradient(at 30% 23%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 93% 23%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 73% 26%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 56% 51%, rgb(193, 192, 197) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-4',
        backgroundColor: 'rgb(1, 74, 80)',
        backgroundImage:
            'radial-gradient(at 41% 59%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(193, 192, 197) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-5',
        backgroundColor: 'rgb(144, 103, 75)',
        backgroundImage:
            'radial-gradient(at 69% 53%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(83, 142, 156) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-4-1',
        backgroundColor: 'rgb(193, 192, 197)',
        backgroundImage:
            'radial-gradient(at 17% 61%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 72% 97%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 44% 75%, rgb(1, 74, 80) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-4-2',
        backgroundColor: 'rgb(144, 103, 75)',
        backgroundImage:
            'radial-gradient(at 26% 73%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 59% 14%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 71% 72%, rgb(83, 142, 156) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-3-1',
        backgroundColor: 'rgb(1, 74, 80)',
        backgroundImage:
            'radial-gradient(at 31% 77%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 91% 12%, rgb(83, 142, 156) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-3-2',
        backgroundColor: 'rgb(1, 74, 80)',
        backgroundImage:
            'radial-gradient(at 33% 75%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 70% 39%, rgb(83, 142, 156) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-1',
        backgroundColor: 'rgb(1, 74, 80)',
        backgroundImage:
            'radial-gradient(at 51% 86%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(221, 220, 223) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-2',
        backgroundColor: 'rgb(193, 192, 197)',
        backgroundImage: '',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-1',
        backgroundColor: 'rgb(83, 142, 156)',
        backgroundImage:
            'radial-gradient(at 51% 86%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(176, 127, 93) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-2',
        backgroundColor: 'rgb(144, 103, 75)',
        backgroundImage:
            'radial-gradient(at 61% 8%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(100, 170, 186) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
]

/**
 * @description Defines the image-based backgrounds for the Pata theme.
 * These items use a solid color as a fallback or tint.
 * @type {IColorItem[]}
 */
export const imageBackgrounds: IColorItem[] = [
    { background: 'rgb(193, 192, 197)' },
    { background: 'rgb(1, 74, 80)', showPlusBadge: true },
    { background: 'rgb(83, 142, 156)', showPlusBadge: true },
    { background: 'rgb(144, 103, 75)', showPlusBadge: true },
    { background: 'rgb(73, 37, 9)', showPlusBadge: true },
]
