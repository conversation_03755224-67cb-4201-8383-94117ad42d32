export const Public_MockupSmallLayout = () => {
    return (
        <div className='panel-control undefined Public_MockupSmallLayout'>
            <span className='label gray-text'>Layout</span>
            <div className='controls'>
                <div className='layout-item' style={{ width: 208, height: 156 }}>
                    <div className='frame layout-frame' style={{ width: 208, height: 156 }}>
                        <div className='frame-content'>
                            <div
                                style={{
                                    position: 'relative',
                                    overflow: 'hidden',
                                    width: 208,
                                    height: 156,
                                    opacity: 1,
                                }}
                            >
                                <div
                                    style={{
                                        width: 208,
                                        height: 156,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        overflow: 'hidden',
                                    }}
                                >
                                    <div
                                        className='__remotion-player'
                                        style={{
                                            position: 'absolute',
                                            width: 208,
                                            height: 156,
                                            display: 'flex',
                                            transform: 'scale(1)',
                                            marginLeft: 0,
                                            marginTop: 0,
                                            overflow: 'hidden',
                                        }}
                                    >
                                        <div className='frame-background-display'>
                                            <div className='frame-noise' style={{ opacity: 0 }} />
                                            <div
                                                className='frame-background'
                                                style={{
                                                    background: 'rgb(143, 74, 74)',
                                                    opacity: 1,
                                                }}
                                            />
                                        </div>
                                        <div className='display-container-single'>
                                            <div
                                                className='component'
                                                style={{
                                                    willChange: 'transform',
                                                    position: 'absolute',
                                                    inset: 0,
                                                    transform:
                                                        'translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
                                                    transformOrigin: 'center center',
                                                    transition: 'transform 0.125s linear',
                                                }}
                                            >
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        position: 'relative',
                                                        width: '100%',
                                                        height: '100%',
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                            position: 'relative',
                                                            width: 'auto',
                                                            height: '75%',
                                                            aspectRatio: '0.461373 / 1',
                                                        }}
                                                    >
                                                        <div
                                                            className='devices iphone-15-plus black phone-shadow-config'
                                                            style={{
                                                                position: 'absolute',
                                                                willChange: 'transform',
                                                                zIndex: 1,
                                                                width: '100%',
                                                                height: 'auto',
                                                                aspectRatio: '430 / 932',
                                                                transform:
                                                                    'perspective(200em) translate(0%, 0%) scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skewX(0deg) skewY(0deg)',
                                                                transition:
                                                                    'transform 0.125s linear',
                                                                transformOrigin: 'center center',
                                                            }}
                                                        >
                                                            <div className='item'>
                                                                <div className='item-container'>
                                                                    <div className='device-asset is-portrait'>
                                                                        <img
                                                                            crossOrigin='anonymous'
                                                                            loading='lazy'
                                                                            decoding='async'
                                                                            className='device-asset-image'
                                                                            alt='device'
                                                                            src='/mockups/iPhone 15 Plus/portrait/black.png'
                                                                        />
                                                                    </div>
                                                                    <div
                                                                        className='drop-wrapper'
                                                                        style={{
                                                                            aspectRatio:
                                                                                '430 / 932',
                                                                            maskImage:
                                                                                'url("/mockups/iPhone 15 Plus/portrait/display.svg")',
                                                                            maskSize: '100% 100%',
                                                                            width: '100%',
                                                                            height: '100%',
                                                                        }}
                                                                    >
                                                                        <div
                                                                            className='dropzone  bg-panel-dim'
                                                                            style={{
                                                                                overflow: 'hidden',
                                                                            }}
                                                                        >
                                                                            <div
                                                                                className='file-drop'
                                                                                style={{
                                                                                    cursor: 'pointer',
                                                                                }}
                                                                            />
                                                                            <div
                                                                                className='empty-state start-state'
                                                                                style={{
                                                                                    opacity: 1,
                                                                                }}
                                                                            >
                                                                                <div className='icons'>
                                                                                    <div className='icon'>
                                                                                        <svg
                                                                                            xmlns='http://www.w3.org/2000/svg'
                                                                                            viewBox='0 0 24 24'
                                                                                        >
                                                                                            <path
                                                                                                fill='currentColor'
                                                                                                fillRule='evenodd'
                                                                                                d='M12.97 11.16h3.88c.46 0 .83.37.83.83s-.38.83-.84.83h-3.89v3.88c0 .46-.38.83-.84.83-.47 0-.84-.38-.84-.84V12.8H7.38c-.47 0-.84-.38-.84-.84 0-.47.37-.84.83-.84h3.88V7.23a.83.83 0 1 1 1.66-.01v3.88ZM5.06 4.92c-3.91 3.9-3.91 10.23 0 14.14 3.9 3.9 10.23 3.9 14.14 0 3.9-3.91 3.9-10.24 0-14.15a10 10 0 0 0-14.15 0Z'
                                                                                            />
                                                                                        </svg>
                                                                                    </div>
                                                                                    <div className='image-icon'>
                                                                                        <svg
                                                                                            xmlns='http://www.w3.org/2000/svg'
                                                                                            viewBox='0 0 24 24'
                                                                                        >
                                                                                            <path
                                                                                                fill='currentColor'
                                                                                                d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                                                            />
                                                                                        </svg>
                                                                                    </div>
                                                                                    <div className='video-icon'>
                                                                                        <svg
                                                                                            xmlns='http://www.w3.org/2000/svg'
                                                                                            viewBox='0 0 24 24'
                                                                                        >
                                                                                            <path
                                                                                                fill='currentColor'
                                                                                                d='M4.599 18.5h8.628c1.616 0 2.6-.928 2.6-2.517V8.016c0-1.589-.891-2.516-2.497-2.516H4.599C3.077 5.5 2 6.427 2 8.016v7.967c0 1.589.983 2.517 2.599 2.517m12.429-4.137 2.962 2.557c.28.245.622.398.912.398.663 0 1.098-.479 1.098-1.152V7.833c0-.673-.435-1.152-1.098-1.152-.29 0-.632.153-.912.398l-2.962 2.557z'
                                                                                            />
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                                <span className='title'>
                                                                                    Drop or Paste
                                                                                </span>
                                                                                <span className='subtitle'>
                                                                                    Images &amp;
                                                                                    Videos
                                                                                </span>
                                                                            </div>
                                                                            <input
                                                                                className='d-none'
                                                                                accept='i,m,a,g,e,/,p,n,g,,,i,m,a,g,e,/,j,p,e,g,,,i,m,a,g,e,/,j,p,g,,,i,m,a,g,e,/,w,e,b,p,v,i,d,e,o,/,m,o,v,,,v,i,d,e,o,/,q,u,i,c,k,t,i,m,e,,,v,i,d,e,o,/,m,p,4,,,v,i,d,e,o,/,w,e,b,m'
                                                                                type='file'
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                    <div className='shadow'>
                                                                        <div
                                                                            className='shadow-layer'
                                                                            style={{
                                                                                boxShadow:
                                                                                    'rgba(0, 0, 0, 0.35) 0.515625em 2.0625em 4.95em -2.5em, rgba(0, 0, 0, 0.35) 1.71875em 6.875em 16.5em -2.5em',
                                                                                willChange: '',
                                                                            }}
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <span className='footnote gray-text2' style={{ margin: 'auto' }}>
                    Customize
                </span>
            </div>
        </div>
    )
}
