/**
 * @fileoverview 媒体选择器弹窗组件
 * @description 统一的图片选择和管理界面，支持图片上传、删除、选择绑定等功能
 *
 * 主要功能：
 * - 📁 图片文件上传和预览
 * - 🗑️ 图片删除管理
 * - 🔗 图片与设备的绑定操作
 * - 📱 响应式设计：支持移动端和PC端
 * - 🎯 设备上下文感知：基于当前选中设备进行操作
 *
 * 交互设计：
 * - 点击图片：直接绑定到当前选中设备
 * - 高亮显示：当前设备绑定的图片会有 is-active 样式
 * - 删除操作：支持安全的图片删除和内存清理
 *
 * 组件特点：
 * - 零UI污染：严格遵循用户要求，不添加额外的UI元素
 * - 智能绑定：利用底层的智能绑定算法处理设备唯一性
 * - 响应式布局：自动适配移动端和PC端的显示需求
 *
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025.01
 */

import { useRef } from 'react'
import { useIsMobile } from './hooks/useAppState'
import {
    useImageStore,
    ImageItem,
    ImageStatus,
    getCurrentSelectedDevice,
} from './ImageMange/imageMangeIndex'
import { HTML_ACCEPT_ATTRIBUTES } from './ImageMange/imageConfig'

/**
 * @interface Public_MediaPickerModalProps
 * @description 媒体选择器弹窗组件的属性定义
 */
interface Public_MediaPickerModalProps {
    /** 弹窗的显示状态 - 控制组件的显示/隐藏 */
    isActiveMediaPickerModal: boolean

    /** 设置弹窗状态的回调函数 - 用于外部控制弹窗的开关 */
    setIsActiveMediaPickerModal: (isActive: boolean) => void
}

/**
 * @component Public_MediaPickerModal
 * @description 统一的媒体选择器弹窗组件
 *
 * 组件架构：
 * 1. 状态管理：通过 useImageStore 获取图片数据和操作方法
 * 2. 设备上下文：基于 currentSelectedDevice 进行操作
 * 3. 响应式渲染：根据设备类型渲染不同的UI布局
 * 4. 智能交互：点击即绑定，删除即清理
 *
 * 核心功能流程：
 * 用户选择图片 → 直接绑定到当前设备 → 自动替换原有绑定 → UI实时更新
 *
 * @param {Public_MediaPickerModalProps} props 组件属性
 * @returns {JSX.Element} 渲染的媒体选择器组件
 */
export const Public_MediaPickerModal = ({
    isActiveMediaPickerModal,
    setIsActiveMediaPickerModal,
}: Public_MediaPickerModalProps) => {
    // ==================== 引用和状态管理 ====================

    /** 文件输入控件的引用 - 用于程序化触发文件选择对话框 */
    const fileInputRef = useRef<HTMLInputElement>(null)

    const isMobile = useIsMobile()

    // 从状态管理中获取图片数据和操作方法
    const {
        images, // 所有图片数据
        addImages, // 添加图片方法
        removeImage, // 删除图片方法
        currentSelectedDevice, // 当前选中的设备索引
        selectImageForCurrentDevice, // 将图片绑定到当前设备的方法
        removeImageFromDevice, // 从设备解绑图片的方法（预留，当前未使用）
        isImageBindToDevice, // 检查图片是否绑定到设备的方法
    } = useImageStore()

    // ==================== 事件处理函数 ====================

    /**
     * @function handleMediaPickerModal
     * @description 处理弹窗状态切换
     *
     * 设计说明：
     * - 当前实现为空函数，保留扩展接口
     * - 可用于添加弹窗切换时的额外逻辑
     * - 例如：数据保存、状态重置、动画控制等
     */
    const handleMediaPickerModal = () => {
        // 预留扩展功能
        // 例如：弹窗关闭时的清理工作、状态重置等
    }

    /**
     * @function handleFileSelect
     * @description 处理用户通过文件选择器上传的文件
     *
     * 处理流程：
     * 1. 验证文件的有效性（非空检查）
     * 2. 调用 addImages 方法处理文件（包括格式验证、尺寸获取等）
     * 3. 重置文件输入控件，允许重复选择相同文件
     *
     * 智能绑定：
     * - addImages 方法内部会自动检查当前选中设备
     * - 如果有选中设备，会自动绑定第一张上传的图片
     * - 无需在此处手动处理绑定逻辑
     *
     * @param {React.ChangeEvent<HTMLInputElement>} event 文件选择事件对象
     */
    const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files

        // 验证文件有效性
        if (files && files.length > 0) {
            // 异步处理文件上传
            await addImages(Array.from(files))

            // 重要：重置文件输入，确保可以重复选择相同文件
            // 这对用户体验很重要，特别是在开发和测试阶段
            event.target.value = ''
        }
    }

    /**
     * @function handleAddMediaClick
     * @description 处理添加媒体按钮的点击事件
     *
     * 实现原理：
     * - 程序化触发隐藏的文件输入控件
     * - 通过 ref 直接调用 input.click() 方法
     * - 实现自定义按钮样式的文件选择功能
     *
     * 用户体验：
     * - 用户看到的是自定义样式的按钮
     * - 点击后弹出标准的文件选择对话框
     * - 保持操作系统原生的文件选择体验
     */
    const handleAddMediaClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click()
        }
    }

    /**
     * @function handleRemoveImage
     * @description 处理图片删除操作
     *
     * 删除流程：
     * 1. 调用 removeImage 方法从状态管理中删除图片
     * 2. 自动清理图片的预览URL，防止内存泄漏
     * 3. 自动解除图片与所有设备的绑定关系
     * 4. 更新UI显示，移除图片卡片
     *
     * 安全性：
     * - 操作是原子性的，确保数据一致性
     * - 内存自动清理，防止内存泄漏
     * - 绑定关系自动清理，避免孤立引用
     *
     * @param {string} imageId 要删除的图片ID
     */
    const handleRemoveImage = (imageId: string) => {
        // 调用状态管理的删除方法，会自动处理所有清理工作
        removeImage(imageId)
        console.log(`🗑️ 图片删除操作: ${imageId}`)
    }

    /**
     * @function handleImageSelect
     * @description 处理图片选择操作 - 核心交互逻辑
     *
     * 业务逻辑：
     * 1. 验证当前是否有选中的设备
     * 2. 如果有设备，直接将图片绑定到该设备
     * 3. 利用智能绑定算法自动处理设备唯一性约束
     *
     * 智能绑定效果：
     * - 图片会立即绑定到当前设备
     * - 该设备原有的图片绑定会被自动替换
     * - 其他设备的绑定关系保持不变
     * - UI会实时更新高亮状态
     *
     * 错误处理：
     * - 无选中设备时给出明确的警告信息
     * - 不会执行任何绑定操作，保持系统状态稳定
     *
     * @param {string} imageId 要选择的图片ID
     */
    const handleImageSelect = (imageId: string) => {
        // 前置条件检查：必须有选中的设备才能进行绑定
        if (currentSelectedDevice === null) {
            console.warn('⚠️ 没有选中的设备，无法绑定图片')
            return
        }

        // 执行智能绑定操作
        // selectImageForCurrentDevice 会调用底层的 setImageDevice 方法
        // 自动处理设备唯一性约束和绑定冲突
        selectImageForCurrentDevice(imageId)
        console.log(`🎯 图片选择操作: ${imageId} → 设备 ${currentSelectedDevice}`)
    }

    // ==================== 渲染函数 ====================

    /**
     * @function renderMediaItem
     * @description 渲染单个媒体项组件
     *
     * 组件结构：
     * - 外层容器：处理点击事件和样式状态
     * - 图片显示区：展示图片预览和图标
     * - 删除按钮：独立的删除操作，阻止事件冒泡
     *
     * 样式逻辑：
     * - is-active: 当图片绑定到当前设备时高亮显示
     * - is-landscape/is-portrait: 根据图片宽高比应用不同样式
     *
     * 交互设计：
     * - 点击图片区域：绑定图片到当前设备
     * - 点击删除按钮：删除图片（阻止事件冒泡）
     * - 鼠标样式：pointer表示可点击
     *
     * @param {ImageItem} image 要渲染的图片项数据
     * @returns {JSX.Element} 渲染的媒体项JSX元素
     */
    const renderMediaItem = (image: ImageItem) => {
        // 计算图片方向类型，用于样式应用
        const isLandscape = image.aspectRatio > 1

        // 判断图片是否与当前选中的设备绑定（用于高亮显示）
        // 这是实现 is-active 样式的核心逻辑
        const isActiveDevice =
            currentSelectedDevice !== null && isImageBindToDevice(image.id, currentSelectedDevice)

        return (
            <div
                key={image.id}
                className={`media-item ${isActiveDevice ? 'is-active' : ''} ${isLandscape ? 'is-landscape' : 'is-portrait'}`}
                onClick={e => {
                    // 阻止事件冒泡，避免触发父级的点击事件
                    e.stopPropagation()
                    handleImageSelect(image.id)
                }}
                style={{ cursor: 'pointer' }}
            >
                {/* 图片显示区域 */}
                <div className='media-safearea'>
                    <div
                        className='media-display'
                        style={{ aspectRatio: image.aspectRatio.toString() }}
                    >
                        {/* 图片预览 */}
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src={image.preview}
                            alt='上传的图片'
                        />

                        {/* 图片类型图标 */}
                        <div className='media-icon'>
                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                <path
                                    fill='currentColor'
                                    d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                />
                            </svg>
                        </div>
                    </div>
                </div>

                {/* 删除按钮 */}
                <button
                    type='button'
                    className='button icon-button tiny-button secondary-button undefined-blur true-round undefined-active remove-button'
                    style={{ flexDirection: 'row' }}
                    onClick={e => {
                        // 关键：阻止事件冒泡，确保删除操作不会触发图片选择
                        e.stopPropagation()
                        handleRemoveImage(image.id)
                    }}
                >
                    {/* 删除图标 */}
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M9.298 19.148c.313 0 .509-.195.502-.477l-.291-9.827c-.007-.29-.215-.471-.501-.471-.302 0-.51.189-.502.478l.29 9.823c.008.29.208.474.502.474m2.687 0c.309 0 .517-.192.517-.477v-9.82c0-.286-.208-.478-.517-.478-.306 0-.518.192-.518.478v9.82c0 .285.212.477.518.477m2.691.007c.289 0 .497-.187.505-.477l.282-9.824c.008-.289-.196-.477-.502-.477-.29 0-.497.188-.505.47l-.282 9.827c-.008.282.192.481.502.481M8.078 6.023h1.264V4.105c0-.562.381-.912.972-.912h3.33c.586 0 .967.35.967.912v1.918h1.273V4.025c0-1.256-.82-2.025-2.168-2.025h-3.482c-1.337 0-2.156.769-2.156 2.025zm-4.182.632h16.178a.594.594 0 0 0 .592-.601c0-.337-.271-.6-.592-.6H3.896a.61.61 0 0 0-.597.6c0 .334.283.601.597.601M7.875 22h8.227c1.225 0 2.08-.817 2.142-2.043l.649-13.461H17.61l-.63 13.331c-.027.562-.44.971-1.001.971H7.975c-.541 0-.963-.417-.989-.971L6.329 6.5H5.076l.661 13.465C5.799 21.191 6.634 22 7.875 22'
                        />
                    </svg>
                </button>
            </div>
        )
    }

    /**
     * @function publicRender
     * @description 渲染通用的弹窗内容结构
     *
     * 组件结构：
     * 1. 标题区域：显示"Media Picker"标题
     * 2. 媒体列表：动态渲染所有已上传的图片
     * 3. 添加按钮：支持新增图片的操作入口
     * 4. 隐藏输入：实际的文件选择控件
     *
     * 布局特点：
     * - 响应式网格布局
     * - 支持动态数量的图片项
     * - 添加按钮始终显示在列表末尾
     *
     * @returns {JSX.Element} 通用内容的JSX结构
     */
    const publicRender = () => {
        return (
            <div className='content' onClick={handleMediaPickerModal}>
                {/* 弹窗标题 */}
                <span className='h5'>Media Picker</span>

                {/* 媒体列表区域 */}
                <div className='media-list'>
                    {/* 动态渲染已上传的图片列表 */}
                    {images.map(renderMediaItem)}

                    {/* 添加媒体按钮 - 始终显示在列表末尾 */}
                    <div
                        className='media-item new-asset'
                        onClick={e => {
                            // 阻止事件冒泡，避免触发父级点击事件
                            e.stopPropagation()
                            handleAddMediaClick()
                        }}
                    >
                        {/* 添加图标 */}
                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                            <path
                                fill='currentColor'
                                d='M4 11.664a.96.96 0 0 0 .949.949h5.765v5.765c0 .504.434.95.95.95.515 0 .937-.446.937-.95v-5.765h5.777a.96.96 0 0 0 .938-.949.96.96 0 0 0-.938-.95h-5.777V4.949c0-.504-.422-.949-.937-.949a.97.97 0 0 0-.95.949v5.765H4.949a.96.96 0 0 0-.949.95'
                            />
                        </svg>
                        <span className='caption2'>Add media</span>

                        {/* 隐藏的文件输入控件 - 实际的文件选择功能 */}
                        <input
                            ref={fileInputRef}
                            className='d-none'
                            accept={HTML_ACCEPT_ATTRIBUTES.STANDARD_IMAGES}
                            type='file'
                            multiple
                            onChange={handleFileSelect}
                        />
                    </div>
                </div>

                {isMobile && (
                    <>
                        {/* 按钮区域 - 预留扩展功能 */}
                        <div className='buttons' onClick={() => setIsActiveMediaPickerModal(false)}>
                            <button
                                className='button default-button huge-button primary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: '140px',
                                }}
                                type='button'
                            >
                                <span>Done</span>
                            </button>
                        </div>
                    </>
                )}
            </div>
        )
    }

    /**
     * @function mobileInViewRender
     * @description 移动端特定的弹窗渲染
     *
     * 移动端特点：
     * - 固定在屏幕底部显示
     * - 较小的宽度（160px）适应移动设备
     * - 无模糊效果，保持清晰显示
     *
     * 样式特征：
     * - bottom: 0 固定在底部
     * - 自动高度适应内容
     * - 去除变换效果，提升性能
     *
     * @returns {JSX.Element} 移动端布局的JSX结构
     */
    const mobileInViewRender = () => {
        return (
            <div
                className='popover dropzone-edit-popover'
                style={{
                    top: 'unset',
                    right: 'unset',
                    bottom: 0, // 固定在底部
                    left: 'unset',
                    width: 160, // 移动端适配宽度
                    height: 'auto',
                    margin: 0,
                    filter: 'blur(0px)', // 无模糊效果
                    opacity: 1,
                    transform: 'none', // 无变换效果
                }}
            >
                {publicRender()}
            </div>
        )
    }

    /**
     * @function pcInViewRender
     * @description PC端特定的弹窗渲染
     *
     * PC端特点：
     * - 固定在屏幕顶部显示
     * - 适中的宽度（160px）适应桌面环境
     * - 完整的视觉效果支持
     *
     * 样式特征：
     * - top: 0 固定在顶部
     * - 支持完整的CSS效果
     * - 更好的视觉层次感
     *
     * @returns {JSX.Element} PC端布局的JSX结构
     */
    const pcInViewRender = () => {
        return (
            <div
                className='popover dropzone-edit-popover'
                style={{
                    top: 0, // 固定在顶部
                    right: 'unset',
                    bottom: 'unset',
                    left: 'unset',
                    width: 160, // PC端适配宽度
                    height: 'auto',
                    margin: 0,
                    filter: 'blur(0px)', // 清晰显示
                    opacity: 1,
                    transform: 'none', // 无变换
                }}
            >
                {publicRender()}
            </div>
        )
    }

    // ==================== 主渲染逻辑 ====================

    // 根据设备类型渲染不同的布局
    // 使用 useIsMobile() hook 进行设备检测
    if (useIsMobile()) {
        return mobileInViewRender()
    }
    return pcInViewRender()
}
