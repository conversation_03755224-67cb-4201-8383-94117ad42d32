{"typescript.tsdk": "node_modules/typescript/lib", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "editor.formatOnSave": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.requireConfig": true, "prettier.useEditorConfig": false, "typescript.preferences.includePackageJsonAutoImports": "off", "typescript.updateImportsOnFileMove.enabled": "never", "typescript.suggest.autoImports": false}