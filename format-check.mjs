/**
 * 格式化检查脚本
 * 用于验证项目中的ESLint和Prettier配置是否正确
 */

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {boolean} - 文件是否存在
 */
function checkFileExists(filePath) {
    try {
        return fs.existsSync(filePath)
    } catch {
        // 忽略错误并返回false
        return false
    }
}

/**
 * 运行命令并返回结果
 * @param {string} command - 要运行的命令
 * @returns {string} - 命令输出
 */
function runCommand(command) {
    try {
        return execSync(command, { encoding: 'utf8' })
    } catch (error) {
        return `错误: ${error.message}`
    }
}

/**
 * 读取JSON文件并移除注释
 * @param {string} filePath - 文件路径
 * @returns {object} - 解析后的JSON对象
 */
function readJsonWithComments(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8')
        // 移除单行注释
        const noComments = content.replace(/\/\/.*$/gm, '')
        return JSON.parse(noComments)
    } catch (error) {
        console.log(`❌ 无法解析文件 ${filePath}: ${error.message}`)
        return {}
    }
}

// 检查VSCode设置
console.log('🔍 检查VSCode设置...')
const vscodeSettingsPath = path.join(__dirname, '.vscode', 'settings.json')
if (checkFileExists(vscodeSettingsPath)) {
    console.log('✅ .vscode/settings.json 文件存在')
    
    // 检查设置内容
    const settings = readJsonWithComments(vscodeSettingsPath)
    
    if (settings.editor && settings.editor.formatOnSave === true) {
        console.log('✅ editor.formatOnSave 已启用')
    } else {
        console.log('❌ editor.formatOnSave 未启用')
    }
    
    if (settings.editor && 
        settings.editor.codeActionsOnSave && 
        settings.editor.codeActionsOnSave['source.fixAll.eslint'] === true) {
        console.log('✅ ESLint自动修复已启用')
    } else {
        console.log('❌ ESLint自动修复未启用')
    }
} else {
    console.log('❌ .vscode/settings.json 文件不存在')
}

// 检查ESLint配置
console.log('\n🔍 检查ESLint配置...')
const eslintConfigPath = path.join(__dirname, 'eslint.config.mjs')
if (checkFileExists(eslintConfigPath)) {
    console.log('✅ eslint.config.mjs 文件存在')
} else {
    console.log('❌ eslint.config.mjs 文件不存在')
}

// 检查Prettier配置
console.log('\n🔍 检查Prettier配置...')
const prettierConfigPath = path.join(__dirname, '.prettierrc')
if (checkFileExists(prettierConfigPath)) {
    console.log('✅ .prettierrc 文件存在')
} else {
    console.log('❌ .prettierrc 文件不存在')
}

// 检查依赖项
console.log('\n🔍 检查依赖项...')
try {
    const packageJsonContent = fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8')
    const packageJson = JSON.parse(packageJsonContent)
    const devDeps = packageJson.devDependencies || {}

    const requiredDeps = [
        'eslint',
        'prettier',
        'eslint-config-prettier',
    ]

    requiredDeps.forEach(dep => {
        if (devDeps[dep]) {
            console.log(`✅ ${dep} 已安装 (${devDeps[dep]})`)
        } else {
            console.log(`❌ ${dep} 未安装`)
        }
    })
} catch (error) {
    console.log(`❌ 无法读取 package.json: ${error.message}`)
}

// 测试运行ESLint和Prettier
console.log('\n🔍 测试运行ESLint和Prettier...')
console.log('ESLint检查结果:')
console.log(runCommand('npx eslint --version'))

console.log('Prettier检查结果:')
console.log(runCommand('npx prettier --version'))

console.log('\n📋 建议:')
console.log('1. 确保VSCode已安装ESLint和Prettier扩展')
console.log('2. 重启VSCode以应用新的设置')
console.log('3. 尝试手动运行格式化命令: pnpm run format')
console.log('4. 如果仍然不工作，尝试手动修复ESLint问题: pnpm run lint:fix') 