# ESLint忽略文件配置
# 以下文件和目录将不会被ESLint检查

# 构建输出目录 - 这些是自动生成的文件
.next           # Next.js构建输出
out             # Next.js静态导出
build           # 通用构建目录
dist            # 通用分发目录

# 依赖目录 - 第三方代码不需要检查
node_modules    # npm/yarn/pnpm安装的依赖

# 缓存目录 - 临时文件不需要检查
.cache          # 通用缓存目录
.cursor         # Cursor编辑器缓存

# 各种配置文件 - 这些通常有自己的格式规则
next.config.ts  # Next.js配置
next-env.d.ts   # Next.js环境类型声明
*.config.js     # JavaScript配置文件
*.config.mjs    # ESM JavaScript配置文件
postcss.config.mjs # PostCSS配置

# 其他文件 - 杂项不需要检查的文件
*.log           # 日志文件
.DS_Store       # macOS文件系统元数据 