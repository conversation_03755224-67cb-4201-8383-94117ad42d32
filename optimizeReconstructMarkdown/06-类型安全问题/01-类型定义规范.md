# 01-类型定义规范

## 🎯 问题描述

项目中的 TypeScript 类型定义不够规范和完整，存在类型定义分散、可选属性过多、类型安全性不足等问题，影响代码的健壮性和开发体验。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 类型定义分散
**问题**：类型定义散布在多个文件中，缺乏统一管理
**具体表现**：
- 相同的类型在多个文件中重复定义
- 类型定义与使用位置距离较远
- 缺乏全局类型定义文件
- 类型导入路径复杂

**问题示例**：
```typescript
// 在多个文件中重复定义相似类型
// app/ImageMange/imageMangeIndex.tsx
interface ImageItem {
    id: string
    file: File
    preview: string
    status: ImageStatus
}

// app/components/DisplayContainer/DisplayConfig.ts
interface DeviceImage {
    id: string
    url: string
    file?: File
}
```

### 1.2 可选属性过多
**问题**：接口定义中过多的可选属性，增加运行时错误风险
**具体表现**：
- 关键属性被定义为可选
- 缺少必要的类型约束
- 运行时类型检查不足
- 类型推断不准确

### 1.3 any 类型使用过多
**问题**：部分地方使用 any 类型，降低类型安全性
**具体表现**：
- 事件处理函数参数使用 any
- 第三方库集成时使用 any
- 复杂对象类型简化为 any
- 动态属性访问使用 any

### 1.4 缺少严格的类型约束
**问题**：类型定义不够严格，缺少必要的约束
**具体表现**：
- 字符串类型应该使用字面量类型
- 数值类型缺少范围约束
- 对象类型缺少索引签名
- 函数类型定义不完整

### 1.5 泛型使用不当
**问题**：泛型类型使用不当或缺失
**具体表现**：
- 应该使用泛型的地方使用具体类型
- 泛型约束不够严格
- 泛型默认值设置不合理
- 复杂泛型类型难以理解

## 🎯 解决方案

### 方案 1：建立统一的类型定义体系

#### 1.1 创建全局类型定义
```typescript
// app/shared/types/global.ts
/**
 * 全局基础类型定义
 */

// 基础 ID 类型
export type ID = string
export type NumericID = number

// 时间戳类型
export type Timestamp = number
export type ISODateString = string

// 文件相关类型
export interface FileInfo {
    readonly id: ID
    readonly name: string
    readonly size: number
    readonly type: string
    readonly lastModified: number
}

// 尺寸相关类型
export interface Dimensions {
    readonly width: number
    readonly height: number
}

export interface Position {
    readonly x: number
    readonly y: number
}

export interface Rect extends Position, Dimensions {}

// 颜色相关类型
export type HexColor = `#${string}`
export type RGBColor = readonly [number, number, number]
export type RGBAColor = readonly [number, number, number, number]

export interface ColorInfo {
    readonly hex: HexColor
    readonly rgb: RGBColor
    readonly name?: string
}

// 状态相关类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface AsyncState<T = unknown, E = Error> {
    readonly data: T | null
    readonly error: E | null
    readonly status: LoadingState
}

// 事件相关类型
export interface BaseEvent {
    readonly timestamp: Timestamp
    readonly type: string
}

export interface UserEvent extends BaseEvent {
    readonly userId?: ID
    readonly sessionId?: ID
}
```

#### 1.2 创建业务领域类型
```typescript
// app/shared/types/image.ts
import { ID, Dimensions, FileInfo, AsyncState } from './global'

/**
 * 图片相关类型定义
 */

// 图片状态枚举
export const ImageStatus = {
    IDLE: 'idle',
    UPLOADING: 'uploading',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    ERROR: 'error',
} as const

export type ImageStatusType = typeof ImageStatus[keyof typeof ImageStatus]

// 图片项目接口
export interface ImageItem extends FileInfo {
    readonly preview: string
    readonly status: ImageStatusType
    readonly aspectRatio: number
    readonly deviceIndexes: readonly number[]
    readonly uploadProgress?: number
    readonly error?: string
    readonly metadata?: ImageMetadata
}

// 图片元数据
export interface ImageMetadata {
    readonly dimensions: Dimensions
    readonly colorProfile?: string
    readonly exif?: Record<string, unknown>
    readonly dominantColors?: readonly ColorInfo[]
}

// 图片处理选项
export interface ImageProcessingOptions {
    readonly maxWidth?: number
    readonly maxHeight?: number
    readonly quality?: number
    readonly format?: 'jpeg' | 'png' | 'webp'
    readonly preserveExif?: boolean
}

// 图片验证规则
export interface ImageValidationRules {
    readonly allowedTypes: readonly string[]
    readonly maxFileSize: number
    readonly minDimensions?: Dimensions
    readonly maxDimensions?: Dimensions
}

// 图片验证结果
export interface ImageValidationResult {
    readonly isValid: boolean
    readonly errors: readonly string[]
    readonly warnings: readonly string[]
}

// 图片上传状态
export type ImageUploadState = AsyncState<ImageItem[], Error> & {
    readonly progress: Record<ID, number>
}
```

#### 1.3 创建组件 Props 类型
```typescript
// app/shared/types/components.ts
import { ReactNode, CSSProperties } from 'react'
import { ID } from './global'

/**
 * 组件相关类型定义
 */

// 基础组件 Props
export interface BaseComponentProps {
    readonly className?: string
    readonly style?: CSSProperties
    readonly children?: ReactNode
    readonly testId?: string
}

// 可交互组件 Props
export interface InteractiveComponentProps extends BaseComponentProps {
    readonly disabled?: boolean
    readonly loading?: boolean
    readonly onClick?: () => void
}

// 表单组件 Props
export interface FormComponentProps<T = unknown> extends BaseComponentProps {
    readonly value: T
    readonly onChange: (value: T) => void
    readonly error?: string
    readonly required?: boolean
    readonly placeholder?: string
}

// 模态框 Props
export interface ModalProps extends BaseComponentProps {
    readonly isOpen: boolean
    readonly onClose: () => void
    readonly title?: string
    readonly size?: 'sm' | 'md' | 'lg' | 'xl'
    readonly closeOnOverlayClick?: boolean
    readonly closeOnEscape?: boolean
}

// 列表组件 Props
export interface ListComponentProps<T> extends BaseComponentProps {
    readonly items: readonly T[]
    readonly renderItem: (item: T, index: number) => ReactNode
    readonly keyExtractor: (item: T, index: number) => string
    readonly loading?: boolean
    readonly empty?: ReactNode
    readonly error?: string
}

// 虚拟滚动 Props
export interface VirtualListProps<T> extends ListComponentProps<T> {
    readonly itemHeight: number | ((item: T, index: number) => number)
    readonly containerHeight: number
    readonly overscan?: number
}
```

### 方案 2：实现严格的类型约束

#### 2.1 使用字面量类型和联合类型
```typescript
// app/shared/types/enums.ts
/**
 * 严格的枚举和字面量类型定义
 */

// 设备类型
export const DeviceType = {
    PHONE: 'phone',
    TABLET: 'tablet',
    LAPTOP: 'laptop',
    DESKTOP: 'desktop',
    WATCH: 'watch',
} as const

export type DeviceTypeValue = typeof DeviceType[keyof typeof DeviceType]

// 布局类型
export const LayoutType = {
    SINGLE: 'single',
    DUAL: 'dual',
    TRIPLE: 'triple',
    GRID: 'grid',
} as const

export type LayoutTypeValue = typeof LayoutType[keyof typeof LayoutType]

// 导出格式
export const ExportFormat = {
    PNG: 'png',
    JPEG: 'jpeg',
    WEBP: 'webp',
    SVG: 'svg',
} as const

export type ExportFormatValue = typeof ExportFormat[keyof typeof ExportFormat]

// 质量预设
export const QualityPreset = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    ULTRA: 'ultra',
} as const

export type QualityPresetValue = typeof QualityPreset[keyof typeof QualityPreset]

// 主题模式
export const ThemeMode = {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto',
} as const

export type ThemeModeValue = typeof ThemeMode[keyof typeof ThemeMode]
```

#### 2.2 使用泛型和条件类型
```typescript
// app/shared/types/utilities.ts
/**
 * 工具类型定义
 */

// 深度只读类型
export type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

// 可选属性类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// 必需属性类型
export type Required<T, K extends keyof T> = T & Required<Pick<T, K>>

// 非空类型
export type NonNullable<T> = T extends null | undefined ? never : T

// 数组元素类型
export type ArrayElement<T> = T extends readonly (infer U)[] ? U : never

// 函数参数类型
export type Parameters<T> = T extends (...args: infer P) => unknown ? P : never

// 函数返回类型
export type ReturnType<T> = T extends (...args: unknown[]) => infer R ? R : unknown

// 异步函数返回类型
export type AsyncReturnType<T> = T extends (...args: unknown[]) => Promise<infer R> ? R : never

// 键值对类型
export type KeyValuePair<K extends string | number | symbol, V> = {
    readonly [P in K]: V
}

// 条件类型示例
export type ApiResponse<T> = T extends string
    ? { message: T }
    : T extends Error
    ? { error: T }
    : { data: T }

// 状态机类型
export type StateMachine<S extends string, E extends string> = {
    readonly currentState: S
    readonly availableTransitions: readonly E[]
    readonly transition: (event: E) => S
}
```

#### 2.3 创建类型守卫和验证函数
```typescript
// app/shared/types/guards.ts
import { ImageItem, ImageStatusType } from './image'
import { DeviceTypeValue, LayoutTypeValue } from './enums'

/**
 * 类型守卫函数
 */

// 基础类型守卫
export const isString = (value: unknown): value is string => {
    return typeof value === 'string'
}

export const isNumber = (value: unknown): value is number => {
    return typeof value === 'number' && !isNaN(value)
}

export const isObject = (value: unknown): value is Record<string, unknown> => {
    return typeof value === 'object' && value !== null && !Array.isArray(value)
}

export const isArray = <T>(value: unknown): value is T[] => {
    return Array.isArray(value)
}

// 业务类型守卫
export const isImageItem = (value: unknown): value is ImageItem => {
    if (!isObject(value)) return false
    
    const obj = value as Record<string, unknown>
    
    return (
        isString(obj.id) &&
        isString(obj.name) &&
        isNumber(obj.size) &&
        isString(obj.type) &&
        isString(obj.preview) &&
        isImageStatus(obj.status) &&
        isNumber(obj.aspectRatio)
    )
}

export const isImageStatus = (value: unknown): value is ImageStatusType => {
    return isString(value) && ['idle', 'uploading', 'processing', 'completed', 'error'].includes(value)
}

export const isDeviceType = (value: unknown): value is DeviceTypeValue => {
    return isString(value) && ['phone', 'tablet', 'laptop', 'desktop', 'watch'].includes(value)
}

export const isLayoutType = (value: unknown): value is LayoutTypeValue => {
    return isString(value) && ['single', 'dual', 'triple', 'grid'].includes(value)
}

// 复合类型守卫
export const isImageArray = (value: unknown): value is ImageItem[] => {
    return isArray(value) && value.every(isImageItem)
}

// 运行时类型验证
export const validateImageItem = (value: unknown): ImageItem => {
    if (!isImageItem(value)) {
        throw new TypeError('Invalid ImageItem object')
    }
    return value
}

export const validateImageArray = (value: unknown): ImageItem[] => {
    if (!isImageArray(value)) {
        throw new TypeError('Invalid ImageItem array')
    }
    return value
}
```

## 📋 执行步骤

### 步骤 1：类型定义审计
- [ ] 扫描所有 TypeScript 文件
- [ ] 识别重复的类型定义
- [ ] 分析 any 类型的使用情况
- [ ] 评估类型安全性问题

### 步骤 2：创建类型定义体系
- [ ] 创建 `app/shared/types/` 目录结构
- [ ] 实现全局基础类型
- [ ] 实现业务领域类型
- [ ] 实现组件相关类型

### 步骤 3：实现严格类型约束
- [ ] 使用字面量类型替换字符串类型
- [ ] 实现泛型和条件类型
- [ ] 创建类型守卫函数
- [ ] 添加运行时类型验证

### 步骤 4：重构现有代码
- [ ] 替换 any 类型为具体类型
- [ ] 更新接口定义
- [ ] 添加类型约束
- [ ] 修复类型错误

### 步骤 5：配置 TypeScript 严格模式
- [ ] 启用严格类型检查
- [ ] 配置类型检查规则
- [ ] 添加 ESLint 类型规则
- [ ] 设置 CI/CD 类型检查

## ✅ 验收标准

- [ ] 消除所有 any 类型使用
- [ ] 建立完整的类型定义体系
- [ ] TypeScript 严格模式无错误
- [ ] 类型覆盖率达到 95% 以上
- [ ] 运行时类型错误显著减少
- [ ] 开发体验和 IDE 支持改善

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
