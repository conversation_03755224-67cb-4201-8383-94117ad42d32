# 01-渲染性能优化

## 🎯 问题描述

项目中存在不必要的重渲染、缺少性能优化措施，导致用户交互响应慢、页面卡顿，特别是在图片处理、画布渲染和复杂组件更新时性能问题明显。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 缺少 React 性能优化
**问题**：组件没有使用 React 的性能优化 API
**具体表现**：
- 缺少 `React.memo` 包装纯组件
- 缺少 `useMemo` 优化计算密集型操作
- 缺少 `useCallback` 优化事件处理函数
- 缺少 `useDeferredValue` 处理非紧急更新

**问题示例**：
```typescript
// 未优化的组件 - 每次父组件更新都会重渲染
const ImageItem = ({ image, onSelect, onRemove }) => {
    // 每次渲染都会创建新的函数
    const handleClick = () => onSelect(image.id)
    const handleDelete = () => onRemove(image.id)
    
    return (
        <div onClick={handleClick}>
            <img src={image.preview} />
            <button onClick={handleDelete}>删除</button>
        </div>
    )
}
```

### 1.2 状态更新导致过度渲染
**问题**：状态更新触发不必要的组件重渲染
**具体表现**：
- 全局状态更新影响不相关组件
- 频繁的状态更新导致渲染抖动
- 拖拽操作时的高频状态更新
- 鼠标移动事件导致的频繁重渲染

### 1.3 大列表渲染性能问题
**问题**：图片列表、设备列表等大量数据渲染时性能差
**具体表现**：
- 图片列表没有虚拟滚动
- 所有图片同时渲染和加载
- 缺少懒加载机制
- 没有分页或无限滚动

### 1.4 复杂计算没有缓存
**问题**：重复的复杂计算没有缓存结果
**具体表现**：
- 尺寸计算每次都重新执行
- 颜色处理算法重复计算
- 布局计算没有缓存
- 图片处理结果没有缓存

### 1.5 DOM 操作性能问题
**问题**：频繁的 DOM 操作和样式更新
**具体表现**：
- 动画使用 JavaScript 而非 CSS
- 频繁的样式计算和更新
- 大量的 DOM 查询操作
- 没有使用 CSS 硬件加速

## 🎯 解决方案

### 方案 1：React 性能优化

#### 1.1 使用 React.memo 优化组件
```typescript
// app/components/ImageItem.tsx - 优化后的组件
interface ImageItemProps {
    image: ImageItem
    onSelect: (id: string) => void
    onRemove: (id: string) => void
    isSelected?: boolean
}

export const ImageItem = React.memo<ImageItemProps>(({ 
    image, 
    onSelect, 
    onRemove, 
    isSelected 
}) => {
    // 使用 useCallback 缓存事件处理函数
    const handleClick = useCallback(() => {
        onSelect(image.id)
    }, [image.id, onSelect])
    
    const handleDelete = useCallback((e: React.MouseEvent) => {
        e.stopPropagation()
        onRemove(image.id)
    }, [image.id, onRemove])
    
    // 使用 useMemo 缓存复杂计算
    const imageStyle = useMemo(() => ({
        width: '100%',
        height: 'auto',
        opacity: isSelected ? 0.8 : 1,
        transform: isSelected ? 'scale(0.95)' : 'scale(1)',
        transition: 'all 0.2s ease-in-out',
    }), [isSelected])
    
    return (
        <div 
            className="image-item" 
            onClick={handleClick}
            data-selected={isSelected}
        >
            <img 
                src={image.preview} 
                alt={image.file.name}
                style={imageStyle}
                loading="lazy" // 原生懒加载
            />
            <button 
                className="delete-button"
                onClick={handleDelete}
                aria-label="删除图片"
            >
                ×
            </button>
        </div>
    )
}, (prevProps, nextProps) => {
    // 自定义比较函数，只在必要时重渲染
    return (
        prevProps.image.id === nextProps.image.id &&
        prevProps.isSelected === nextProps.isSelected &&
        prevProps.image.preview === nextProps.image.preview
    )
})
```

#### 1.2 使用 useMemo 优化计算
```typescript
// app/hooks/useCanvasDimensions.ts
export const useCanvasDimensions = (
    containerRef: RefObject<HTMLElement>,
    aspectRatio: number = 16 / 9
) => {
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
    
    // 使用 useMemo 缓存尺寸计算
    const dimensions = useMemo(() => {
        if (!containerSize.width || !containerSize.height) {
            return { width: 0, height: 0, scale: 1 }
        }
        
        const { width: containerWidth, height: containerHeight } = containerSize
        
        // 复杂的尺寸计算逻辑
        let canvasWidth = containerWidth
        let canvasHeight = canvasWidth / aspectRatio
        
        if (canvasHeight > containerHeight) {
            canvasHeight = containerHeight
            canvasWidth = canvasHeight * aspectRatio
        }
        
        const scale = Math.min(
            containerWidth / canvasWidth,
            containerHeight / canvasHeight
        )
        
        return {
            width: canvasWidth,
            height: canvasHeight,
            scale
        }
    }, [containerSize.width, containerSize.height, aspectRatio])
    
    // 使用 ResizeObserver 监听容器尺寸变化
    useEffect(() => {
        if (!containerRef.current) return
        
        const resizeObserver = new ResizeObserver(
            // 使用 throttle 限制更新频率
            throttle((entries) => {
                const entry = entries[0]
                if (entry) {
                    setContainerSize({
                        width: entry.contentRect.width,
                        height: entry.contentRect.height
                    })
                }
            }, 16) // 约 60fps
        )
        
        resizeObserver.observe(containerRef.current)
        
        return () => resizeObserver.disconnect()
    }, [containerRef])
    
    return dimensions
}
```

#### 1.3 使用 useCallback 优化事件处理
```typescript
// app/components/ImageManager.tsx
export const ImageManager: React.FC = () => {
    const { images, addImages, removeImage, selectImage } = useImageStore()
    
    // 使用 useCallback 缓存事件处理函数
    const handleImageSelect = useCallback((imageId: string) => {
        selectImage(imageId)
    }, [selectImage])
    
    const handleImageRemove = useCallback((imageId: string) => {
        removeImage(imageId)
    }, [removeImage])
    
    const handleFilesUpload = useCallback(async (files: File[]) => {
        await addImages(files)
    }, [addImages])
    
    // 使用 useMemo 缓存渲染列表
    const imageList = useMemo(() => 
        images.map(image => (
            <ImageItem
                key={image.id}
                image={image}
                onSelect={handleImageSelect}
                onRemove={handleImageRemove}
                isSelected={selectedImageId === image.id}
            />
        )), 
        [images, handleImageSelect, handleImageRemove, selectedImageId]
    )
    
    return (
        <div className="image-manager">
            <ImageUploader onUpload={handleFilesUpload} />
            <div className="image-list">
                {imageList}
            </div>
        </div>
    )
}
```

### 方案 2：虚拟滚动和懒加载

#### 2.1 实现虚拟滚动
```typescript
// app/components/VirtualList.tsx
interface VirtualListProps<T> {
    items: T[]
    itemHeight: number
    containerHeight: number
    renderItem: (item: T, index: number) => React.ReactNode
    overscan?: number
}

export const VirtualList = <T,>({
    items,
    itemHeight,
    containerHeight,
    renderItem,
    overscan = 5
}: VirtualListProps<T>) => {
    const [scrollTop, setScrollTop] = useState(0)
    
    // 计算可见范围
    const visibleRange = useMemo(() => {
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
        const endIndex = Math.min(
            items.length - 1,
            Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
        )
        
        return { startIndex, endIndex }
    }, [scrollTop, itemHeight, containerHeight, items.length, overscan])
    
    // 只渲染可见的项目
    const visibleItems = useMemo(() => {
        const { startIndex, endIndex } = visibleRange
        return items.slice(startIndex, endIndex + 1).map((item, index) => ({
            item,
            index: startIndex + index
        }))
    }, [items, visibleRange])
    
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
        setScrollTop(e.currentTarget.scrollTop)
    }, [])
    
    return (
        <div 
            className="virtual-list"
            style={{ height: containerHeight, overflow: 'auto' }}
            onScroll={handleScroll}
        >
            {/* 总高度占位符 */}
            <div style={{ height: items.length * itemHeight, position: 'relative' }}>
                {visibleItems.map(({ item, index }) => (
                    <div
                        key={index}
                        style={{
                            position: 'absolute',
                            top: index * itemHeight,
                            height: itemHeight,
                            width: '100%'
                        }}
                    >
                        {renderItem(item, index)}
                    </div>
                ))}
            </div>
        </div>
    )
}
```

#### 2.2 图片懒加载组件
```typescript
// app/components/LazyImage.tsx
interface LazyImageProps {
    src: string
    alt: string
    placeholder?: string
    className?: string
    onLoad?: () => void
    onError?: () => void
}

export const LazyImage: React.FC<LazyImageProps> = ({
    src,
    alt,
    placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
    className,
    onLoad,
    onError
}) => {
    const [isLoaded, setIsLoaded] = useState(false)
    const [hasError, setHasError] = useState(false)
    const [isInView, setIsInView] = useState(false)
    const imgRef = useRef<HTMLImageElement>(null)
    
    // 使用 Intersection Observer 检测图片是否进入视口
    useEffect(() => {
        if (!imgRef.current) return
        
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsInView(true)
                    observer.disconnect()
                }
            },
            { threshold: 0.1 }
        )
        
        observer.observe(imgRef.current)
        
        return () => observer.disconnect()
    }, [])
    
    const handleLoad = useCallback(() => {
        setIsLoaded(true)
        onLoad?.()
    }, [onLoad])
    
    const handleError = useCallback(() => {
        setHasError(true)
        onError?.()
    }, [onError])
    
    return (
        <div ref={imgRef} className={`lazy-image ${className || ''}`}>
            {isInView && (
                <img
                    src={hasError ? placeholder : (isLoaded ? src : placeholder)}
                    alt={alt}
                    onLoad={handleLoad}
                    onError={handleError}
                    style={{
                        opacity: isLoaded ? 1 : 0.5,
                        transition: 'opacity 0.3s ease-in-out'
                    }}
                />
            )}
        </div>
    )
}
```

### 方案 3：状态更新优化

#### 3.1 使用 useDeferredValue 优化非紧急更新
```typescript
// app/components/SearchableImageList.tsx
export const SearchableImageList: React.FC = () => {
    const [searchTerm, setSearchTerm] = useState('')
    const deferredSearchTerm = useDeferredValue(searchTerm)
    const { images } = useImageStore()
    
    // 使用 deferred 值进行过滤，避免阻塞输入
    const filteredImages = useMemo(() => {
        if (!deferredSearchTerm) return images
        
        return images.filter(image => 
            image.file.name.toLowerCase().includes(deferredSearchTerm.toLowerCase())
        )
    }, [images, deferredSearchTerm])
    
    return (
        <div>
            <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索图片..."
            />
            <ImageList images={filteredImages} />
        </div>
    )
}
```

#### 3.2 使用 useTransition 优化状态更新
```typescript
// app/hooks/useImageUpload.ts
export const useImageUpload = () => {
    const [isPending, startTransition] = useTransition()
    const { addImages } = useImageStore()
    
    const uploadImages = useCallback((files: File[]) => {
        startTransition(() => {
            // 将图片处理标记为非紧急更新
            addImages(files)
        })
    }, [addImages])
    
    return {
        uploadImages,
        isPending
    }
}
```

## 📋 执行步骤

### 步骤 1：性能分析和基准测试
- [ ] 使用 React DevTools Profiler 分析组件渲染
- [ ] 使用 Chrome DevTools 分析页面性能
- [ ] 建立性能基准指标
- [ ] 识别性能瓶颈组件

### 步骤 2：实现基础性能优化
- [ ] 为纯组件添加 React.memo
- [ ] 使用 useCallback 优化事件处理函数
- [ ] 使用 useMemo 优化复杂计算
- [ ] 实现防抖和节流机制

### 步骤 3：实现高级性能优化
- [ ] 实现虚拟滚动组件
- [ ] 添加图片懒加载
- [ ] 使用 useTransition 优化状态更新
- [ ] 实现组件级别的代码分割

### 步骤 4：优化渲染策略
- [ ] 减少不必要的重渲染
- [ ] 优化状态更新策略
- [ ] 实现智能的组件更新
- [ ] 添加性能监控

### 步骤 5：测试和验证
- [ ] 进行性能回归测试
- [ ] 验证用户体验改善
- [ ] 监控内存使用情况
- [ ] 优化移动端性能

## ✅ 验收标准

- [ ] 首屏渲染时间减少 30% 以上
- [ ] 交互响应时间控制在 100ms 以内
- [ ] 大列表滚动流畅度显著提升
- [ ] 内存使用量没有明显增加
- [ ] 所有功能正常工作
- [ ] 移动端性能表现良好

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
