# 03-内存管理优化

## 🎯 问题描述

项目中存在多个内存泄漏风险点，包括 createObjectURL 未释放、事件监听器未清理、定时器未清除等问题，可能导致内存占用持续增长，影响应用性能。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 3.1 createObjectURL 内存泄漏
**问题**：图片预览 URL 创建后未及时释放
**具体表现**：
```typescript
// app/ImageMange/imageMangeIndex.tsx 中的问题
const preview = URL.createObjectURL(file) // ❌ 创建后未释放
```

**风险**：每个图片文件都会创建一个 Blob URL，如果不释放会导致内存持续增长

### 3.2 事件监听器未清理
**问题**：组件卸载时未清理事件监听器
**具体表现**：
- ResizeObserver 可能未正确断开连接
- 拖拽事件监听器可能未清理
- 全局事件监听器（如 window.resize）未移除

### 3.3 定时器和异步操作未清理
**问题**：组件卸载时异步操作仍在执行
**具体表现**：
- setTimeout/setInterval 未清除
- Promise 链未中断
- 图片加载异步操作未取消

## 🎯 解决方案

### 方案 1：URL 对象生命周期管理

#### 1.1 创建 URL 管理 Hook
```typescript
// app/shared/hooks/useObjectURL.ts
export const useObjectURL = () => {
    const urlsRef = useRef<Set<string>>(new Set())
    
    const createObjectURL = useCallback((file: File): string => {
        const url = URL.createObjectURL(file)
        urlsRef.current.add(url)
        return url
    }, [])
    
    const revokeObjectURL = useCallback((url: string) => {
        if (urlsRef.current.has(url)) {
            URL.revokeObjectURL(url)
            urlsRef.current.delete(url)
        }
    }, [])
    
    const revokeAllObjectURLs = useCallback(() => {
        urlsRef.current.forEach(url => {
            URL.revokeObjectURL(url)
        })
        urlsRef.current.clear()
    }, [])
    
    // 组件卸载时自动清理所有 URL
    useEffect(() => {
        return () => {
            revokeAllObjectURLs()
        }
    }, [revokeAllObjectURLs])
    
    return {
        createObjectURL,
        revokeObjectURL,
        revokeAllObjectURLs
    }
}
```

### 方案 2：事件监听器清理管理

#### 2.1 创建事件监听器管理 Hook
```typescript
// app/shared/hooks/useEventListener.ts
export const useEventListener = <T extends keyof WindowEventMap>(
    eventType: T,
    handler: (event: WindowEventMap[T]) => void,
    element: Window | Element | null = window,
    options?: AddEventListenerOptions
) => {
    const savedHandler = useRef(handler)
    
    useEffect(() => {
        savedHandler.current = handler
    }, [handler])
    
    useEffect(() => {
        if (!element) return
        
        const eventListener = (event: Event) => {
            savedHandler.current(event as WindowEventMap[T])
        }
        
        element.addEventListener(eventType, eventListener, options)
        
        return () => {
            element.removeEventListener(eventType, eventListener, options)
        }
    }, [eventType, element, options])
}
```

### 方案 3：异步操作取消管理

#### 3.1 创建可取消的异步操作 Hook
```typescript
// app/shared/hooks/useCancellablePromise.ts
export const useCancellablePromise = () => {
    const pendingPromises = useRef<Set<{ cancel: () => void }>>(new Set())
    
    const cancellablePromise = useCallback(<T>(
        promise: Promise<T>
    ): { promise: Promise<T>; cancel: () => void } => {
        let isCancelled = false
        
        const wrappedPromise = new Promise<T>((resolve, reject) => {
            promise
                .then(value => {
                    if (!isCancelled) {
                        resolve(value)
                    }
                })
                .catch(error => {
                    if (!isCancelled) {
                        reject(error)
                    }
                })
        })
        
        const cancel = () => {
            isCancelled = true
            pendingPromises.current.delete(cancellablePromiseObj)
        }
        
        const cancellablePromiseObj = { cancel }
        pendingPromises.current.add(cancellablePromiseObj)
        
        return {
            promise: wrappedPromise,
            cancel
        }
    }, [])
    
    // 组件卸载时取消所有待处理的 Promise
    useEffect(() => {
        return () => {
            pendingPromises.current.forEach(({ cancel }) => cancel())
            pendingPromises.current.clear()
        }
    }, [])
    
    return { cancellablePromise }
}
```

## 📋 执行步骤

### 步骤 1：内存泄漏审计
- [ ] 扫描所有 createObjectURL 使用
- [ ] 检查事件监听器清理情况
- [ ] 识别未清理的定时器和异步操作
- [ ] 使用浏览器开发工具分析内存使用

### 步骤 2：实现内存管理工具
- [ ] 创建 useObjectURL Hook
- [ ] 创建 useEventListener Hook
- [ ] 创建 useResizeObserver Hook
- [ ] 创建 useCancellablePromise Hook

### 步骤 3：重构现有组件
- [ ] 重构图片管理组件使用 URL 管理
- [ ] 重构事件监听器使用管理 Hook
- [ ] 重构异步操作使用可取消 Promise
- [ ] 添加组件卸载清理逻辑

### 步骤 4：内存监控和测试
- [ ] 添加内存使用监控
- [ ] 进行内存泄漏测试
- [ ] 验证清理机制有效性
- [ ] 性能基准测试

## ✅ 验收标准

- [ ] 所有 createObjectURL 都有对应的 revokeObjectURL
- [ ] 所有事件监听器都有清理机制
- [ ] 异步操作可以被正确取消
- [ ] 内存使用量保持稳定
- [ ] 长时间使用后无明显内存增长

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
