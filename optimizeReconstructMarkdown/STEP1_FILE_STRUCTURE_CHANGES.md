# 📁 第一步：文件结构重构变更计划

## 🎯 变更概述

这是第一阶段基础重构的第一步，主要目标是：
1. 清理根目录，移动文档和配置文件
2. 重新组织 app 目录结构
3. 统一文件命名规范
4. 建立清晰的功能模块分类

## 📊 当前问题分析

### 🚨 根目录问题
- **文件过多**：根目录有 20+ 个文件，包含配置、文档、测试文件
- **中英文混合**：`渐变颜色生成系统技术文档.md`、`根据图片获取颜色test（成功例子）.html`
- **功能分散**：相关文件分布在不同位置

### 🚨 app 目录问题
- **命名不统一**：`PcFrame_Effects__ModalDefault.tsx` vs `CanvasContainer.tsx`
- **功能混乱**：组件直接放在 app 根目录
- **模块分散**：相关功能文件分布在不同子目录

## 🎯 目标文件结构

```
project-root/
├── README.md                    # 项目主要说明（保留）
├── package.json                 # 项目依赖（保留）
├── package-lock.json           # 依赖锁定（保留）
├── pnpm-lock.yaml              # pnpm 锁定（保留）
├── next.config.ts              # Next.js 配置（保留）
├── tsconfig.json               # TypeScript 配置（保留）
├── eslint.config.mjs           # ESLint 配置（保留）
├── postcss.config.mjs          # PostCSS 配置（保留）
├── next-env.d.ts               # Next.js 类型声明（保留）
├── tsconfig.tsbuildinfo        # TypeScript 构建信息（保留）
├── docs/                       # 📚 项目文档
│   ├── README.md              # 文档总览
│   ├── development/           # 开发相关文档
│   │   ├── claude-guide.md    # Claude 使用指南
│   │   ├── commit-guidelines.md # 提交规范
│   │   ├── git-conventions.md # Git 规范
│   │   └── formatting-solutions.md # 格式化解决方案
│   ├── architecture/          # 架构设计文档
│   │   └── gradient-color-system.md # 渐变颜色系统
│   └── guides/                # 使用指南
├── scripts/                    # 🔧 项目脚本
│   ├── format-check.mjs       # 格式检查脚本
│   └── test-format.ts         # 格式化测试
├── tests/                      # 🧪 测试文件
│   └── fixtures/              # 测试数据
│       └── color-extraction-test.html # 颜色提取测试
├── app/
│   ├── components/            # 🧩 通用组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── layout/           # 布局组件
│   │   └── business/         # 业务组件
│   ├── features/             # 🎯 功能模块
│   │   ├── image-management/ # 图片管理
│   │   ├── device-mockup/    # 设备模拟
│   │   ├── color-extraction/ # 颜色提取
│   │   ├── canvas-rendering/ # 画布渲染
│   │   └── export-system/    # 导出系统
│   ├── shared/               # 🔗 共享资源
│   │   ├── types/           # 类型定义
│   │   ├── utils/           # 工具函数
│   │   ├── hooks/           # 自定义钩子
│   │   ├── constants/       # 常量定义
│   │   └── config/          # 配置文件
│   └── styles/              # 🎨 样式文件
│       ├── globals/         # 全局样式
│       └── components/      # 组件样式
├── public/                   # 📁 静态资源（保留）
├── out/                      # 📦 构建输出（保留）
└── node_modules/            # 📦 依赖包（保留）
```

## 📋 详细变更清单

### 第一部分：根目录清理

#### 🗂️ 文档文件移动
```bash
# 移动文档文件
CLAUDE.md → docs/development/claude-guide.md
doc.md → docs/README.md
commit让AI生成内容.md → docs/development/commit-guidelines.md
git提交规范规则.md → docs/development/git-conventions.md
渐变颜色生成系统技术文档.md → docs/architecture/gradient-color-system.md
自动格式化问题解决方案.md → docs/development/formatting-solutions.md
```

#### 🔧 脚本文件移动
```bash
# 移动脚本文件
format-check.mjs → scripts/format-check.mjs
test-format.ts → scripts/test-format.ts
```

#### 🧪 测试文件移动
```bash
# 移动测试文件
根据图片获取颜色test（成功例子）.html → tests/fixtures/color-extraction-test.html
```

#### 🗑️ 清理临时目录
```bash
# 删除或移动临时目录
__MARKDOWN_MD__/ → docs/legacy/ (或直接删除)
build/ → 检查内容后决定保留或删除
```

### 第二部分：app 目录重构

#### 🧩 组件重新分类

##### UI 基础组件 (app/components/ui/)
```bash
# 这些组件将在后续步骤中创建，当前先创建目录结构
mkdir -p app/components/ui
mkdir -p app/components/layout  
mkdir -p app/components/business
```

##### 布局组件 (app/components/layout/)
```bash
# 移动布局相关组件
MobileMockup_Layout.tsx → app/components/layout/MobileLayout.tsx
MobileMockup_Tabs.tsx → app/components/layout/MobileTabs.tsx
Public_MockupSmallLayout.tsx → app/components/layout/SmallLayout.tsx
Public_PanelTabs.tsx → app/components/layout/PanelTabs.tsx
```

##### 业务组件 (app/components/business/)
```bash
# 移动业务逻辑组件
PcMockup_Media.tsx → app/components/business/MediaSelector.tsx
PcMockup_Visibility.tsx → app/components/business/VisibilityControl.tsx
PcRightSlider.tsx → app/components/business/RightSlider.tsx
Public_MockupDetails.tsx → app/components/business/MockupDetails.tsx
Public_MockupStyle.tsx → app/components/business/MockupStyle.tsx
Public_MockupShadow.tsx → app/components/business/MockupShadow.tsx
```

#### 🎯 功能模块重构

##### 图片管理模块 (app/features/image-management/)
```bash
# 整合图片管理相关功能
ImageMange/ → app/features/image-management/
├── core/
│   ├── imageMangeIndex.tsx → store.ts
│   └── imageConfig.ts → config.ts
├── components/
│   ├── ImageUploader.tsx (新建)
│   ├── ImageList.tsx (新建)
│   └── ImageItem.tsx (新建)
└── docs/
    ├── 最终设计流程方案.md
    ├── 流程.md
    ├── 设计.md
    └── 配置重构总结.md
```

##### 颜色提取模块 (app/features/color-extraction/)
```bash
# 整合颜色相关功能
get_color/ → app/features/color-extraction/
MobileFrame_Magic/ → app/features/color-extraction/magic-background/
test_color/ → app/features/color-extraction/test/
```

##### 设备模拟模块 (app/features/device-mockup/)
```bash
# 整合设备模拟相关组件
Public_MockUp_ModelSelector.tsx → app/features/device-mockup/ModelSelector.tsx
Public_MockUp_SizeSelector.tsx → app/features/device-mockup/SizeSelector.tsx
Public_FrameScene.tsx → app/features/device-mockup/FrameScene.tsx
Public_FrameCustomView.tsx → app/features/device-mockup/CustomView.tsx
```

##### 画布渲染模块 (app/features/canvas-rendering/)
```bash
# 整合画布相关组件
components/CanvasContainer/ → app/features/canvas-rendering/container/
components/DisplayContainer/ → app/features/canvas-rendering/display/
Public_CaptureCanvas.tsx → app/features/canvas-rendering/CaptureCanvas.tsx
```

##### 导出系统模块 (app/features/export-system/)
```bash
# 整合导出相关功能
components/ExportPopver/ → app/features/export-system/popover/
utils/imageExport.ts → app/features/export-system/core/export.ts
```

#### 🔗 共享资源重构

##### 类型定义 (app/shared/types/)
```bash
# 整合类型定义
types/ → app/shared/types/
├── colorTypes.ts
├── imageTypes.ts (新建)
├── deviceTypes.ts (新建)
└── index.ts (新建)
```

##### 工具函数 (app/shared/utils/)
```bash
# 整合工具函数
utils/ → app/shared/utils/
├── browser.ts
├── imageLoader.ts
├── picaConfig.ts
├── viewRenderCheck.ts
└── index.ts (新建)
```

##### 自定义钩子 (app/shared/hooks/)
```bash
# 整合自定义钩子
hooks/ → app/shared/hooks/
├── useAppState.ts
├── useBackgroundStore.ts
├── useColorGenerationFromImage.ts
├── useCustomImageStore.ts
├── useMagicBackground.ts
├── useMagicBackgroundStore.ts
├── useMockupShadowStore.ts
├── useMockupStore.ts
├── usePasteImage.ts
├── useResizeImage.ts
├── useSceneStore.ts
├── pasteUploadStore.ts
└── index.ts (新建)
```

##### 配置文件 (app/shared/config/)
```bash
# 整合配置文件
config/ → app/shared/config/
├── ImageSources.ts
└── index.ts (新建)
```

#### 🎨 样式文件重构

##### 样式文件 (app/styles/)
```bash
# 重新组织样式文件
css/ → app/styles/
├── globals/
│   └── globals.scss
└── components/
    └── (组件样式文件)
```

### 第三部分：模态框组件重构

#### 🔄 模态框统一命名
```bash
# 重命名模态框组件
PcFrame_Effects__ModalDefault.tsx → app/components/business/modals/EffectsModal.tsx
PcFrame_Effects__ModalPortrait.tsx → app/components/business/modals/PortraitEffectsModal.tsx
PcFrame_Effects__ModalVef.tsx → app/components/business/modals/VefEffectsModal.tsx
PcFrame_ShapesItemsModal.tsx → app/components/business/modals/ShapesModal.tsx
PcFrame_WaterMarkModal.tsx → app/components/business/modals/WaterMarkModal.tsx
PublicFrame_ColorPicker_Modal.tsx → app/components/business/modals/ColorPickerModal.tsx
PublicFrame_CustomImage_Modal.tsx → app/components/business/modals/CustomImageModal.tsx
Public_FrameSizeModal.tsx → app/components/business/modals/FrameSizeModal.tsx
Public_MediaPickerModal.tsx → app/components/business/modals/MediaPickerModal.tsx
Public_MockupModal.tsx → app/components/business/modals/MockupModal.tsx
```

## ⚠️ 重要注意事项

### 🔒 安全措施
1. **渐进式移动**：一次只移动一个模块，确保应用正常运行
2. **保留备份**：移动前创建 Git 分支备份
3. **路径更新**：移动文件后立即更新所有导入路径
4. **功能验证**：每次移动后都要测试相关功能

### 📝 执行顺序建议
1. **第一步**：创建新的目录结构
2. **第二步**：移动文档和脚本文件（风险最低）
3. **第三步**：移动样式文件
4. **第四步**：移动工具函数和类型定义
5. **第五步**：移动组件文件（风险最高，需要更新大量导入）

### 🔧 工具支持
- 使用 VSCode 的重构功能批量更新导入路径
- 使用 TypeScript 编译器检查路径错误
- 使用 ESLint 检查代码规范

## ✅ 验收标准

- [ ] 根目录文件数量减少至 15 个以内
- [ ] 所有文档文件有序组织在 docs/ 目录
- [ ] app/ 目录结构清晰，功能模块明确
- [ ] 所有导入路径正确更新
- [ ] 应用功能完全正常
- [ ] TypeScript 编译无错误
- [ ] ESLint 检查通过

## 🤔 需要你确认的问题

1. **是否同意这个文件结构设计？**
2. **是否有特定的文件不希望移动？**
3. **是否希望调整某些模块的分类？**
4. **是否希望保留某些中文命名的文件？**

请确认后，我将开始执行具体的文件移动操作！
