# 01-重构文件结构

## 🎯 问题描述

当前项目的文件组织结构存在以下问题：
- 根目录文件过多，缺乏清晰的分类
- 中英文混合的文件夹命名
- 功能模块分散，缺乏统一的组织原则
- 配置文件和业务代码混合

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 根目录文件过多
**问题**：根目录下有太多配置和文档文件，影响项目结构清晰度
**影响文件**：
- `CLAUDE.md`
- `doc.md`
- `commit让AI生成内容.md`
- `git提交规范规则.md`
- `渐变颜色生成系统技术文档.md`
- `自动格式化问题解决方案.md`
- `根据图片获取颜色test（成功例子）.html`

### 1.2 中英文混合命名
**问题**：文件夹使用中文命名，不符合国际化标准
**影响文件夹**：
- `app/features/viewDimensions/utils/重构/`
- `app/MobileFrame_Magic/color推算实现/`
- `app/ImageMange/`
- `__MARKDOWN_MD__/`

### 1.3 功能模块分散
**问题**：相关功能的文件分散在不同位置
**具体表现**：
- 图片管理相关：`app/ImageMange/`、`app/get_color/`
- 设备模拟相关：多个 `PcFrame_*`、`MobileFrame_*` 文件
- 工具函数分散在各个目录

## 🎯 解决方案

### 目标文件结构
```
project-root/
├── docs/                           # 📚 项目文档
│   ├── development/               # 开发相关文档
│   ├── architecture/              # 架构设计文档
│   └── guides/                    # 使用指南
├── app/
│   ├── components/                # 🧩 通用组件
│   │   ├── ui/                   # 基础UI组件
│   │   ├── layout/               # 布局组件
│   │   └── business/             # 业务组件
│   ├── features/                  # 🎯 功能模块
│   │   ├── image-management/     # 图片管理
│   │   ├── device-mockup/        # 设备模拟
│   │   ├── color-extraction/     # 颜色提取
│   │   ├── canvas-rendering/     # 画布渲染
│   │   └── export-system/        # 导出系统
│   ├── shared/                    # 🔗 共享资源
│   │   ├── types/                # 类型定义
│   │   ├── utils/                # 工具函数
│   │   ├── hooks/                # 自定义钩子
│   │   ├── constants/            # 常量定义
│   │   └── config/               # 配置文件
│   └── styles/                    # 🎨 样式文件
│       ├── globals/              # 全局样式
│       ├── components/           # 组件样式
│       └── themes/               # 主题样式
├── public/                        # 📁 静态资源
└── config/                        # ⚙️ 项目配置
    ├── eslint/
    ├── prettier/
    └── build/
```

## 📋 执行步骤

### 步骤 1：创建新的目录结构
- [ ] 创建 `docs/` 目录及子目录
- [ ] 创建 `app/features/` 目录及功能模块子目录
- [ ] 创建 `app/shared/` 目录及子目录
- [ ] 创建 `config/` 目录

### 步骤 2：移动文档文件
- [ ] 移动 `CLAUDE.md` → `docs/development/claude-guide.md`
- [ ] 移动 `doc.md` → `docs/README.md`
- [ ] 移动 `渐变颜色生成系统技术文档.md` → `docs/architecture/gradient-system.md`
- [ ] 移动其他文档到相应位置

### 步骤 3：重构功能模块
- [ ] 整合图片管理：`app/ImageMange/` → `app/features/image-management/`
- [ ] 整合颜色提取：`app/get_color/` → `app/features/color-extraction/`
- [ ] 整合设备模拟相关组件 → `app/features/device-mockup/`
- [ ] 整合画布渲染相关组件 → `app/features/canvas-rendering/`

### 步骤 4：重构共享资源
- [ ] 移动类型定义到 `app/shared/types/`
- [ ] 移动工具函数到 `app/shared/utils/`
- [ ] 移动自定义钩子到 `app/shared/hooks/`
- [ ] 移动常量定义到 `app/shared/constants/`

### 步骤 5：更新导入路径
- [ ] 更新所有文件中的导入路径
- [ ] 配置路径别名（tsconfig.json）
- [ ] 测试所有导入是否正常工作

## ⚠️ 注意事项

1. **渐进式迁移**：不要一次性移动所有文件，分批进行
2. **保持功能完整**：每次移动后都要确保应用正常运行
3. **更新配置**：移动文件后要相应更新 tsconfig.json、eslint 等配置
4. **团队沟通**：大规模文件移动前要与团队成员沟通

## ✅ 验收标准

- [ ] 新的目录结构清晰合理
- [ ] 所有文件都有明确的归属
- [ ] 导入路径都能正常工作
- [ ] 应用功能完全正常
- [ ] 构建和部署流程正常

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
