# 02-统一命名规范

## 🎯 问题描述

当前项目的命名规范不统一，存在以下问题：
- 组件命名风格混乱（PascalCase、snake_case、kebab-case 混用）
- 文件夹使用中文命名
- 函数和变量命名不一致
- 常量命名不规范

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 2.1 组件命名不统一
**问题**：组件文件命名风格混乱
**具体表现**：
- `PcFrame_Effects__ModalDefault.tsx` (snake_case + 双下划线)
- `Public_MediaPickerModal.tsx` (snake_case)
- `MobileFrame_ColorsBackground.tsx` (snake_case)
- `CanvasContainer.tsx` (PascalCase) ✅ 正确

### 2.2 文件夹中文命名
**问题**：使用中文命名，不利于国际化和版本控制
**具体表现**：
- `重构/` → 应改为 `refactor/`
- `color推算实现/` → 应改为 `color-calculation/`
- `__MARKDOWN_MD__/` → 应改为 `docs/` 或 `markdown/`

### 2.3 函数命名不一致
**问题**：函数命名风格不统一
**具体表现**：
- `useIsMobile` ✅ 正确 (camelCase)
- `getAppState` ✅ 正确 (camelCase)
- `setImageDevice` ✅ 正确 (camelCase)
- 但在某些地方可能存在不一致

### 2.4 常量命名不规范
**问题**：常量命名不遵循 SCREAMING_SNAKE_CASE
**需要检查的文件**：
- 配置文件中的常量
- 枚举值的命名
- 全局常量的定义

## 🎯 解决方案

### 命名规范标准

#### 文件和文件夹命名
```typescript
// ✅ 正确的命名方式
components/
├── ui/
│   ├── Button.tsx              // 组件：PascalCase
│   ├── Input.tsx
│   └── Modal.tsx
├── layout/
│   ├── Header.tsx
│   └── Sidebar.tsx
└── business/
    ├── ImageUploader.tsx
    └── DeviceMockup.tsx

features/
├── image-management/           // 功能模块：kebab-case
├── device-mockup/
└── color-extraction/

utils/
├── image-processing.ts         // 工具文件：kebab-case
├── color-utils.ts
└── validation-helpers.ts
```

#### 代码命名规范
```typescript
// ✅ 组件命名：PascalCase
export const ImageUploader = () => {}
export const DeviceMockupRenderer = () => {}

// ✅ 函数命名：camelCase
export const calculateDimensions = () => {}
export const processImageFile = () => {}
export const validateUserInput = () => {}

// ✅ 变量命名：camelCase
const currentImageId = 'img-123'
const isLoadingComplete = false
const userSelectedDevice = null

// ✅ 常量命名：SCREAMING_SNAKE_CASE
export const MAX_FILE_SIZE = 10 * 1024 * 1024
export const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png']
export const DEFAULT_CANVAS_WIDTH = 800

// ✅ 枚举命名：PascalCase，值使用 SCREAMING_SNAKE_CASE
export enum ImageStatus {
    IDLE = 'IDLE',
    UPLOADING = 'UPLOADING',
    COMPLETED = 'COMPLETED',
    ERROR = 'ERROR'
}

// ✅ 接口命名：PascalCase，以 Interface 或描述性名词结尾
export interface ImageItem {
    id: string
    file: File
    status: ImageStatus
}

export interface DeviceConfig {
    width: number
    height: number
    name: string
}
```

## 📋 执行步骤

### 步骤 1：制定命名规范文档
- [ ] 创建 `docs/development/naming-conventions.md`
- [ ] 定义各种命名场景的标准
- [ ] 提供正确和错误的示例

### 步骤 2：重命名文件夹
- [ ] `重构/` → `refactor/`
- [ ] `color推算实现/` → `color-calculation/`
- [ ] `__MARKDOWN_MD__/` → `docs/`
- [ ] `ImageMange/` → `image-management/`

### 步骤 3：重命名组件文件
- [ ] `PcFrame_Effects__ModalDefault.tsx` → `PcEffectsModal.tsx`
- [ ] `Public_MediaPickerModal.tsx` → `MediaPickerModal.tsx`
- [ ] `MobileFrame_ColorsBackground.tsx` → `MobileColorBackground.tsx`
- [ ] 其他类似的文件按规范重命名

### 步骤 4：重命名工具文件
- [ ] 检查 `utils/` 目录下的文件命名
- [ ] 确保使用 kebab-case 命名
- [ ] 更新相应的导入路径

### 步骤 5：检查代码内部命名
- [ ] 检查函数命名是否符合 camelCase
- [ ] 检查常量命名是否符合 SCREAMING_SNAKE_CASE
- [ ] 检查接口和类型命名是否符合 PascalCase

### 步骤 6：更新导入路径和引用
- [ ] 使用 IDE 的重构功能批量更新引用
- [ ] 手动检查可能遗漏的引用
- [ ] 更新配置文件中的路径引用

## 🛠️ 工具支持

### ESLint 规则配置
```javascript
// eslint.config.mjs 中添加命名规则
rules: {
    // 强制 camelCase 命名
    'camelcase': ['error', { 
        'properties': 'never',
        'ignoreDestructuring': false 
    }],
    
    // 组件命名规则
    'react/jsx-pascal-case': 'error',
    
    // 文件命名规则（需要插件支持）
    'unicorn/filename-case': [
        'error',
        {
            'cases': {
                'camelCase': true,
                'pascalCase': true,
                'kebabCase': true
            }
        }
    ]
}
```

### TypeScript 配置
```json
// tsconfig.json 中配置路径别名
{
    "compilerOptions": {
        "baseUrl": ".",
        "paths": {
            "@/components/*": ["app/components/*"],
            "@/features/*": ["app/features/*"],
            "@/shared/*": ["app/shared/*"],
            "@/utils/*": ["app/shared/utils/*"],
            "@/types/*": ["app/shared/types/*"]
        }
    }
}
```

## ⚠️ 注意事项

1. **渐进式重命名**：不要一次性重命名所有文件，分批进行
2. **保持一致性**：团队成员要统一遵循命名规范
3. **更新文档**：重命名后要更新相关文档
4. **测试验证**：每次重命名后都要测试功能是否正常

## ✅ 验收标准

- [ ] 所有文件和文件夹命名符合规范
- [ ] 代码中的命名符合规范
- [ ] 导入路径都能正常工作
- [ ] 应用功能完全正常
- [ ] ESLint 检查通过

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
