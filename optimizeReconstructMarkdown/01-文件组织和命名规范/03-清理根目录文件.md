# 03-清理根目录文件

## 🎯 问题描述

项目根目录文件过多，包含大量配置文件、文档文件和临时文件，影响项目结构的清晰度和专业性。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 3.1 文档文件散乱
**问题**：文档文件直接放在根目录，缺乏分类
**影响文件**：
- `CLAUDE.md` - Claude AI 使用指南
- `README.md` - 项目说明（保留）
- `doc.md` - 项目文档
- `commit让AI生成内容.md` - 提交规范
- `git提交规范规则.md` - Git 规范
- `渐变颜色生成系统技术文档.md` - 技术文档
- `自动格式化问题解决方案.md` - 开发问题解决方案

### 3.2 测试和临时文件
**问题**：测试文件和临时文件混在根目录
**影响文件**：
- `test-format.ts` - 格式化测试文件
- `format-check.mjs` - 格式检查脚本
- `根据图片获取颜色test（成功例子）.html` - 测试文件

### 3.3 构建产物目录
**问题**：构建相关目录结构不清晰
**影响目录**：
- `build/` - 构建相关文件
- `out/` - Next.js 输出目录（保留）

### 3.4 配置文件过多
**问题**：各种配置文件直接放在根目录
**影响文件**：
- `eslint.config.mjs` - ESLint 配置（保留）
- `next.config.ts` - Next.js 配置（保留）
- `postcss.config.mjs` - PostCSS 配置（保留）
- `tsconfig.json` - TypeScript 配置（保留）
- `tailwindcss.config.js` - Tailwind 配置（如果存在）

## 🎯 解决方案

### 目标根目录结构
```
project-root/
├── README.md                    # 📖 项目主要说明（保留）
├── package.json                 # 📦 项目依赖（保留）
├── package-lock.json           # 🔒 依赖锁定（保留）
├── pnpm-lock.yaml              # 🔒 pnpm 锁定（保留）
├── next.config.ts              # ⚙️ Next.js 配置（保留）
├── tsconfig.json               # ⚙️ TypeScript 配置（保留）
├── eslint.config.mjs           # ⚙️ ESLint 配置（保留）
├── postcss.config.mjs          # ⚙️ PostCSS 配置（保留）
├── next-env.d.ts               # 🔧 Next.js 类型声明（保留）
├── tsconfig.tsbuildinfo        # 🔧 TypeScript 构建信息（保留）
├── .eslintignore               # 🔧 ESLint 忽略文件（保留）
├── docs/                       # 📚 项目文档目录
│   ├── README.md              # 文档总览
│   ├── development/           # 开发相关文档
│   ├── architecture/          # 架构设计文档
│   └── guides/                # 使用指南
├── scripts/                    # 🔧 项目脚本
│   ├── format-check.mjs       # 格式检查脚本
│   └── test-format.ts         # 格式化测试
├── tests/                      # 🧪 测试文件
│   ├── unit/                  # 单元测试
│   ├── integration/           # 集成测试
│   └── fixtures/              # 测试数据
├── app/                        # 📱 应用代码（保留）
├── public/                     # 📁 静态资源（保留）
├── out/                        # 📦 构建输出（保留）
└── node_modules/               # 📦 依赖包（保留）
```

## 📋 执行步骤

### 步骤 1：创建新的目录结构
- [ ] 创建 `docs/` 目录
- [ ] 创建 `docs/development/` 子目录
- [ ] 创建 `docs/architecture/` 子目录
- [ ] 创建 `docs/guides/` 子目录
- [ ] 创建 `scripts/` 目录
- [ ] 创建 `tests/` 目录及子目录

### 步骤 2：移动文档文件
- [ ] `CLAUDE.md` → `docs/development/claude-guide.md`
- [ ] `doc.md` → `docs/README.md`
- [ ] `commit让AI生成内容.md` → `docs/development/commit-guidelines.md`
- [ ] `git提交规范规则.md` → `docs/development/git-conventions.md`
- [ ] `渐变颜色生成系统技术文档.md` → `docs/architecture/gradient-color-system.md`
- [ ] `自动格式化问题解决方案.md` → `docs/development/formatting-solutions.md`

### 步骤 3：移动脚本和测试文件
- [ ] `format-check.mjs` → `scripts/format-check.mjs`
- [ ] `test-format.ts` → `scripts/test-format.ts`
- [ ] `根据图片获取颜色test（成功例子）.html` → `tests/fixtures/color-extraction-test.html`

### 步骤 4：处理构建相关目录
- [ ] 检查 `build/` 目录内容
- [ ] 如果是临时构建文件，添加到 `.gitignore`
- [ ] 如果是配置文件，移动到合适位置
- [ ] 清理不必要的构建产物

### 步骤 5：更新配置文件引用
- [ ] 更新 `package.json` 中的脚本路径
- [ ] 更新 `.gitignore` 文件
- [ ] 更新 CI/CD 配置（如果有）
- [ ] 更新文档中的路径引用

### 步骤 6：创建新的 README 结构
- [ ] 更新根目录 `README.md`，简化内容
- [ ] 在 `docs/README.md` 中提供详细文档
- [ ] 添加目录结构说明
- [ ] 添加快速开始指南

## 📝 文件移动清单

### 需要移动的文件
```bash
# 文档文件
CLAUDE.md → docs/development/claude-guide.md
doc.md → docs/README.md
commit让AI生成内容.md → docs/development/commit-guidelines.md
git提交规范规则.md → docs/development/git-conventions.md
渐变颜色生成系统技术文档.md → docs/architecture/gradient-color-system.md
自动格式化问题解决方案.md → docs/development/formatting-solutions.md

# 脚本文件
format-check.mjs → scripts/format-check.mjs
test-format.ts → scripts/test-format.ts

# 测试文件
根据图片获取颜色test（成功例子）.html → tests/fixtures/color-extraction-test.html
```

### 需要保留的文件
```bash
# 项目核心文件
README.md
package.json
package-lock.json
pnpm-lock.yaml

# 配置文件
next.config.ts
tsconfig.json
eslint.config.mjs
postcss.config.mjs
next-env.d.ts
tsconfig.tsbuildinfo
.eslintignore

# 目录
app/
public/
out/
node_modules/
```

## ⚠️ 注意事项

1. **备份重要文件**：移动前先备份重要文件
2. **更新引用路径**：移动文件后要更新所有引用
3. **测试脚本功能**：确保移动后的脚本能正常运行
4. **更新文档链接**：更新文档中的内部链接

## ✅ 验收标准

- [ ] 根目录文件数量显著减少
- [ ] 文档文件有序组织在 `docs/` 目录
- [ ] 脚本文件组织在 `scripts/` 目录
- [ ] 所有引用路径正确更新
- [ ] 项目功能完全正常
- [ ] 构建和部署流程正常

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
