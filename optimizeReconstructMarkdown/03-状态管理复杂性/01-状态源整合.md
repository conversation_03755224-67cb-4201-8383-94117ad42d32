# 01-状态源整合

## 🎯 问题描述

项目中存在多个独立的状态管理源（useAppStore、useImageStore），导致状态分散、数据同步困难、调试复杂，需要整合相关状态源以简化状态管理架构。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 多个 Zustand Store 分散
**问题**：项目中有多个独立的 Zustand store，状态管理分散
**具体表现**：
- `useAppStore` - 管理应用级状态（视图尺寸、导出设置、UI状态、模态框状态）
- `useImageStore` - 管理图片相关状态（图片列表、拖拽状态、选中状态）
- 可能还有其他未发现的状态源

**问题影响**：
```typescript
// 在组件中需要导入多个 store
import { useAppStore } from '../features/viewDimensions/utils/重构/状态管理'
import { useImageStore } from '../ImageMange/imageMangeIndex'

// 状态更新时需要手动同步
const updateImageAndUI = () => {
    useImageStore.getState().setSelectedImage(imageId)
    useAppStore.getState().setModal('mediaPickerModal', false)
}
```

### 1.2 状态依赖关系复杂
**问题**：不同 store 之间存在隐式依赖关系
**具体表现**：
- 图片选择状态影响 UI 显示状态
- 设备选择状态影响图片绑定状态
- 导出设置依赖当前选中的图片和设备

### 1.3 状态同步困难
**问题**：跨 store 的状态同步需要手动处理
**具体表现**：
- 图片上传完成后需要更新多个状态
- 设备切换时需要同步图片显示状态
- 模态框状态与业务状态不同步

### 1.4 调试和开发体验差
**问题**：多个状态源导致调试困难
**具体表现**：
- Redux DevTools 中状态分散
- 状态变化追踪困难
- 时间旅行调试不完整

## 🎯 解决方案

### 方案 1：合并相关 Store

#### 1.1 创建统一的应用状态结构
```typescript
// app/shared/store/app-store.ts
interface UnifiedAppState {
    // UI 相关状态
    ui: {
        isMobile: boolean
        activeTab: string
        sidebarOpen: boolean
    }
    
    // 模态框状态
    modals: {
        mediaPickerModal: boolean
        exportModal: boolean
        colorPickerModal: boolean
        // 其他模态框...
    }
    
    // 视图和画布状态
    canvas: {
        dimensions: {
            width: number
            height: number
            scale: number
        }
        viewMode: 'preview' | 'edit'
        activeLayout: {
            type: LayoutType
            id: number
        }
    }
    
    // 图片管理状态
    images: {
        items: ImageItem[]
        selectedImageId: string | null
        dragState: DragState
        uploadProgress: Record<string, number>
    }
    
    // 设备和绑定状态
    devices: {
        selectedDeviceIndex: number | null
        deviceImageBindings: Record<number, string> // deviceIndex -> imageId
    }
    
    // 导出设置
    export: {
        format: ImageExportFormat
        quality: ExportQuality
        scale: number
    }
}
```

#### 1.2 实现状态分片（State Slices）
```typescript
// app/shared/store/slices/ui-slice.ts
export interface UISlice {
    ui: UnifiedAppState['ui']
    setIsMobile: (isMobile: boolean) => void
    setActiveTab: (tab: string) => void
    toggleSidebar: () => void
}

export const createUISlice: StateCreator<UnifiedAppState, [], [], UISlice> = (set) => ({
    ui: {
        isMobile: false,
        activeTab: 'mockup',
        sidebarOpen: true,
    },
    
    setIsMobile: (isMobile) => set((state) => ({
        ui: { ...state.ui, isMobile }
    })),
    
    setActiveTab: (activeTab) => set((state) => ({
        ui: { ...state.ui, activeTab }
    })),
    
    toggleSidebar: () => set((state) => ({
        ui: { ...state.ui, sidebarOpen: !state.ui.sidebarOpen }
    })),
})
```

```typescript
// app/shared/store/slices/image-slice.ts
export interface ImageSlice {
    images: UnifiedAppState['images']
    addImages: (files: File[]) => Promise<void>
    removeImage: (imageId: string) => void
    selectImage: (imageId: string | null) => void
    updateDragState: (dragState: Partial<DragState>) => void
}

export const createImageSlice: StateCreator<UnifiedAppState, [], [], ImageSlice> = (set, get) => ({
    images: {
        items: [],
        selectedImageId: null,
        dragState: {
            isDragging: false,
            dragOverTarget: null,
            dropPosition: null,
        },
        uploadProgress: {},
    },
    
    addImages: async (files) => {
        // 图片添加逻辑
        const processedImages = await processFiles(files)
        set((state) => ({
            images: {
                ...state.images,
                items: [...state.images.items, ...processedImages]
            }
        }))
    },
    
    removeImage: (imageId) => set((state) => ({
        images: {
            ...state.images,
            items: state.images.items.filter(item => item.id !== imageId),
            selectedImageId: state.images.selectedImageId === imageId 
                ? null 
                : state.images.selectedImageId
        },
        // 同时清理设备绑定
        devices: {
            ...state.devices,
            deviceImageBindings: Object.fromEntries(
                Object.entries(state.devices.deviceImageBindings)
                    .filter(([_, id]) => id !== imageId)
            )
        }
    })),
    
    selectImage: (imageId) => set((state) => ({
        images: { ...state.images, selectedImageId: imageId }
    })),
    
    updateDragState: (dragState) => set((state) => ({
        images: {
            ...state.images,
            dragState: { ...state.images.dragState, ...dragState }
        }
    })),
})
```

#### 1.3 创建统一的 Store
```typescript
// app/shared/store/index.ts
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { createUISlice, UISlice } from './slices/ui-slice'
import { createImageSlice, ImageSlice } from './slices/image-slice'
import { createCanvasSlice, CanvasSlice } from './slices/canvas-slice'
import { createDeviceSlice, DeviceSlice } from './slices/device-slice'
import { createExportSlice, ExportSlice } from './slices/export-slice'

type AppStore = UISlice & ImageSlice & CanvasSlice & DeviceSlice & ExportSlice

export const useAppStore = create<AppStore>()(
    devtools(
        (...args) => ({
            ...createUISlice(...args),
            ...createImageSlice(...args),
            ...createCanvasSlice(...args),
            ...createDeviceSlice(...args),
            ...createExportSlice(...args),
        }),
        {
            name: 'app-store',
        }
    )
)

// 导出选择器 hooks
export const useUI = () => useAppStore((state) => state.ui)
export const useImages = () => useAppStore((state) => state.images)
export const useCanvas = () => useAppStore((state) => state.canvas)
export const useDevices = () => useAppStore((state) => state.devices)
export const useExport = () => useAppStore((state) => state.export)
```

### 方案 2：实现状态中间件

#### 2.1 状态同步中间件
```typescript
// app/shared/store/middleware/sync-middleware.ts
export const syncMiddleware = (config) => (set, get, api) => {
    const originalSet = set
    
    const syncedSet = (partial, replace) => {
        const result = originalSet(partial, replace)
        
        // 状态同步逻辑
        const state = get()
        
        // 当图片被删除时，清理相关状态
        if (typeof partial === 'function') {
            const newState = partial(state)
            if (newState.images?.items.length !== state.images?.items.length) {
                // 图片数量变化，可能需要同步其他状态
                syncRelatedStates(newState, originalSet)
            }
        }
        
        return result
    }
    
    return config(syncedSet, get, api)
}

const syncRelatedStates = (state, set) => {
    // 实现状态同步逻辑
    // 例如：当选中的图片被删除时，清除选中状态
    if (state.images.selectedImageId && 
        !state.images.items.find(item => item.id === state.images.selectedImageId)) {
        set((prevState) => ({
            ...prevState,
            images: {
                ...prevState.images,
                selectedImageId: null
            }
        }))
    }
}
```

#### 2.2 持久化中间件
```typescript
// app/shared/store/middleware/persist-middleware.ts
import { persist } from 'zustand/middleware'

export const persistConfig = {
    name: 'app-storage',
    partialize: (state: AppStore) => ({
        // 只持久化部分状态
        export: state.export,
        ui: {
            activeTab: state.ui.activeTab,
            sidebarOpen: state.ui.sidebarOpen,
        },
    }),
    version: 1,
    migrate: (persistedState: any, version: number) => {
        // 处理状态迁移
        if (version === 0) {
            // 从版本 0 迁移到版本 1
            return {
                ...persistedState,
                // 迁移逻辑
            }
        }
        return persistedState
    },
}
```

## 📋 执行步骤

### 步骤 1：分析现有状态结构
- [ ] 审计所有现有的 Zustand stores
- [ ] 分析状态之间的依赖关系
- [ ] 识别可以合并的状态模块
- [ ] 制定状态整合计划

### 步骤 2：设计新的状态架构
- [ ] 设计统一的状态结构
- [ ] 定义状态分片接口
- [ ] 设计状态同步机制
- [ ] 规划状态持久化策略

### 步骤 3：实现新的状态管理
- [ ] 创建状态分片（slices）
- [ ] 实现统一的 store
- [ ] 添加状态中间件
- [ ] 创建选择器 hooks

### 步骤 4：迁移现有组件
- [ ] 更新组件中的状态使用
- [ ] 替换旧的 store 导入
- [ ] 测试状态更新逻辑
- [ ] 验证状态同步功能

### 步骤 5：清理和优化
- [ ] 删除旧的 store 文件
- [ ] 更新类型定义
- [ ] 优化性能和内存使用
- [ ] 更新文档和注释

## ⚠️ 注意事项

1. **渐进式迁移**：不要一次性替换所有状态管理
2. **保持功能完整**：确保迁移过程中功能不受影响
3. **性能考虑**：避免过度订阅导致不必要的重渲染
4. **类型安全**：确保新的状态结构有完整的类型定义
5. **向后兼容**：考虑状态持久化的向后兼容性

## ✅ 验收标准

- [ ] 成功合并相关的状态源
- [ ] 状态同步机制正常工作
- [ ] 所有组件功能正常
- [ ] Redux DevTools 调试体验改善
- [ ] 状态管理代码减少至少 20%
- [ ] 性能没有明显下降

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
