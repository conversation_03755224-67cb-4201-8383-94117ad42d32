# 01-边界情况处理

## 🎯 问题描述

项目中缺乏完善的边界情况处理机制，包括网络错误、文件处理失败、用户输入异常等场景的处理不足，导致应用在异常情况下可能崩溃或表现异常。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 文件处理边界情况
**问题**：文件上传和处理过程中的异常情况处理不足
**具体表现**：
- 文件大小超限时处理不当
- 不支持的文件格式处理缺失
- 文件损坏或读取失败时无处理
- 网络中断导致上传失败无重试机制

**问题示例**：
```typescript
// 当前的文件处理缺少边界情况处理
const handleFileUpload = async (files: File[]) => {
    // ❌ 没有检查文件是否为空
    // ❌ 没有检查文件大小
    // ❌ 没有检查文件类型
    // ❌ 没有错误处理
    const results = await Promise.all(
        files.map(file => processFile(file))
    )
    setImages(results)
}
```

### 1.2 网络请求边界情况
**问题**：网络请求失败、超时等情况处理不完善
**具体表现**：
- 网络超时无处理机制
- 服务器错误响应处理不当
- 并发请求限制缺失
- 请求重试机制不完善

### 1.3 用户输入边界情况
**问题**：用户输入验证和异常处理不足
**具体表现**：
- 空值输入处理不当
- 特殊字符输入未验证
- 数值范围检查缺失
- 输入格式验证不完整

### 1.4 状态管理边界情况
**问题**：状态更新和管理中的异常情况处理不足
**具体表现**：
- 状态更新失败时无回滚机制
- 并发状态更新冲突处理缺失
- 状态持久化失败处理不当
- 状态同步异常处理不足

### 1.5 UI 渲染边界情况
**问题**：组件渲染过程中的异常情况处理不足
**具体表现**：
- 数据为空时的显示处理
- 加载状态处理不完整
- 组件卸载时的清理不足
- 异步操作取消机制缺失

## 🎯 解决方案

### 方案 1：文件处理边界情况处理

#### 1.1 完善的文件验证和处理
```typescript
// app/shared/utils/file-handler.ts
export interface FileValidationOptions {
    maxSize: number
    allowedTypes: string[]
    maxFiles: number
    minDimensions?: { width: number; height: number }
    maxDimensions?: { width: number; height: number }
}

export interface FileProcessingResult {
    success: boolean
    file?: File
    preview?: string
    error?: FileProcessingError
    warnings?: string[]
}

export enum FileProcessingErrorType {
    INVALID_TYPE = 'INVALID_TYPE',
    FILE_TOO_LARGE = 'FILE_TOO_LARGE',
    FILE_CORRUPTED = 'FILE_CORRUPTED',
    PROCESSING_FAILED = 'PROCESSING_FAILED',
    NETWORK_ERROR = 'NETWORK_ERROR',
    QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

export class FileProcessingError extends Error {
    constructor(
        public type: FileProcessingErrorType,
        message: string,
        public file?: File,
        public details?: Record<string, unknown>
    ) {
        super(message)
        this.name = 'FileProcessingError'
    }
}

export const validateAndProcessFiles = async (
    files: File[],
    options: FileValidationOptions
): Promise<FileProcessingResult[]> => {
    // 边界情况检查
    if (!files || files.length === 0) {
        return []
    }

    if (files.length > options.maxFiles) {
        throw new FileProcessingError(
            FileProcessingErrorType.QUOTA_EXCEEDED,
            `最多只能上传 ${options.maxFiles} 个文件，当前选择了 ${files.length} 个文件`
        )
    }

    const results: FileProcessingResult[] = []

    for (const file of files) {
        try {
            // 基础验证
            const validationResult = await validateFile(file, options)
            if (!validationResult.isValid) {
                results.push({
                    success: false,
                    file,
                    error: new FileProcessingError(
                        FileProcessingErrorType.INVALID_TYPE,
                        validationResult.error || '文件验证失败'
                    )
                })
                continue
            }

            // 文件处理
            const processedResult = await processFile(file, options)
            results.push(processedResult)

        } catch (error) {
            results.push({
                success: false,
                file,
                error: error instanceof FileProcessingError 
                    ? error 
                    : new FileProcessingError(
                        FileProcessingErrorType.PROCESSING_FAILED,
                        `处理文件 ${file.name} 时发生错误: ${error.message}`,
                        file
                    )
            })
        }
    }

    return results
}

const validateFile = async (
    file: File, 
    options: FileValidationOptions
): Promise<{ isValid: boolean; error?: string; warnings?: string[] }> => {
    const warnings: string[] = []

    // 检查文件是否存在
    if (!file) {
        return { isValid: false, error: '文件不存在' }
    }

    // 检查文件大小
    if (file.size === 0) {
        return { isValid: false, error: '文件为空' }
    }

    if (file.size > options.maxSize) {
        return { 
            isValid: false, 
            error: `文件大小 ${formatFileSize(file.size)} 超过限制 ${formatFileSize(options.maxSize)}` 
        }
    }

    // 检查文件类型
    if (!options.allowedTypes.includes(file.type)) {
        return { 
            isValid: false, 
            error: `不支持的文件类型 ${file.type}，支持的类型：${options.allowedTypes.join(', ')}` 
        }
    }

    // 检查文件名
    if (file.name.length > 255) {
        warnings.push('文件名过长，可能在某些系统中出现问题')
    }

    // 对于图片文件，检查尺寸
    if (file.type.startsWith('image/')) {
        try {
            const dimensions = await getImageDimensions(file)
            
            if (options.minDimensions) {
                if (dimensions.width < options.minDimensions.width || 
                    dimensions.height < options.minDimensions.height) {
                    return {
                        isValid: false,
                        error: `图片尺寸 ${dimensions.width}x${dimensions.height} 小于最小要求 ${options.minDimensions.width}x${options.minDimensions.height}`
                    }
                }
            }

            if (options.maxDimensions) {
                if (dimensions.width > options.maxDimensions.width || 
                    dimensions.height > options.maxDimensions.height) {
                    warnings.push(`图片尺寸 ${dimensions.width}x${dimensions.height} 超过建议尺寸 ${options.maxDimensions.width}x${options.maxDimensions.height}`)
                }
            }
        } catch (error) {
            return { isValid: false, error: '无法读取图片信息，文件可能已损坏' }
        }
    }

    return { isValid: true, warnings }
}

const processFile = async (
    file: File, 
    options: FileValidationOptions
): Promise<FileProcessingResult> => {
    try {
        // 创建预览
        const preview = await createFilePreview(file)
        
        return {
            success: true,
            file,
            preview
        }
    } catch (error) {
        throw new FileProcessingError(
            FileProcessingErrorType.PROCESSING_FAILED,
            `创建文件预览失败: ${error.message}`,
            file
        )
    }
}

const createFilePreview = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        // 设置超时
        const timeout = setTimeout(() => {
            reader.abort()
            reject(new Error('文件读取超时'))
        }, 10000) // 10秒超时

        const reader = new FileReader()
        
        reader.onload = (e) => {
            clearTimeout(timeout)
            const result = e.target?.result
            if (typeof result === 'string') {
                resolve(result)
            } else {
                reject(new Error('文件读取结果格式错误'))
            }
        }
        
        reader.onerror = () => {
            clearTimeout(timeout)
            reject(new Error('文件读取失败，文件可能已损坏'))
        }
        
        reader.onabort = () => {
            clearTimeout(timeout)
            reject(new Error('文件读取被中断'))
        }
        
        try {
            reader.readAsDataURL(file)
        } catch (error) {
            clearTimeout(timeout)
            reject(new Error(`无法读取文件: ${error.message}`))
        }
    })
}

const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
        const img = new Image()
        const url = URL.createObjectURL(file)
        
        const cleanup = () => {
            URL.revokeObjectURL(url)
        }
        
        img.onload = () => {
            cleanup()
            resolve({
                width: img.naturalWidth,
                height: img.naturalHeight
            })
        }
        
        img.onerror = () => {
            cleanup()
            reject(new Error('无法加载图片'))
        }
        
        // 设置超时
        setTimeout(() => {
            cleanup()
            reject(new Error('图片加载超时'))
        }, 5000)
        
        img.src = url
    })
}

const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
```

### 方案 2：网络请求边界情况处理

#### 2.1 带重试机制的网络请求处理
```typescript
// app/shared/utils/network-handler.ts
export interface NetworkRequestOptions {
    timeout: number
    retries: number
    retryDelay: number
    retryCondition?: (error: Error) => boolean
}

export class NetworkError extends Error {
    constructor(
        message: string,
        public status?: number,
        public code?: string,
        public isRetryable: boolean = false
    ) {
        super(message)
        this.name = 'NetworkError'
    }
}

export const withRetry = async <T>(
    operation: () => Promise<T>,
    options: NetworkRequestOptions
): Promise<T> => {
    let lastError: Error
    
    for (let attempt = 0; attempt <= options.retries; attempt++) {
        try {
            // 设置超时
            const timeoutPromise = new Promise<never>((_, reject) => {
                setTimeout(() => {
                    reject(new NetworkError('请求超时', 0, 'TIMEOUT', true))
                }, options.timeout)
            })
            
            const result = await Promise.race([
                operation(),
                timeoutPromise
            ])
            
            return result
        } catch (error) {
            lastError = error
            
            // 检查是否应该重试
            const shouldRetry = attempt < options.retries && 
                (options.retryCondition ? options.retryCondition(error) : isRetryableError(error))
            
            if (!shouldRetry) {
                throw error
            }
            
            // 等待后重试
            await delay(options.retryDelay * Math.pow(2, attempt)) // 指数退避
        }
    }
    
    throw lastError
}

const isRetryableError = (error: Error): boolean => {
    if (error instanceof NetworkError) {
        return error.isRetryable
    }
    
    // 网络错误通常可以重试
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return true
    }
    
    return false
}

const delay = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
}
```

### 方案 3：React 错误边界处理

#### 3.1 全局错误边界组件
```typescript
// app/components/ErrorBoundary.tsx
interface ErrorBoundaryState {
    hasError: boolean
    error?: Error
    errorInfo?: ErrorInfo
}

interface ErrorBoundaryProps {
    children: ReactNode
    fallback?: ComponentType<{ error: Error; retry: () => void }>
    onError?: (error: Error, errorInfo: ErrorInfo) => void
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props)
        this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return {
            hasError: true,
            error
        }
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo)
        
        // 调用错误处理回调
        this.props.onError?.(error, errorInfo)
        
        // 上报错误到监控系统
        this.reportError(error, errorInfo)
        
        this.setState({
            error,
            errorInfo
        })
    }

    private reportError = (error: Error, errorInfo: ErrorInfo) => {
        // 上报错误到监控系统
        try {
            // 这里可以集成 Sentry、LogRocket 等错误监控服务
            console.error('Error reported:', {
                message: error.message,
                stack: error.stack,
                componentStack: errorInfo.componentStack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            })
        } catch (reportingError) {
            console.error('Failed to report error:', reportingError)
        }
    }

    private handleRetry = () => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined })
    }

    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback || DefaultErrorFallback
            return (
                <FallbackComponent 
                    error={this.state.error!} 
                    retry={this.handleRetry}
                />
            )
        }

        return this.props.children
    }
}

// 默认错误回退组件
const DefaultErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ 
    error, 
    retry 
}) => {
    return (
        <div className="error-boundary-fallback">
            <h2>出现了一些问题</h2>
            <p>应用遇到了意外错误，请尝试刷新页面。</p>
            <details>
                <summary>错误详情</summary>
                <pre>{error.message}</pre>
            </details>
            <button onClick={retry}>重试</button>
            <button onClick={() => window.location.reload()}>刷新页面</button>
        </div>
    )
}
```

## 📋 执行步骤

### 步骤 1：边界情况识别和分析
- [ ] 识别所有可能的边界情况
- [ ] 分析当前处理机制的不足
- [ ] 制定边界情况处理优先级
- [ ] 设计错误处理策略

### 步骤 2：实现文件处理边界情况
- [ ] 完善文件验证机制
- [ ] 添加文件处理错误处理
- [ ] 实现文件上传重试机制
- [ ] 添加文件处理进度反馈

### 步骤 3：实现网络请求边界情况
- [ ] 添加网络请求超时处理
- [ ] 实现请求重试机制
- [ ] 添加网络状态检测
- [ ] 实现离线模式处理

### 步骤 4：实现 UI 边界情况处理
- [ ] 添加 React 错误边界
- [ ] 实现加载状态处理
- [ ] 添加空状态处理
- [ ] 实现异步操作取消

### 步骤 5：测试和验证
- [ ] 编写边界情况测试用例
- [ ] 进行异常情况模拟测试
- [ ] 验证错误恢复机制
- [ ] 测试用户体验改善

## ✅ 验收标准

- [ ] 所有边界情况都有相应的处理机制
- [ ] 错误信息对用户友好且有指导性
- [ ] 应用在异常情况下不会崩溃
- [ ] 错误恢复机制正常工作
- [ ] 用户体验在异常情况下仍然良好
- [ ] 错误监控和上报机制完善

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
