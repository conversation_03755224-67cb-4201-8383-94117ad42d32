# 01-单元测试添加

## 🎯 问题描述

项目中完全缺乏测试覆盖，没有单元测试、集成测试或端到端测试，这导致代码质量难以保证，重构风险高，bug 容易引入且难以发现。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 完全缺乏测试文件
**问题**：项目中没有发现任何测试文件
**具体表现**：
- 没有 `*.test.ts` 或 `*.spec.ts` 文件
- 没有测试配置文件
- 没有测试运行脚本
- 没有测试覆盖率报告

### 1.2 核心功能未测试
**问题**：关键业务逻辑和工具函数缺乏测试
**具体表现**：
- 图片处理逻辑未测试
- 颜色提取算法未测试
- 尺寸计算函数未测试
- 状态管理逻辑未测试
- 数据验证函数未测试

### 1.3 组件行为未测试
**问题**：React 组件的行为和交互未测试
**具体表现**：
- 组件渲染结果未验证
- 用户交互行为未测试
- 组件状态变化未测试
- Props 传递未验证

### 1.4 错误处理未测试
**问题**：异常情况和错误处理逻辑未测试
**具体表现**：
- 边界情况处理未验证
- 错误恢复机制未测试
- 异常抛出未验证
- 错误信息准确性未检查

## 🎯 解决方案

### 方案 1：建立测试基础设施

#### 1.1 配置测试环境
```json
// package.json - 添加测试相关依赖和脚本
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  },
  "devDependencies": {
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.1.0",
    "@testing-library/user-event": "^14.5.0",
    "@types/jest": "^29.5.0",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^29.7.0",
    "ts-jest": "^29.1.0"
  }
}
```

```javascript
// jest.config.js
module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'jsdom',
    setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
    moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/app/$1',
        '^@/components/(.*)$': '<rootDir>/app/components/$1',
        '^@/shared/(.*)$': '<rootDir>/app/shared/$1',
        '^@/features/(.*)$': '<rootDir>/app/features/$1',
    },
    collectCoverageFrom: [
        'app/**/*.{ts,tsx}',
        '!app/**/*.d.ts',
        '!app/**/index.ts',
        '!app/**/*.stories.{ts,tsx}',
    ],
    coverageThreshold: {
        global: {
            branches: 80,
            functions: 80,
            lines: 80,
            statements: 80,
        },
    },
    testMatch: [
        '<rootDir>/tests/**/*.test.{ts,tsx}',
        '<rootDir>/app/**/__tests__/**/*.{ts,tsx}',
        '<rootDir>/app/**/*.{test,spec}.{ts,tsx}',
    ],
}
```

```typescript
// tests/setup.ts
import '@testing-library/jest-dom'

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
}

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url')
global.URL.revokeObjectURL = jest.fn()

// Mock FileReader
global.FileReader = class FileReader {
    result: string | ArrayBuffer | null = null
    error: DOMException | null = null
    readyState: number = 0
    
    onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
    onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
    
    readAsDataURL(file: Blob): void {
        setTimeout(() => {
            this.result = `data:${file.type};base64,mock-base64-data`
            this.readyState = 2
            this.onload?.(new ProgressEvent('load'))
        }, 0)
    }
    
    abort(): void {}
}
```

### 方案 2：工具函数单元测试

#### 2.1 图片处理工具测试
```typescript
// tests/shared/utils/image-utils.test.ts
import { 
    validateImage, 
    createImagePreview, 
    formatFileSize,
    calculateAspectRatio 
} from '@/shared/utils/image-utils'

describe('image-utils', () => {
    describe('validateImage', () => {
        const createMockFile = (
            name: string, 
            size: number, 
            type: string
        ): File => {
            return new File(['mock content'], name, { type, lastModified: Date.now() })
        }

        it('should validate a valid image file', () => {
            const file = createMockFile('test.jpg', 1024 * 1024, 'image/jpeg')
            const result = validateImage(file)
            
            expect(result.valid).toBe(true)
            expect(result.error).toBeUndefined()
        })

        it('should reject file that is too large', () => {
            const file = createMockFile('large.jpg', 20 * 1024 * 1024, 'image/jpeg')
            const result = validateImage(file, { maxSize: 10 * 1024 * 1024 })
            
            expect(result.valid).toBe(false)
            expect(result.error).toContain('文件大小')
            expect(result.error).toContain('超过限制')
        })

        it('should reject unsupported file type', () => {
            const file = createMockFile('test.gif', 1024, 'image/gif')
            const result = validateImage(file, { 
                allowedTypes: ['image/jpeg', 'image/png'] 
            })
            
            expect(result.valid).toBe(false)
            expect(result.error).toContain('不支持的文件类型')
        })

        it('should reject empty file', () => {
            const file = createMockFile('empty.jpg', 0, 'image/jpeg')
            const result = validateImage(file)
            
            expect(result.valid).toBe(false)
            expect(result.error).toContain('文件为空')
        })
    })

    describe('createImagePreview', () => {
        it('should create preview for valid image file', async () => {
            const file = new File(['mock content'], 'test.jpg', { type: 'image/jpeg' })
            const preview = await createImagePreview(file)
            
            expect(preview).toMatch(/^data:image\/jpeg;base64,/)
        })

        it('should handle FileReader error', async () => {
            const file = new File(['mock content'], 'test.jpg', { type: 'image/jpeg' })
            
            // Mock FileReader to simulate error
            const originalFileReader = global.FileReader
            global.FileReader = class extends FileReader {
                readAsDataURL() {
                    setTimeout(() => {
                        this.onerror?.(new ProgressEvent('error'))
                    }, 0)
                }
            }
            
            await expect(createImagePreview(file)).rejects.toThrow('文件读取失败')
            
            // Restore original FileReader
            global.FileReader = originalFileReader
        })
    })

    describe('formatFileSize', () => {
        it('should format bytes correctly', () => {
            expect(formatFileSize(0)).toBe('0 Bytes')
            expect(formatFileSize(1024)).toBe('1 KB')
            expect(formatFileSize(1024 * 1024)).toBe('1 MB')
            expect(formatFileSize(1536)).toBe('1.5 KB')
        })
    })

    describe('calculateAspectRatio', () => {
        it('should calculate aspect ratio correctly', () => {
            expect(calculateAspectRatio(1920, 1080)).toEqual({ width: 16, height: 9 })
            expect(calculateAspectRatio(1024, 768)).toEqual({ width: 4, height: 3 })
            expect(calculateAspectRatio(100, 100)).toEqual({ width: 1, height: 1 })
        })

        it('should handle edge cases', () => {
            expect(calculateAspectRatio(0, 100)).toEqual({ width: 0, height: 1 })
            expect(calculateAspectRatio(100, 0)).toEqual({ width: 1, height: 0 })
        })
    })
})
```

#### 2.2 尺寸计算工具测试
```typescript
// tests/shared/utils/dimension-utils.test.ts
import { 
    calculateFitDimensions, 
    calculateScaleToFit,
    calculateAspectRatio 
} from '@/shared/utils/dimension-utils'

describe('dimension-utils', () => {
    describe('calculateFitDimensions', () => {
        const container = { width: 800, height: 600 }

        it('should fit content within container (contain mode)', () => {
            const content = { width: 1600, height: 900 }
            const result = calculateFitDimensions(container, content, 'contain')
            
            expect(result.width).toBeCloseTo(800)
            expect(result.height).toBeCloseTo(450)
        })

        it('should cover container with content (cover mode)', () => {
            const content = { width: 1600, height: 900 }
            const result = calculateFitDimensions(container, content, 'cover')
            
            expect(result.width).toBeCloseTo(1066.67, 1)
            expect(result.height).toBe(600)
        })

        it('should handle content smaller than container', () => {
            const content = { width: 400, height: 300 }
            const result = calculateFitDimensions(container, content, 'contain')
            
            expect(result.width).toBe(800)
            expect(result.height).toBe(600)
        })
    })

    describe('calculateScaleToFit', () => {
        it('should calculate correct scale factor', () => {
            const container = { width: 800, height: 600 }
            const content = { width: 1600, height: 1200 }
            
            const scale = calculateScaleToFit(container, content)
            expect(scale).toBe(0.5)
        })

        it('should return 1 for content smaller than container', () => {
            const container = { width: 800, height: 600 }
            const content = { width: 400, height: 300 }
            
            const scale = calculateScaleToFit(container, content)
            expect(scale).toBe(2)
        })
    })
})
```

### 方案 3：React 组件测试

#### 3.1 基础组件测试
```typescript
// tests/components/ui/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Button } from '@/components/ui/Button'

describe('Button', () => {
    it('should render with correct text', () => {
        render(<Button>Click me</Button>)
        expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
    })

    it('should handle click events', async () => {
        const handleClick = jest.fn()
        const user = userEvent.setup()
        
        render(<Button onClick={handleClick}>Click me</Button>)
        
        await user.click(screen.getByRole('button'))
        expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('should be disabled when disabled prop is true', () => {
        render(<Button disabled>Disabled button</Button>)
        
        const button = screen.getByRole('button')
        expect(button).toBeDisabled()
    })

    it('should show loading state', () => {
        render(<Button loading>Loading button</Button>)
        
        expect(screen.getByRole('button')).toBeDisabled()
        expect(screen.getByText('Loading button')).toBeInTheDocument()
    })

    it('should apply custom className', () => {
        render(<Button className="custom-class">Styled button</Button>)
        
        const button = screen.getByRole('button')
        expect(button).toHaveClass('custom-class')
    })
})
```

#### 3.2 复杂组件测试
```typescript
// tests/components/ImageUploader.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ImageUploader } from '@/components/ImageUploader'

// Mock the image processing hook
jest.mock('@/shared/hooks/useImageProcessor', () => ({
    useImageProcessor: () => ({
        processImages: jest.fn().mockResolvedValue([]),
        isProcessing: false,
        error: null,
        clearError: jest.fn(),
    }),
}))

describe('ImageUploader', () => {
    const mockOnUpload = jest.fn()

    beforeEach(() => {
        mockOnUpload.mockClear()
    })

    it('should render upload area', () => {
        render(<ImageUploader onUpload={mockOnUpload} />)
        
        expect(screen.getByText(/拖拽图片到此处/)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /选择文件/ })).toBeInTheDocument()
    })

    it('should handle file selection', async () => {
        const user = userEvent.setup()
        render(<ImageUploader onUpload={mockOnUpload} />)
        
        const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
        const input = screen.getByLabelText(/选择文件/)
        
        await user.upload(input, file)
        
        await waitFor(() => {
            expect(mockOnUpload).toHaveBeenCalledWith([file])
        })
    })

    it('should show error message for invalid files', async () => {
        const user = userEvent.setup()
        render(<ImageUploader onUpload={mockOnUpload} />)
        
        const file = new File(['test'], 'test.txt', { type: 'text/plain' })
        const input = screen.getByLabelText(/选择文件/)
        
        await user.upload(input, file)
        
        await waitFor(() => {
            expect(screen.getByText(/不支持的文件类型/)).toBeInTheDocument()
        })
    })

    it('should handle drag and drop', async () => {
        render(<ImageUploader onUpload={mockOnUpload} />)
        
        const dropZone = screen.getByText(/拖拽图片到此处/).closest('div')
        const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
        
        fireEvent.dragEnter(dropZone!, {
            dataTransfer: {
                files: [file],
                types: ['Files'],
            },
        })
        
        expect(dropZone).toHaveClass('drag-over')
        
        fireEvent.drop(dropZone!, {
            dataTransfer: {
                files: [file],
            },
        })
        
        await waitFor(() => {
            expect(mockOnUpload).toHaveBeenCalledWith([file])
        })
    })
})
```

### 方案 4：自定义 Hooks 测试

#### 4.1 状态管理 Hook 测试
```typescript
// tests/hooks/useImageStore.test.ts
import { renderHook, act } from '@testing-library/react'
import { useImageStore } from '@/shared/store/image-store'

describe('useImageStore', () => {
    beforeEach(() => {
        // Reset store state before each test
        useImageStore.getState().reset?.()
    })

    it('should add images to store', async () => {
        const { result } = renderHook(() => useImageStore())
        
        const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
        
        await act(async () => {
            await result.current.addImages([mockFile])
        })
        
        expect(result.current.images).toHaveLength(1)
        expect(result.current.images[0].file).toBe(mockFile)
    })

    it('should remove image from store', () => {
        const { result } = renderHook(() => useImageStore())
        
        // First add an image
        act(() => {
            result.current.setImages([{
                id: 'test-id',
                file: new File(['test'], 'test.jpg', { type: 'image/jpeg' }),
                preview: 'mock-preview',
                status: 'completed',
                aspectRatio: 1.5,
                deviceIndexes: [],
            }])
        })
        
        expect(result.current.images).toHaveLength(1)
        
        // Then remove it
        act(() => {
            result.current.removeImage('test-id')
        })
        
        expect(result.current.images).toHaveLength(0)
    })

    it('should select image', () => {
        const { result } = renderHook(() => useImageStore())
        
        act(() => {
            result.current.selectImage('test-id')
        })
        
        expect(result.current.selectedImageId).toBe('test-id')
    })
})
```

## 📋 执行步骤

### 步骤 1：建立测试基础设施
- [ ] 安装测试相关依赖
- [ ] 配置 Jest 和 Testing Library
- [ ] 创建测试配置文件
- [ ] 设置测试脚本和 CI 集成

### 步骤 2：编写工具函数测试
- [ ] 测试图片处理工具函数
- [ ] 测试尺寸计算工具函数
- [ ] 测试数据验证工具函数
- [ ] 测试颜色处理工具函数

### 步骤 3：编写组件测试
- [ ] 测试基础 UI 组件
- [ ] 测试业务组件
- [ ] 测试复杂交互组件
- [ ] 测试错误边界组件

### 步骤 4：编写 Hooks 测试
- [ ] 测试自定义状态管理 hooks
- [ ] 测试业务逻辑 hooks
- [ ] 测试副作用 hooks
- [ ] 测试性能优化 hooks

### 步骤 5：建立测试流程
- [ ] 设置测试覆盖率目标
- [ ] 配置 CI/CD 测试流程
- [ ] 建立测试编写规范
- [ ] 设置测试报告和监控

## ✅ 验收标准

- [ ] 测试覆盖率达到 80% 以上
- [ ] 所有核心功能都有测试覆盖
- [ ] 测试能够在 CI/CD 中自动运行
- [ ] 测试运行时间控制在合理范围内
- [ ] 测试代码质量良好，易于维护
- [ ] 建立了完整的测试编写规范

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
