# 03-逻辑重复问题

## 🎯 问题描述

项目中存在大量重复的业务逻辑代码，包括图片处理、尺寸计算、数据验证等核心功能在多个组件中重复实现，增加了维护成本和出错风险。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 3.1 图片处理逻辑重复
**问题**：图片上传、预览、处理逻辑在多个组件中重复
**具体表现**：
- 文件验证逻辑重复（类型、大小检查）
- 图片预览生成逻辑重复
- 图片压缩和处理逻辑重复
- 错误处理逻辑重复

**重复代码示例**：
```typescript
// 在多个组件中重复出现的图片验证逻辑
const validateImage = (file: File) => {
    if (!file.type.startsWith('image/')) {
        return { valid: false, error: '请选择图片文件' }
    }
    if (file.size > 10 * 1024 * 1024) {
        return { valid: false, error: '图片大小不能超过10MB' }
    }
    return { valid: true }
}
```

### 3.2 尺寸计算逻辑重复
**问题**：画布尺寸、设备尺寸计算逻辑在多处重复
**具体表现**：
- 响应式尺寸计算重复
- 设备比例计算重复
- 画布适配逻辑重复
- 缩放比例计算重复

**重复代码示例**：
```typescript
// 在多个组件中重复的尺寸计算
const calculateDimensions = (containerWidth: number, containerHeight: number) => {
    const aspectRatio = 16 / 9
    let width = containerWidth
    let height = width / aspectRatio
    
    if (height > containerHeight) {
        height = containerHeight
        width = height * aspectRatio
    }
    
    return { width, height }
}
```

### 3.3 数据验证逻辑重复
**问题**：表单验证、数据格式检查逻辑重复
**具体表现**：
- 颜色值验证重复
- 数值范围验证重复
- 必填字段验证重复
- 格式验证重复

### 3.4 状态管理逻辑重复
**问题**：相似的状态更新和管理逻辑重复
**具体表现**：
- 加载状态管理重复
- 错误状态处理重复
- 数据获取逻辑重复
- 缓存逻辑重复

### 3.5 事件处理逻辑重复
**问题**：相似的用户交互处理逻辑重复
**具体表现**：
- 拖拽处理逻辑重复
- 键盘事件处理重复
- 鼠标事件处理重复
- 触摸事件处理重复

## 🎯 解决方案

### 方案 1：创建通用工具函数库

#### 1.1 图片处理工具
```typescript
// app/shared/utils/image-utils.ts
export interface ImageValidationOptions {
    maxSize?: number
    allowedTypes?: string[]
    minWidth?: number
    minHeight?: number
}

export interface ImageValidationResult {
    valid: boolean
    error?: string
    warnings?: string[]
}

export const validateImage = (
    file: File, 
    options: ImageValidationOptions = {}
): ImageValidationResult => {
    const {
        maxSize = 10 * 1024 * 1024, // 10MB
        allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
        minWidth = 0,
        minHeight = 0
    } = options

    // 类型验证
    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            error: `不支持的文件类型。支持的类型：${allowedTypes.join(', ')}`
        }
    }

    // 大小验证
    if (file.size > maxSize) {
        return {
            valid: false,
            error: `文件大小超过限制。最大允许：${formatFileSize(maxSize)}`
        }
    }

    return { valid: true }
}

export const createImagePreview = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as string)
        reader.onerror = reject
        reader.readAsDataURL(file)
    })
}

export const compressImage = async (
    file: File, 
    quality: number = 0.8,
    maxWidth: number = 1920,
    maxHeight: number = 1080
): Promise<File> => {
    // 图片压缩逻辑
    // 使用 Canvas API 或第三方库实现
}

export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
```

#### 1.2 尺寸计算工具
```typescript
// app/shared/utils/dimension-utils.ts
export interface Dimensions {
    width: number
    height: number
}

export interface AspectRatio {
    width: number
    height: number
}

export const calculateFitDimensions = (
    container: Dimensions,
    content: Dimensions,
    mode: 'contain' | 'cover' = 'contain'
): Dimensions => {
    const containerRatio = container.width / container.height
    const contentRatio = content.width / content.height

    let width: number
    let height: number

    if (mode === 'contain') {
        if (containerRatio > contentRatio) {
            height = container.height
            width = height * contentRatio
        } else {
            width = container.width
            height = width / contentRatio
        }
    } else { // cover
        if (containerRatio > contentRatio) {
            width = container.width
            height = width / contentRatio
        } else {
            height = container.height
            width = height * contentRatio
        }
    }

    return { width, height }
}

export const calculateScaleToFit = (
    container: Dimensions,
    content: Dimensions
): number => {
    const scaleX = container.width / content.width
    const scaleY = container.height / content.height
    return Math.min(scaleX, scaleY)
}

export const calculateAspectRatio = (width: number, height: number): AspectRatio => {
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
    const divisor = gcd(width, height)
    return {
        width: width / divisor,
        height: height / divisor
    }
}
```

#### 1.3 数据验证工具
```typescript
// app/shared/utils/validation-utils.ts
export interface ValidationRule<T> {
    validate: (value: T) => boolean
    message: string
}

export interface ValidationResult {
    valid: boolean
    errors: string[]
}

export const createValidator = <T>(...rules: ValidationRule<T>[]) => {
    return (value: T): ValidationResult => {
        const errors: string[] = []
        
        for (const rule of rules) {
            if (!rule.validate(value)) {
                errors.push(rule.message)
            }
        }
        
        return {
            valid: errors.length === 0,
            errors
        }
    }
}

// 常用验证规则
export const ValidationRules = {
    required: <T>(message = '此字段为必填项'): ValidationRule<T> => ({
        validate: (value) => value != null && value !== '',
        message
    }),

    minLength: (min: number, message?: string): ValidationRule<string> => ({
        validate: (value) => value.length >= min,
        message: message || `最少需要 ${min} 个字符`
    }),

    maxLength: (max: number, message?: string): ValidationRule<string> => ({
        validate: (value) => value.length <= max,
        message: message || `最多允许 ${max} 个字符`
    }),

    range: (min: number, max: number, message?: string): ValidationRule<number> => ({
        validate: (value) => value >= min && value <= max,
        message: message || `值必须在 ${min} 到 ${max} 之间`
    }),

    hexColor: (message = '请输入有效的十六进制颜色值'): ValidationRule<string> => ({
        validate: (value) => /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value),
        message
    }),

    email: (message = '请输入有效的邮箱地址'): ValidationRule<string> => ({
        validate: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
        message
    })
}
```

### 方案 2：创建自定义 Hooks

#### 2.1 图片处理 Hook
```typescript
// app/shared/hooks/useImageProcessor.ts
export const useImageProcessor = () => {
    const [isProcessing, setIsProcessing] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const processImages = useCallback(async (
        files: File[],
        options: ImageValidationOptions = {}
    ) => {
        setIsProcessing(true)
        setError(null)

        try {
            const results = await Promise.all(
                files.map(async (file) => {
                    const validation = validateImage(file, options)
                    if (!validation.valid) {
                        throw new Error(validation.error)
                    }

                    const preview = await createImagePreview(file)
                    return {
                        file,
                        preview,
                        id: generateId()
                    }
                })
            )

            return results
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '处理图片时发生错误'
            setError(errorMessage)
            throw err
        } finally {
            setIsProcessing(false)
        }
    }, [])

    return {
        processImages,
        isProcessing,
        error,
        clearError: () => setError(null)
    }
}
```

#### 2.2 尺寸计算 Hook
```typescript
// app/shared/hooks/useDimensions.ts
export const useDimensions = (
    containerRef: RefObject<HTMLElement>,
    contentDimensions?: Dimensions
) => {
    const [dimensions, setDimensions] = useState<Dimensions>({ width: 0, height: 0 })
    const [scale, setScale] = useState(1)

    useEffect(() => {
        const updateDimensions = () => {
            if (!containerRef.current) return

            const container = {
                width: containerRef.current.clientWidth,
                height: containerRef.current.clientHeight
            }

            if (contentDimensions) {
                const fitted = calculateFitDimensions(container, contentDimensions)
                const calculatedScale = calculateScaleToFit(container, contentDimensions)
                
                setDimensions(fitted)
                setScale(calculatedScale)
            } else {
                setDimensions(container)
            }
        }

        updateDimensions()

        const resizeObserver = new ResizeObserver(updateDimensions)
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current)
        }

        return () => resizeObserver.disconnect()
    }, [containerRef, contentDimensions])

    return { dimensions, scale }
}
```

## 📋 执行步骤

### 步骤 1：识别重复逻辑
- [ ] 扫描所有组件文件
- [ ] 识别重复的业务逻辑模式
- [ ] 分析逻辑的相似度和差异
- [ ] 制定抽取优先级

### 步骤 2：创建工具函数库
- [ ] 创建 `app/shared/utils/` 目录结构
- [ ] 实现图片处理工具函数
- [ ] 实现尺寸计算工具函数
- [ ] 实现数据验证工具函数
- [ ] 实现通用业务逻辑函数

### 步骤 3：创建自定义 Hooks
- [ ] 创建 `app/shared/hooks/` 目录
- [ ] 实现图片处理相关 hooks
- [ ] 实现尺寸计算相关 hooks
- [ ] 实现数据管理相关 hooks
- [ ] 实现事件处理相关 hooks

### 步骤 4：重构现有组件
- [ ] 替换重复的图片处理逻辑
- [ ] 替换重复的尺寸计算逻辑
- [ ] 替换重复的验证逻辑
- [ ] 替换重复的状态管理逻辑

### 步骤 5：测试和验证
- [ ] 为工具函数编写单元测试
- [ ] 为自定义 hooks 编写测试
- [ ] 验证重构后的组件功能
- [ ] 性能测试和优化

## ✅ 验收标准

- [ ] 重复业务逻辑代码减少至少 40%
- [ ] 创建了完整的工具函数库
- [ ] 实现了可复用的自定义 hooks
- [ ] 所有原有功能正常工作
- [ ] 代码可维护性显著提升
- [ ] 单元测试覆盖率达到 80% 以上

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
