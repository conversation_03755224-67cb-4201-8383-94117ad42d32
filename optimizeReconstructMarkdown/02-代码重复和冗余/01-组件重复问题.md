# 01-组件重复问题

## 🎯 问题描述

项目中存在大量重复的组件代码，特别是PC端和移动端的相似组件，以及功能相近的组件之间存在代码重复，增加了维护成本和出错风险。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 PC端和移动端组件重复
**问题**：PC端和移动端有相似功能但代码重复的组件
**具体表现**：
- `PcMockup_Media.tsx` vs 移动端媒体组件
- `PcFrame_*` 系列 vs `MobileFrame_*` 系列
- `PcRightSlider.tsx` vs 移动端滑块组件

**重复代码示例**：
```typescript
// PcMockup_Media.tsx 中的逻辑
const handleImageClick = () => {
    // 图片点击处理逻辑
}

// 移动端组件中的相似逻辑
const handleImageClick = () => {
    // 几乎相同的图片点击处理逻辑
}
```

### 1.2 模态框组件重复
**问题**：多个模态框组件有相似的结构和逻辑
**具体表现**：
- `PcFrame_Effects__ModalDefault.tsx`
- `PcFrame_Effects__ModalPortrait.tsx`
- `PcFrame_Effects__ModalVef.tsx`
- `Public_FrameSizeModal.tsx`
- `Public_MockupModal.tsx`

**重复模式**：
- 相似的打开/关闭逻辑
- 相似的背景遮罩处理
- 相似的动画效果
- 相似的键盘事件处理

### 1.3 布局组件重复
**问题**：布局相关组件存在重复的响应式逻辑
**具体表现**：
- `MobileMockup_Layout.tsx`
- `MobileMockup_Tabs.tsx`
- `Public_MockupSmallLayout.tsx`

**重复逻辑**：
- 响应式断点处理
- 布局切换逻辑
- 尺寸计算方法

### 1.4 表单和输入组件重复
**问题**：表单处理和输入组件有重复的验证和处理逻辑
**具体表现**：
- 颜色选择器相关组件
- 文件上传处理逻辑
- 表单验证逻辑

## 🎯 解决方案

### 方案 1：创建通用基础组件

#### 1.1 通用模态框组件
```typescript
// app/components/ui/Modal.tsx
interface ModalProps {
    isOpen: boolean
    onClose: () => void
    title?: string
    size?: 'sm' | 'md' | 'lg' | 'xl'
    children: React.ReactNode
    showCloseButton?: boolean
    closeOnOverlayClick?: boolean
    closeOnEscape?: boolean
}

export const Modal: React.FC<ModalProps> = ({
    isOpen,
    onClose,
    title,
    size = 'md',
    children,
    showCloseButton = true,
    closeOnOverlayClick = true,
    closeOnEscape = true,
}) => {
    // 通用模态框逻辑
}
```

#### 1.2 响应式容器组件
```typescript
// app/components/layout/ResponsiveContainer.tsx
interface ResponsiveContainerProps {
    children: React.ReactNode
    breakpoint?: number
    mobileComponent?: React.ComponentType
    desktopComponent?: React.ComponentType
    className?: string
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
    children,
    breakpoint = 800,
    mobileComponent: MobileComponent,
    desktopComponent: DesktopComponent,
    className
}) => {
    const isMobile = useMediaQuery(`(max-width: ${breakpoint}px)`)
    
    if (MobileComponent && DesktopComponent) {
        return isMobile ? <MobileComponent /> : <DesktopComponent />
    }
    
    return <div className={className}>{children}</div>
}
```

### 方案 2：抽取共享逻辑到自定义 Hooks

#### 2.1 模态框逻辑 Hook
```typescript
// app/shared/hooks/useModal.ts
export const useModal = (initialState = false) => {
    const [isOpen, setIsOpen] = useState(initialState)
    
    const open = useCallback(() => setIsOpen(true), [])
    const close = useCallback(() => setIsOpen(false), [])
    const toggle = useCallback(() => setIsOpen(prev => !prev), [])
    
    // 键盘事件处理
    useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isOpen) {
                close()
            }
        }
        
        document.addEventListener('keydown', handleEscape)
        return () => document.removeEventListener('keydown', handleEscape)
    }, [isOpen, close])
    
    return { isOpen, open, close, toggle }
}
```

#### 2.2 图片处理逻辑 Hook
```typescript
// app/shared/hooks/useImageHandler.ts
export const useImageHandler = () => {
    const handleImageClick = useCallback((imageId: string, deviceIndex?: number) => {
        // 统一的图片点击处理逻辑
    }, [])
    
    const handleImageUpload = useCallback((files: File[]) => {
        // 统一的图片上传处理逻辑
    }, [])
    
    const handleImageDelete = useCallback((imageId: string) => {
        // 统一的图片删除处理逻辑
    }, [])
    
    return {
        handleImageClick,
        handleImageUpload,
        handleImageDelete
    }
}
```

### 方案 3：组件组合模式

#### 3.1 效果模态框组合
```typescript
// app/components/business/EffectsModal.tsx
interface EffectsModalProps {
    type: 'default' | 'portrait' | 'vef'
    isOpen: boolean
    onClose: () => void
}

export const EffectsModal: React.FC<EffectsModalProps> = ({ type, isOpen, onClose }) => {
    const renderContent = () => {
        switch (type) {
            case 'default':
                return <DefaultEffectsContent />
            case 'portrait':
                return <PortraitEffectsContent />
            case 'vef':
                return <VefEffectsContent />
            default:
                return null
        }
    }
    
    return (
        <Modal isOpen={isOpen} onClose={onClose} title={`${type} Effects`}>
            {renderContent()}
        </Modal>
    )
}
```

## 📋 执行步骤

### 步骤 1：分析重复代码
- [ ] 识别所有重复的组件和逻辑
- [ ] 分析重复代码的相似度和差异点
- [ ] 确定可以抽取的通用逻辑
- [ ] 制定重构优先级

### 步骤 2：创建基础组件库
- [ ] 创建 `app/components/ui/` 目录
- [ ] 实现通用 Modal 组件
- [ ] 实现通用 Button 组件
- [ ] 实现通用 Input 组件
- [ ] 实现通用 Layout 组件

### 步骤 3：抽取共享逻辑
- [ ] 创建 `app/shared/hooks/` 目录
- [ ] 实现 `useModal` hook
- [ ] 实现 `useImageHandler` hook
- [ ] 实现 `useResponsive` hook
- [ ] 实现 `useFormValidation` hook

### 步骤 4：重构现有组件
- [ ] 重构模态框组件使用通用 Modal
- [ ] 重构图片处理组件使用共享 hooks
- [ ] 重构布局组件使用通用 Layout
- [ ] 重构表单组件使用共享验证逻辑

### 步骤 5：测试和验证
- [ ] 测试重构后的组件功能
- [ ] 确保所有交互正常工作
- [ ] 检查性能是否有改善
- [ ] 验证代码复用率提升

## 🛠️ 重构工具

### 代码分析工具
```bash
# 使用 jscpd 检测重复代码
npx jscpd --min-lines 10 --min-tokens 50 app/

# 使用 cloc 统计代码行数
npx cloc app/ --exclude-dir=node_modules
```

### 重构辅助脚本
```javascript
// scripts/find-duplicates.js
// 自定义脚本查找重复的组件模式
```

## ⚠️ 注意事项

1. **保持功能一致性**：重构时确保原有功能不受影响
2. **渐进式重构**：不要一次性重构所有组件
3. **测试覆盖**：重构前后都要进行充分测试
4. **性能考虑**：确保抽象不会影响性能
5. **团队沟通**：重构方案要与团队成员讨论

## ✅ 验收标准

- [ ] 重复代码行数减少至少 30%
- [ ] 创建了可复用的基础组件库
- [ ] 实现了共享的业务逻辑 hooks
- [ ] 所有原有功能正常工作
- [ ] 代码可维护性显著提升
- [ ] 新增功能开发效率提升

## 📊 预期收益

- **代码减少**：预计减少 20-30% 的重复代码
- **维护效率**：修改一处逻辑可以影响多个组件
- **开发速度**：新功能开发可以复用现有组件
- **一致性**：UI 和交互行为更加一致
- **测试效率**：只需测试基础组件即可

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
