# 🚀 第一步：详细执行计划

## 📋 执行前准备

### 1. 创建备份分支
```bash
git checkout -b feature/file-structure-refactor
git add .
git commit -m "backup: 文件结构重构前的备份"
```

### 2. 创建新目录结构
```bash
# 创建文档目录
mkdir -p docs/development
mkdir -p docs/architecture
mkdir -p docs/guides

# 创建脚本目录
mkdir -p scripts

# 创建测试目录
mkdir -p tests/fixtures

# 创建新的 app 目录结构
mkdir -p app/components/ui
mkdir -p app/components/layout
mkdir -p app/components/business/modals
mkdir -p app/features/image-management/core
mkdir -p app/features/image-management/components
mkdir -p app/features/image-management/docs
mkdir -p app/features/color-extraction/magic-background
mkdir -p app/features/color-extraction/test
mkdir -p app/features/device-mockup
mkdir -p app/features/canvas-rendering/container
mkdir -p app/features/canvas-rendering/display
mkdir -p app/features/export-system/core
mkdir -p app/shared/types
mkdir -p app/shared/utils
mkdir -p app/shared/hooks
mkdir -p app/shared/config
mkdir -p app/styles/globals
mkdir -p app/styles/components
```

## 🗂️ 第一阶段：根目录清理（风险最低）

### 移动文档文件
```bash
# 移动文档文件
mv CLAUDE.md docs/development/claude-guide.md
mv doc.md docs/README.md
mv "commit让AI生成内容.md" docs/development/commit-guidelines.md
mv "git提交规范规则.md" docs/development/git-conventions.md
mv "渐变颜色生成系统技术文档.md" docs/architecture/gradient-color-system.md
mv "自动格式化问题解决方案.md" docs/development/formatting-solutions.md
```

### 移动脚本文件
```bash
# 移动脚本文件
mv format-check.mjs scripts/format-check.mjs
mv test-format.ts scripts/test-format.ts
```

### 移动测试文件
```bash
# 移动测试文件
mv "根据图片获取颜色test（成功例子）.html" tests/fixtures/color-extraction-test.html
```

### 更新 package.json 脚本路径
需要更新的脚本路径：
```json
{
  "scripts": {
    "format:check": "node scripts/format-check.mjs",
    "test:format": "ts-node scripts/test-format.ts"
  }
}
```

## 🎨 第二阶段：样式文件移动（风险较低）

### 移动样式文件
```bash
# 移动样式文件
mv app/css/globals.scss app/styles/globals/globals.scss
mv app/css/defaultScss app/styles/components/
rmdir app/css
```

### 更新样式导入路径
需要在以下文件中更新样式导入路径：
- `app/layout.tsx`
- 其他导入样式的组件文件

## 🔧 第三阶段：共享资源移动（风险中等）

### 移动工具函数
```bash
# 移动工具函数
mv app/utils/* app/shared/utils/
rmdir app/utils
```

### 移动类型定义
```bash
# 移动类型定义
mv app/types/* app/shared/types/
rmdir app/types
```

### 移动自定义钩子
```bash
# 移动钩子文件
mv app/hooks/* app/shared/hooks/
rmdir app/hooks
```

### 移动配置文件
```bash
# 移动配置文件
mv app/config/* app/shared/config/
rmdir app/config
```

### 创建索引文件
需要创建以下索引文件来简化导入：

#### app/shared/utils/index.ts
```typescript
export * from './browser'
export * from './imageExport'
export * from './imageLoader'
export * from './picaConfig'
export * from './viewRenderCheck'
```

#### app/shared/types/index.ts
```typescript
export * from './colorTypes'
// 其他类型文件的导出
```

#### app/shared/hooks/index.ts
```typescript
export * from './useAppState'
export * from './useBackgroundStore'
export * from './useColorGenerationFromImage'
export * from './useCustomImageStore'
export * from './useMagicBackground'
export * from './useMagicBackgroundStore'
export * from './useMockupShadowStore'
export * from './useMockupStore'
export * from './usePasteImage'
export * from './useResizeImage'
export * from './useSceneStore'
export * from './pasteUploadStore'
```

## 🧩 第四阶段：功能模块重构（风险最高）

### 移动图片管理模块
```bash
# 移动图片管理相关文件
mv app/ImageMange/imageMangeIndex.tsx app/features/image-management/core/store.ts
mv app/ImageMange/imageConfig.ts app/features/image-management/core/config.ts
mv app/ImageMange/*.md app/features/image-management/docs/
rmdir app/ImageMange
```

### 移动颜色提取模块
```bash
# 移动颜色提取相关文件
mv app/get_color/* app/features/color-extraction/
mv app/MobileFrame_Magic/* app/features/color-extraction/magic-background/
mv app/test_color/* app/features/color-extraction/test/
rmdir app/get_color
rmdir app/MobileFrame_Magic
rmdir app/test_color
```

### 移动现有组件到新位置
```bash
# 移动布局组件
mv app/MobileMockup_Layout.tsx app/components/layout/MobileLayout.tsx
mv app/MobileMockup_Tabs.tsx app/components/layout/MobileTabs.tsx
mv app/Public_MockupSmallLayout.tsx app/components/layout/SmallLayout.tsx
mv app/Public_PanelTabs.tsx app/components/layout/PanelTabs.tsx

# 移动业务组件
mv app/PcMockup_Media.tsx app/components/business/MediaSelector.tsx
mv app/PcMockup_Visibility.tsx app/components/business/VisibilityControl.tsx
mv app/PcRightSlider.tsx app/components/business/RightSlider.tsx
mv app/Public_MockupDetails.tsx app/components/business/MockupDetails.tsx
mv app/Public_MockupStyle.tsx app/components/business/MockupStyle.tsx
mv app/Public_MockupShadow.tsx app/components/business/MockupShadow.tsx

# 移动模态框组件
mv app/PcFrame_Effects__ModalDefault.tsx app/components/business/modals/EffectsModal.tsx
mv app/PcFrame_Effects__ModalPortrait.tsx app/components/business/modals/PortraitEffectsModal.tsx
mv app/PcFrame_Effects__ModalVef.tsx app/components/business/modals/VefEffectsModal.tsx
mv app/PcFrame_ShapesItemsModal.tsx app/components/business/modals/ShapesModal.tsx
mv app/PcFrame_WaterMarkModal.tsx app/components/business/modals/WaterMarkModal.tsx
mv app/PublicFrame_ColorPicker_Modal.tsx app/components/business/modals/ColorPickerModal.tsx
mv app/PublicFrame_CustomImage_Modal.tsx app/components/business/modals/CustomImageModal.tsx
mv app/Public_FrameSizeModal.tsx app/components/business/modals/FrameSizeModal.tsx
mv app/Public_MediaPickerModal.tsx app/components/business/modals/MediaPickerModal.tsx
mv app/Public_MockupModal.tsx app/components/business/modals/MockupModal.tsx

# 移动设备模拟组件
mv app/Public_MockUp_ModelSelector.tsx app/features/device-mockup/ModelSelector.tsx
mv app/Public_MockUp_SizeSelector.tsx app/features/device-mockup/SizeSelector.tsx
mv app/Public_FrameScene.tsx app/features/device-mockup/FrameScene.tsx
mv app/Public_FrameCustomView.tsx app/features/device-mockup/CustomView.tsx

# 移动画布相关组件
mv app/components/CanvasContainer/* app/features/canvas-rendering/container/
mv app/components/DisplayContainer/* app/features/canvas-rendering/display/
mv app/Public_CaptureCanvas.tsx app/features/canvas-rendering/CaptureCanvas.tsx

# 移动导出相关组件
mv app/components/ExportPopver/* app/features/export-system/popover/
```

## 🔄 第五阶段：更新导入路径

### 配置路径别名
在 `tsconfig.json` 中添加路径别名：
```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./app/*"],
      "@/components/*": ["./app/components/*"],
      "@/features/*": ["./app/features/*"],
      "@/shared/*": ["./app/shared/*"],
      "@/styles/*": ["./app/styles/*"]
    }
  }
}
```

### 批量更新导入路径
需要更新的主要文件：
1. `app/page.tsx` - 主页面组件
2. `app/layout.tsx` - 布局组件
3. 所有移动后的组件文件

### 示例导入路径更新
```typescript
// 旧的导入方式
import { useAppState } from '../hooks/useAppState'
import { imageExport } from '../utils/imageExport'
import { ColorTypes } from '../types/colorTypes'

// 新的导入方式
import { useAppState } from '@/shared/hooks'
import { imageExport } from '@/shared/utils'
import { ColorTypes } from '@/shared/types'
```

## ✅ 验证步骤

### 1. TypeScript 编译检查
```bash
npx tsc --noEmit
```

### 2. ESLint 检查
```bash
npx eslint app/ --ext .ts,.tsx
```

### 3. 应用功能测试
```bash
npm run dev
# 测试主要功能：
# - 图片上传
# - 设备选择
# - 颜色提取
# - 导出功能
```

### 4. 构建测试
```bash
npm run build
```

## 🚨 回滚计划

如果出现问题，可以通过以下方式回滚：
```bash
git checkout .
git clean -fd
git checkout main
```

## 📝 执行检查清单

- [ ] 创建备份分支
- [ ] 创建新目录结构
- [ ] 移动文档文件
- [ ] 移动脚本文件
- [ ] 移动测试文件
- [ ] 更新 package.json 脚本路径
- [ ] 移动样式文件
- [ ] 更新样式导入路径
- [ ] 移动共享资源文件
- [ ] 创建索引文件
- [ ] 移动功能模块
- [ ] 移动组件文件
- [ ] 配置路径别名
- [ ] 批量更新导入路径
- [ ] TypeScript 编译检查
- [ ] ESLint 检查
- [ ] 应用功能测试
- [ ] 构建测试
- [ ] 提交变更

## 🤔 执行前确认

请确认以下问题：
1. **是否同意这个执行计划？**
2. **是否希望我逐步执行，每个阶段都等待你的确认？**
3. **是否有特定的文件或模块需要特殊处理？**

确认后，我将开始执行第一阶段的文件移动操作！
