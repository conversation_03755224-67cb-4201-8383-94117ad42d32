# 01-巨型组件拆分

## 🎯 问题描述

项目中存在职责过多、代码量过大的巨型组件，特别是 `page.tsx` 和一些复杂的业务组件，这些组件承担了过多的责任，导致代码难以维护、测试和复用。

## 📊 当前状态：🔴 未开始

## 🔍 具体问题清单

### 1.1 page.tsx 巨型组件
**问题**：主页面组件承担过多职责，代码量庞大
**具体表现**：
- 包含大量状态管理逻辑
- 混合了 PC 端和移动端的渲染逻辑
- 包含多个模态框的管理
- 事件处理逻辑复杂
- 组件内部嵌套层级深

**代码问题示例**：
```typescript
// app/page.tsx 中的问题
export default function Home() {
    // 大量的状态定义（20+ 个状态）
    const [isActiveMediaPickerModal, setIsActiveMediaPickerModal] = useState(false)
    const [isActiveFrameModel, setIsActiveFrameModel] = useState(false)
    // ... 更多状态

    // 复杂的渲染逻辑混合
    const MainPcContent = () => {
        // 大量的 PC 端逻辑
    }

    const MainMobileContent = () => {
        // 大量的移动端逻辑
    }

    // 主组件返回大量 JSX
    return (
        <>
            <main className="...">
                {/* 复杂的条件渲染 */}
                {isMobile ? MainMobileContent() : MainPcContent()}
            </main>
            {/* 大量的模态框组件 */}
            <div id='modal_root'>
                {/* 10+ 个模态框 */}
            </div>
        </>
    )
}
```

### 1.2 复杂的显示容器组件
**问题**：DisplayContainer 等核心组件职责过多
**具体表现**：
- 同时处理布局计算和渲染
- 包含设备选择逻辑
- 混合了数据处理和 UI 展示
- 组件内部逻辑复杂

### 1.3 图片管理组件复杂
**问题**：图片相关组件承担多重职责
**具体表现**：
- 同时处理上传、预览、管理
- 包含拖拽逻辑和状态管理
- 混合了 UI 交互和业务逻辑

### 1.4 模态框组件职责混乱
**问题**：模态框组件内部逻辑复杂
**具体表现**：
- 同时处理显示逻辑和业务逻辑
- 包含表单验证和数据处理
- UI 状态和业务状态混合

## 🎯 解决方案

### 方案 1：页面组件拆分

#### 1.1 拆分主页面组件
```typescript
// app/page.tsx - 重构后的主页面
export default function HomePage() {
    return (
        <>
            <AppLayout>
                <MainContent />
            </AppLayout>
            <ModalProvider>
                <GlobalModals />
            </ModalProvider>
        </>
    )
}

// app/components/layout/AppLayout.tsx
interface AppLayoutProps {
    children: React.ReactNode
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
    const isMobile = useIsMobile()
    
    return (
        <main 
            id="app-main" 
            className={`app-main ${isMobile ? 'shots-mobile-ui' : ''}`}
        >
            <TopBar />
            {children}
        </main>
    )
}

// app/components/layout/MainContent.tsx
export const MainContent: React.FC = () => {
    const isMobile = useIsMobile()
    
    return isMobile ? <MobileContent /> : <DesktopContent />
}

// app/components/layout/DesktopContent.tsx
export const DesktopContent: React.FC = () => {
    return (
        <div className="desktop-layout">
            <LeftPanel />
            <CenterCanvas />
            <RightPanel />
        </div>
    )
}

// app/components/layout/MobileContent.tsx
export const MobileContent: React.FC = () => {
    return (
        <div className="mobile-layout">
            <MobileCanvas />
            <MobileControls />
        </div>
    )
}
```

#### 1.2 模态框管理拆分
```typescript
// app/components/modals/ModalProvider.tsx
export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
        <ModalContext.Provider value={modalContextValue}>
            {children}
        </ModalContext.Provider>
    )
}

// app/components/modals/GlobalModals.tsx
export const GlobalModals: React.FC = () => {
    return (
        <div id="modal_root">
            <MediaPickerModal />
            <ColorPickerModal />
            <ExportModal />
            <FrameSceneModal />
            <MockupModal />
        </div>
    )
}

// app/components/modals/MediaPickerModal.tsx
export const MediaPickerModal: React.FC = () => {
    const { isOpen, close } = useModal('mediaPickerModal')
    
    if (!isOpen) return null
    
    return (
        <Modal onClose={close}>
            <MediaPickerContent />
        </Modal>
    )
}
```

### 方案 2：业务组件拆分

#### 2.1 DisplayContainer 拆分
```typescript
// app/components/canvas/CanvasRenderer.tsx - 纯渲染组件
interface CanvasRendererProps {
    layout: LayoutConfig
    dimensions: Dimensions
    images: Record<number, string>
}

export const CanvasRenderer: React.FC<CanvasRendererProps> = ({
    layout,
    dimensions,
    images
}) => {
    return (
        <div className="canvas-renderer" style={dimensions}>
            {layout.devices.map((device, index) => (
                <DeviceRenderer
                    key={index}
                    device={device}
                    image={images[index]}
                    index={index}
                />
            ))}
        </div>
    )
}

// app/components/canvas/CanvasContainer.tsx - 容器组件
export const CanvasContainer: React.FC = () => {
    const layout = useActiveLayout()
    const dimensions = useCanvasDimensions()
    const images = useDeviceImages()
    
    return (
        <div className="canvas-container">
            <CanvasRenderer
                layout={layout}
                dimensions={dimensions}
                images={images}
            />
        </div>
    )
}

// app/components/canvas/DeviceRenderer.tsx - 设备渲染组件
interface DeviceRendererProps {
    device: DeviceConfig
    image?: string
    index: number
}

export const DeviceRenderer: React.FC<DeviceRendererProps> = ({
    device,
    image,
    index
}) => {
    const handleClick = useDeviceClick(index)
    
    return (
        <div 
            className="device-renderer"
            style={device.style}
            onClick={handleClick}
        >
            {image && <img src={image} alt={`Device ${index}`} />}
        </div>
    )
}
```

#### 2.2 图片管理组件拆分
```typescript
// app/features/image-management/ImageManager.tsx - 主管理组件
export const ImageManager: React.FC = () => {
    return (
        <div className="image-manager">
            <ImageUploader />
            <ImageList />
            <ImagePreview />
        </div>
    )
}

// app/features/image-management/ImageUploader.tsx - 上传组件
export const ImageUploader: React.FC = () => {
    const { processImages, isProcessing } = useImageProcessor()
    
    const handleDrop = useCallback(async (files: File[]) => {
        await processImages(files)
    }, [processImages])
    
    return (
        <DropZone onDrop={handleDrop} disabled={isProcessing}>
            <UploadButton />
        </DropZone>
    )
}

// app/features/image-management/ImageList.tsx - 列表组件
export const ImageList: React.FC = () => {
    const images = useImages()
    
    return (
        <div className="image-list">
            {images.map(image => (
                <ImageItem key={image.id} image={image} />
            ))}
        </div>
    )
}

// app/features/image-management/ImageItem.tsx - 单个图片组件
interface ImageItemProps {
    image: ImageItem
}

export const ImageItem: React.FC<ImageItemProps> = ({ image }) => {
    const { selectImage, removeImage } = useImageActions()
    
    return (
        <div className="image-item">
            <img src={image.preview} alt={image.file.name} />
            <ImageItemActions
                onSelect={() => selectImage(image.id)}
                onRemove={() => removeImage(image.id)}
            />
        </div>
    )
}
```

### 方案 3：自定义 Hooks 提取逻辑

#### 3.1 页面逻辑 Hooks
```typescript
// app/hooks/usePageState.ts
export const usePageState = () => {
    const isMobile = useIsMobile()
    const activeTab = useActiveTab()
    const modals = useModals()
    
    return {
        isMobile,
        activeTab,
        modals,
    }
}

// app/hooks/useDeviceClick.ts
export const useDeviceClick = (deviceIndex: number) => {
    const { selectDevice, openMediaPicker } = useDeviceActions()
    const deviceImage = useDeviceImage(deviceIndex)
    
    return useCallback(() => {
        selectDevice(deviceIndex)
        
        if (deviceImage) {
            // 有图片时打开图片管理
            openMediaPicker()
        } else {
            // 无图片时打开上传
            openMediaPicker()
        }
    }, [deviceIndex, deviceImage, selectDevice, openMediaPicker])
}
```

#### 3.2 业务逻辑 Hooks
```typescript
// app/hooks/useImageActions.ts
export const useImageActions = () => {
    const store = useAppStore()
    
    const selectImage = useCallback((imageId: string) => {
        store.selectImage(imageId)
    }, [store])
    
    const removeImage = useCallback((imageId: string) => {
        store.removeImage(imageId)
    }, [store])
    
    const bindImageToDevice = useCallback((imageId: string, deviceIndex: number) => {
        store.bindImageToDevice(imageId, deviceIndex)
    }, [store])
    
    return {
        selectImage,
        removeImage,
        bindImageToDevice,
    }
}
```

## 📋 执行步骤

### 步骤 1：分析巨型组件
- [ ] 识别所有超过 200 行的组件
- [ ] 分析组件的职责和功能
- [ ] 确定拆分的优先级
- [ ] 制定拆分计划

### 步骤 2：设计组件架构
- [ ] 设计新的组件层次结构
- [ ] 定义组件间的接口
- [ ] 规划状态和逻辑的分布
- [ ] 设计自定义 hooks

### 步骤 3：实现基础组件
- [ ] 创建布局组件
- [ ] 创建容器组件
- [ ] 创建展示组件
- [ ] 实现自定义 hooks

### 步骤 4：重构巨型组件
- [ ] 拆分 page.tsx 组件
- [ ] 拆分 DisplayContainer 组件
- [ ] 拆分图片管理组件
- [ ] 拆分模态框组件

### 步骤 5：测试和优化
- [ ] 测试拆分后的组件功能
- [ ] 优化组件性能
- [ ] 检查代码复用性
- [ ] 验证可维护性提升

## ⚠️ 注意事项

1. **保持功能完整**：拆分过程中确保功能不受影响
2. **合理的抽象层次**：避免过度拆分导致复杂性增加
3. **性能考虑**：确保拆分不会影响渲染性能
4. **类型安全**：保持完整的 TypeScript 类型定义
5. **测试覆盖**：为拆分后的组件添加测试

## ✅ 验收标准

- [ ] 单个组件代码行数控制在 150 行以内
- [ ] 组件职责单一明确
- [ ] 组件可复用性提升
- [ ] 代码可维护性显著改善
- [ ] 所有功能正常工作
- [ ] 性能没有明显下降

## 📊 预期收益

- **可维护性**：组件职责清晰，易于理解和修改
- **可测试性**：小组件更容易编写和维护测试
- **可复用性**：拆分后的组件可以在其他地方复用
- **开发效率**：团队成员可以并行开发不同组件
- **调试效率**：问题定位更加精确

## 📝 完成记录

**开始时间**：_待填写_
**完成时间**：_待填写_
**执行人员**：_待填写_
**遇到问题**：_待填写_
**解决方案**：_待填写_
