# 🚀 第一步：修正版详细执行计划

## 📋 修正说明

根据你的要求：
1. **使用驼峰命名**：不使用 "-" 而是使用驼峰形式
2. **保留关键字**：保留 "Public"、"Pc"、"Frame"、"Mockup" 等关键字
3. **补充代码修改**：详细说明每个文件移动后需要修改的代码内容

## 🗂️ 修正后的目录结构

```
app/
├── components/
│   ├── ui/                     # 基础UI组件
│   ├── layout/                 # 布局组件
│   │   ├── mobileMockupLayout.tsx
│   │   ├── mobileMockupTabs.tsx
│   │   ├── publicMockupSmallLayout.tsx
│   │   └── publicPanelTabs.tsx
│   └── business/               # 业务组件
│       ├── pcMockupMediaSelector.tsx
│       ├── pcMockupVisibilityControl.tsx
│       ├── pcRightSlider.tsx
│       ├── publicMockupDetails.tsx
│       ├── publicMockupStyle.tsx
│       ├── publicMockupShadow.tsx
│       └── modals/
│           ├── pcFrameEffectsModalDefault.tsx
│           ├── pcFrameEffectsModalPortrait.tsx
│           ├── pcFrameEffectsModalVef.tsx
│           ├── pcFrameShapesItemsModal.tsx
│           ├── pcFrameWaterMarkModal.tsx
│           ├── publicFrameColorPickerModal.tsx
│           ├── publicFrameCustomImageModal.tsx
│           ├── publicFrameSizeModal.tsx
│           ├── publicMediaPickerModal.tsx
│           └── publicMockupModal.tsx
├── features/
│   ├── imageManagement/        # 图片管理模块
│   ├── colorExtraction/        # 颜色提取模块
│   ├── deviceMockup/           # 设备模拟模块
│   │   ├── publicMockupModelSelector.tsx
│   │   ├── publicMockupSizeSelector.tsx
│   │   ├── publicFrameScene.tsx
│   │   └── publicFrameCustomView.tsx
│   ├── canvasRendering/        # 画布渲染模块
│   └── exportSystem/           # 导出系统模块
└── shared/
    ├── types/
    ├── utils/
    ├── hooks/
    └── config/
```

## 🔄 第四阶段：组件移动和代码修改

### 移动布局组件并修改代码

#### 1. MobileMockup_Layout.tsx → mobileMockupLayout.tsx
```bash
mv app/MobileMockup_Layout.tsx app/components/layout/mobileMockupLayout.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/components/layout/mobileMockupLayout.tsx
// 修改导入路径
import { useAppState } from '@/shared/hooks'
import { ColorTypes } from '@/shared/types'
// 其他导入路径也需要相应更新

// 组件名保持不变，但导出时使用新名称
export { MobileMockupLayout as default }
```

#### 2. MobileMockup_Tabs.tsx → mobileMockupTabs.tsx
```bash
mv app/MobileMockup_Tabs.tsx app/components/layout/mobileMockupTabs.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/components/layout/mobileMockupTabs.tsx
// 更新导入路径
import { useAppState } from '@/shared/hooks'
// 更新样式导入
import '@/styles/components/mobileTabs.scss'

export { MobileMockupTabs as default }
```

### 移动业务组件并修改代码

#### 3. PcMockup_Media.tsx → pcMockupMediaSelector.tsx
```bash
mv app/PcMockup_Media.tsx app/components/business/pcMockupMediaSelector.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/components/business/pcMockupMediaSelector.tsx
// 更新所有导入路径
import { useAppState } from '@/shared/hooks'
import { imageExport } from '@/shared/utils'
import { ColorTypes } from '@/shared/types'

// 更新组件内部的相对路径引用
// 如果有引用其他组件，需要更新路径
import { PublicMediaPickerModal } from './modals/publicMediaPickerModal'

export { PcMockupMedia as default }
```

#### 4. PcMockup_Visibility.tsx → pcMockupVisibilityControl.tsx
```bash
mv app/PcMockup_Visibility.tsx app/components/business/pcMockupVisibilityControl.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/components/business/pcMockupVisibilityControl.tsx
import { useAppState } from '@/shared/hooks'
import { useMockupStore } from '@/shared/hooks'

export { PcMockupVisibility as default }
```

### 移动模态框组件并修改代码

#### 5. PcFrame_Effects__ModalDefault.tsx → pcFrameEffectsModalDefault.tsx
```bash
mv app/PcFrame_Effects__ModalDefault.tsx app/components/business/modals/pcFrameEffectsModalDefault.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/components/business/modals/pcFrameEffectsModalDefault.tsx
// 更新导入路径
import { useAppState } from '@/shared/hooks'
import { Modal } from '@/components/ui/Modal'
import { ColorTypes } from '@/shared/types'

// 更新样式导入
import '@/styles/components/modals.scss'

export { PcFrameEffectsModalDefault as default }
```

#### 6. Public_MediaPickerModal.tsx → publicMediaPickerModal.tsx
```bash
mv app/Public_MediaPickerModal.tsx app/components/business/modals/publicMediaPickerModal.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/components/business/modals/publicMediaPickerModal.tsx
// 更新导入路径
import { useAppState } from '@/shared/hooks'
import { ImageManagement } from '@/features/imageManagement'

export { PublicMediaPickerModal as default }
```

### 移动设备模拟组件并修改代码

#### 7. Public_MockUp_ModelSelector.tsx → publicMockupModelSelector.tsx
```bash
mv app/Public_MockUp_ModelSelector.tsx app/features/deviceMockup/publicMockupModelSelector.tsx
```

**需要修改的代码内容：**
```typescript
// 文件：app/features/deviceMockup/publicMockupModelSelector.tsx
// 更新导入路径
import { useAppState } from '@/shared/hooks'
import { useMockupStore } from '@/shared/hooks'
import { DeviceTypes } from '@/shared/types'

export { PublicMockUpModelSelector as default }
```

## 📝 主要文件的导入路径更新

### app/page.tsx 主页面文件修改
```typescript
// 原来的导入
import MobileMockupLayout from './MobileMockup_Layout'
import PcMockupMedia from './PcMockup_Media'
import PublicMediaPickerModal from './Public_MediaPickerModal'

// 修改后的导入
import MobileMockupLayout from '@/components/layout/mobileMockupLayout'
import PcMockupMediaSelector from '@/components/business/pcMockupMediaSelector'
import PublicMediaPickerModal from '@/components/business/modals/publicMediaPickerModal'

// 其他所有组件导入都需要相应更新
```

### app/layout.tsx 布局文件修改
```typescript
// 更新样式导入
import '@/styles/globals/globals.scss'

// 更新其他导入路径
import { TopBar } from '@/components/layout/topBar'
```

## 🔧 批量更新导入路径的脚本

创建一个脚本来批量更新导入路径：

### scripts/updateImports.js
```javascript
const fs = require('fs')
const path = require('path')
const glob = require('glob')

// 定义路径映射
const pathMappings = {
  // 布局组件
  './MobileMockup_Layout': '@/components/layout/mobileMockupLayout',
  './MobileMockup_Tabs': '@/components/layout/mobileMockupTabs',
  './Public_MockupSmallLayout': '@/components/layout/publicMockupSmallLayout',
  './Public_PanelTabs': '@/components/layout/publicPanelTabs',
  
  // 业务组件
  './PcMockup_Media': '@/components/business/pcMockupMediaSelector',
  './PcMockup_Visibility': '@/components/business/pcMockupVisibilityControl',
  './PcRightSlider': '@/components/business/pcRightSlider',
  
  // 模态框组件
  './Public_MediaPickerModal': '@/components/business/modals/publicMediaPickerModal',
  './PcFrame_Effects__ModalDefault': '@/components/business/modals/pcFrameEffectsModalDefault',
  
  // 设备模拟组件
  './Public_MockUp_ModelSelector': '@/features/deviceMockup/publicMockupModelSelector',
  
  // 共享资源
  '../hooks/': '@/shared/hooks/',
  '../utils/': '@/shared/utils/',
  '../types/': '@/shared/types/',
  './hooks/': '@/shared/hooks/',
  './utils/': '@/shared/utils/',
  './types/': '@/shared/types/',
}

// 批量更新文件
function updateImportsInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let updated = false
  
  Object.entries(pathMappings).forEach(([oldPath, newPath]) => {
    const regex = new RegExp(`from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g')
    if (content.match(regex)) {
      content = content.replace(regex, `from '${newPath}'`)
      updated = true
    }
  })
  
  if (updated) {
    fs.writeFileSync(filePath, content)
    console.log(`Updated: ${filePath}`)
  }
}

// 扫描所有 TypeScript 文件
glob('app/**/*.{ts,tsx}', (err, files) => {
  if (err) throw err
  files.forEach(updateImportsInFile)
})
```

## ✅ 详细验证步骤

### 1. 每个文件移动后的验证
```bash
# 移动文件后立即检查
npx tsc --noEmit --project tsconfig.json

# 如果有错误，立即修复导入路径
```

### 2. 组件功能验证
```bash
# 启动开发服务器
npm run dev

# 逐个测试每个移动的组件：
# - 点击相关按钮
# - 打开相关模态框
# - 验证功能正常
```

### 3. 构建验证
```bash
# 确保构建成功
npm run build
```

## 🚨 详细回滚计划

如果某个组件移动后出现问题：

### 单个文件回滚
```bash
# 回滚单个文件
git checkout HEAD -- app/components/layout/mobileMockupLayout.tsx
git mv app/components/layout/mobileMockupLayout.tsx app/MobileMockup_Layout.tsx
```

### 完整回滚
```bash
# 完整回滚到开始状态
git reset --hard HEAD
git clean -fd
```

## 📋 执行检查清单（详细版）

- [ ] **阶段1：准备工作**
  - [ ] 创建备份分支
  - [ ] 创建新目录结构
  - [ ] 准备导入路径更新脚本

- [ ] **阶段2：移动布局组件**
  - [ ] 移动 MobileMockup_Layout.tsx
  - [ ] 更新该文件内的导入路径
  - [ ] 测试相关功能
  - [ ] 移动 MobileMockup_Tabs.tsx
  - [ ] 更新该文件内的导入路径
  - [ ] 测试相关功能

- [ ] **阶段3：移动业务组件**
  - [ ] 移动 PcMockup_Media.tsx
  - [ ] 更新该文件内的导入路径
  - [ ] 更新引用该组件的文件
  - [ ] 测试媒体选择功能

- [ ] **阶段4：移动模态框组件**
  - [ ] 移动所有模态框组件
  - [ ] 更新每个文件的导入路径
  - [ ] 更新引用这些组件的文件
  - [ ] 测试每个模态框的打开/关闭

- [ ] **阶段5：更新主要文件**
  - [ ] 更新 app/page.tsx 的所有导入
  - [ ] 更新 app/layout.tsx 的导入
  - [ ] 运行导入路径更新脚本
  - [ ] 全面功能测试

- [ ] **阶段6：最终验证**
  - [ ] TypeScript 编译检查
  - [ ] ESLint 检查
  - [ ] 完整功能测试
  - [ ] 构建测试
  - [ ] 提交变更

这样的详细计划确保每个文件移动后都有对应的代码修改说明，你觉得这个修正版本如何？
