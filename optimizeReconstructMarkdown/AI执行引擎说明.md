# 🤖 AI执行引擎说明

## 🎯 概述

这个文档说明AI如何解析`执行控制器.md`文件中的指令，并自动执行相应的重构操作。

## 🔍 指令解析逻辑

### 指令格式识别
AI会扫描`执行控制器.md`文件中的以下指令格式：

```bash
# 执行指令格式
EXECUTE: <步骤名称>           # 开始执行指定步骤
CONTINUE: <步骤名称-阶段>     # 继续执行指定阶段
ROLLBACK: <回滚类型>         # 执行回滚操作
COMPLETED: <步骤名称>        # 标记步骤完成
CHECK: <检查类型>            # 执行状态检查
```

### 指令优先级
1. **ROLLBACK** - 最高优先级，立即执行回滚
2. **CHECK** - 状态检查，用于诊断问题
3. **CONTINUE** - 继续中断的执行
4. **EXECUTE** - 开始新的执行
5. **COMPLETED** - 标记完成状态

## 🚀 执行流程

### 1. EXECUTE指令处理

当检测到`EXECUTE: 第一步文件结构重构`时：

```typescript
// AI执行逻辑伪代码
async function executeStep(stepName: string) {
    // 1. 验证前置条件
    await checkPrerequisites()
    
    // 2. 创建Git备份
    await createGitBackup(stepName)
    
    // 3. 加载执行计划
    const plan = await loadExecutionPlan(stepName)
    
    // 4. 分阶段执行
    for (const stage of plan.stages) {
        await executeStage(stage)
        await validateStage(stage)
        await createCheckpoint(stage)
    }
    
    // 5. 更新执行记录
    await updateExecutionLog(stepName, 'COMPLETED')
}
```

### 2. CONTINUE指令处理

当检测到`CONTINUE: 第一步文件结构重构-阶段3-共享资源移动`时：

```typescript
async function continueExecution(stepName: string, stageName: string) {
    // 1. 检查当前状态
    const currentState = await getCurrentState()
    
    // 2. 验证可以继续执行
    if (!canContinueFrom(stageName)) {
        throw new Error('无法从指定阶段继续执行')
    }
    
    // 3. 从指定阶段开始执行
    const plan = await loadExecutionPlan(stepName)
    const startIndex = plan.stages.findIndex(s => s.name === stageName)
    
    for (let i = startIndex; i < plan.stages.length; i++) {
        await executeStage(plan.stages[i])
        await validateStage(plan.stages[i])
        await createCheckpoint(plan.stages[i])
    }
}
```

### 3. ROLLBACK指令处理

当检测到`ROLLBACK: 回滚到上一个阶段`时：

```typescript
async function executeRollback(rollbackType: string) {
    switch (rollbackType) {
        case '回滚到上一个阶段':
            await rollbackToPreviousStage()
            break
        case '回滚到重构前状态':
            await rollbackToTag('v重构前稳定版')
            break
        case '紧急回滚到主分支':
            await emergencyRollbackToMain()
            break
    }
    
    // 更新执行记录
    await updateExecutionLog('ROLLBACK', rollbackType)
}
```

## 📋 具体执行映射

### 第一步文件结构重构执行映射

```typescript
const 第一步执行计划 = {
    name: '第一步文件结构重构',
    documentPath: '执行步骤/第一步文件结构重构/',
    stages: [
        {
            name: '阶段1-根目录清理',
            actions: [
                'createDirectories',
                'moveDocumentFiles',
                'moveScriptFiles',
                'updatePackageJson'
            ],
            validation: ['checkTypeScript', 'checkFileStructure']
        },
        {
            name: '阶段2-样式文件移动',
            actions: [
                'moveStyleFiles',
                'updateStyleImports'
            ],
            validation: ['checkTypeScript', 'checkStyleImports']
        },
        {
            name: '阶段3-共享资源移动',
            actions: [
                'moveUtilFiles',
                'moveTypeFiles',
                'moveHookFiles',
                'createIndexFiles'
            ],
            validation: ['checkTypeScript', 'checkImports']
        },
        // ... 其他阶段
    ]
}
```

### Git操作映射

```typescript
const GitOperations = {
    createBackup: async (stepName: string) => {
        await execCommand('git add .')
        await execCommand(`git commit -m "备份：开始${stepName}"`)
        await execCommand(`git tag backup-${Date.now()}-${stepName}`)
    },
    
    createCheckpoint: async (stageName: string) => {
        await execCommand('git add .')
        await execCommand(`git commit -m "检查点：完成${stageName}"`)
    },
    
    rollbackToTag: async (tagName: string) => {
        await execCommand(`git reset --hard ${tagName}`)
        await execCommand('git clean -fd')
    }
}
```

## 🔧 文件操作映射

### 文件移动操作

```typescript
const FileOperations = {
    moveFile: async (from: string, to: string) => {
        await ensureDirectoryExists(path.dirname(to))
        await moveFile(from, to)
        console.log(`✅ 移动文件: ${from} → ${to}`)
    },
    
    updateImports: async (filePath: string, importMappings: Record<string, string>) => {
        let content = await readFile(filePath)
        
        Object.entries(importMappings).forEach(([oldImport, newImport]) => {
            content = content.replace(
                new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
                newImport
            )
        })
        
        await writeFile(filePath, content)
        console.log(`✅ 更新导入路径: ${filePath}`)
    }
}
```

## 📊 状态跟踪

### 执行状态记录

```typescript
interface ExecutionState {
    currentStep: string
    currentStage: string
    completedStages: string[]
    lastCheckpoint: string
    gitCommits: string[]
    errors: string[]
    startTime: Date
    lastUpdateTime: Date
}

// 状态持久化
const saveExecutionState = async (state: ExecutionState) => {
    const stateFile = 'optimizeReconstructMarkdown/执行状态.json'
    await writeFile(stateFile, JSON.stringify(state, null, 2))
}
```

### 自动更新执行控制器

```typescript
const updateExecutionController = async (updates: {
    currentStage?: string
    currentStep?: string
    executionLog?: string[]
    gitLog?: string[]
}) => {
    const controllerPath = 'optimizeReconstructMarkdown/执行控制器.md'
    let content = await readFile(controllerPath)
    
    // 更新当前阶段
    if (updates.currentStage) {
        content = content.replace(
            /### 🔄 当前阶段：.*/,
            `### 🔄 当前阶段：${updates.currentStage}`
        )
    }
    
    // 更新执行日志
    if (updates.executionLog) {
        const logSection = updates.executionLog.join('\n')
        content = content.replace(
            /### 执行日志\n```\n[\s\S]*?\n```/,
            `### 执行日志\n\`\`\`\n${logSection}\n\`\`\``
        )
    }
    
    await writeFile(controllerPath, content)
}
```

## 🔍 验证和检查

### 自动验证流程

```typescript
const ValidationChecks = {
    checkTypeScript: async () => {
        const result = await execCommand('npx tsc --noEmit')
        return result.exitCode === 0
    },
    
    checkAppStart: async () => {
        try {
            await execCommand('timeout 30s npm run dev', { timeout: 35000 })
            return true
        } catch {
            return false
        }
    },
    
    checkFileStructure: async () => {
        const requiredDirs = [
            'app/components/ui',
            'app/components/layout',
            'app/components/business',
            'app/features',
            'app/shared'
        ]
        
        return requiredDirs.every(dir => existsSync(dir))
    }
}
```

## 📝 使用流程

1. **用户编辑执行控制器**：取消注释相应的执行指令
2. **发送给AI**：将`执行控制器.md`文件发送给AI
3. **AI解析指令**：AI自动识别和解析指令
4. **执行操作**：AI按照预定义的逻辑执行操作
5. **更新状态**：AI自动更新执行记录和状态
6. **返回结果**：AI提供详细的执行报告

## 🚨 错误处理

### 执行失败处理

```typescript
const handleExecutionError = async (error: Error, context: ExecutionContext) => {
    // 1. 记录错误
    console.error(`❌ 执行失败: ${error.message}`)
    
    // 2. 自动回滚到最近的检查点
    await rollbackToLastCheckpoint()
    
    // 3. 更新执行记录
    await updateExecutionLog('ERROR', error.message)
    
    // 4. 提供修复建议
    const suggestions = generateFixSuggestions(error, context)
    return { success: false, error: error.message, suggestions }
}
```

这个AI执行引擎确保了自动化、安全和可控的重构执行过程。
